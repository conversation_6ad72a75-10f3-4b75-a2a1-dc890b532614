{"name": "@ky/fe", "version": "0.2.16", "lockfileVersion": 1, "requires": true, "dependencies": {"@types/color-name": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/color-name/download/@types/color-name-1.1.1.tgz", "integrity": "sha1-HBJhu+qhCoBVu8XYq4S3sq/IRqA="}, "ansi-escapes": {"version": "4.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-escapes/download/ansi-escapes-4.3.1.tgz", "integrity": "sha1-pcR8xDGB8fOP/XB2g3cA05VSKmE=", "requires": {"type-fest": "^0.11.0"}}, "ansi-regex": {"version": "5.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-regex/download/ansi-regex-5.0.0.tgz", "integrity": "sha1-OIU59VF5vzkznIGvMKZU1p+Hy3U="}, "ansi-styles": {"version": "4.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.2.1.tgz", "integrity": "sha1-kK51xCTQCNJiTFvynq0xd+v881k=", "requires": {"@types/color-name": "^1.1.1", "color-convert": "^2.0.1"}}, "balanced-match": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/balanced-match/download/balanced-match-1.0.0.tgz", "integrity": "sha1-ibTRmasr7kneFk6gK4nORi1xt2c="}, "brace-expansion": {"version": "1.1.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/brace-expansion/download/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "chalk": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-3.0.0.tgz", "integrity": "sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "chardet": {"version": "0.7.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chardet/download/chardet-0.7.0.tgz", "integrity": "sha1-kAlISfCTfy7twkJdDSip5fDLrZ4="}, "cli-cursor": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cli-cursor/download/cli-cursor-3.1.0.tgz", "integrity": "sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=", "requires": {"restore-cursor": "^3.1.0"}}, "cli-width": {"version": "2.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cli-width/download/cli-width-2.2.1.tgz", "integrity": "sha1-sEM9C06chH7xiGik7xb9X8gnHEg="}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI="}, "commander": {"version": "4.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/commander/download/commander-4.1.1.tgz", "integrity": "sha1-n9YCvZNilOnp70aj9NaWQESxgGg="}, "concat-map": {"version": "0.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/concat-map/download/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="}, "config": {"version": "3.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/config/download/config-3.3.1.tgz", "integrity": "sha1-tqcOKQikO5jtIL5+Nn7fDMjtWhk=", "requires": {"json5": "^2.1.1"}}, "cross-spawn": {"version": "7.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cross-spawn/download/cross-spawn-7.0.3.tgz", "integrity": "sha1-9zqFudXUHQRVUcF34ogtSshXKKY=", "requires": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}}, "emoji-regex": {"version": "8.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/emoji-regex/download/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc="}, "escape-string-regexp": {"version": "1.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="}, "external-editor": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/external-editor/download/external-editor-3.1.0.tgz", "integrity": "sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=", "requires": {"chardet": "^0.7.0", "iconv-lite": "^0.4.24", "tmp": "^0.0.33"}}, "figures": {"version": "3.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/figures/download/figures-3.2.0.tgz", "integrity": "sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=", "requires": {"escape-string-regexp": "^1.0.5"}}, "fs.realpath": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fs.realpath/download/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="}, "glob": {"version": "7.1.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/glob/download/glob-7.1.6.tgz", "integrity": "sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY=", "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "graceful-fs": {"version": "4.2.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/graceful-fs/download/graceful-fs-4.2.4.tgz", "integrity": "sha1-Ila94U02MpWMRl68ltxGfKB6Kfs=", "optional": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s="}, "iconv-lite": {"version": "0.4.24", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/iconv-lite/download/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "inflight": {"version": "1.0.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/inflight/download/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/inherits/download/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="}, "inquirer": {"version": "7.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/inquirer/download/inquirer-7.1.0.tgz", "integrity": "sha1-EpigGFmIPhfHJkuChwrhA0+S3Sk=", "requires": {"ansi-escapes": "^4.2.1", "chalk": "^3.0.0", "cli-cursor": "^3.1.0", "cli-width": "^2.0.0", "external-editor": "^3.0.3", "figures": "^3.0.0", "lodash": "^4.17.15", "mute-stream": "0.0.8", "run-async": "^2.4.0", "rxjs": "^6.5.3", "string-width": "^4.1.0", "strip-ansi": "^6.0.0", "through": "^2.3.6"}}, "interpret": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/interpret/download/interpret-1.2.0.tgz", "integrity": "sha1-1QYaYiS+WOgIOYX1AU2EQ1lXYpY="}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0="}, "isexe": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/isexe/download/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="}, "json5": {"version": "2.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/json5/download/json5-2.1.3.tgz", "integrity": "sha1-ybD3+pIzv+WAf+ZvzzpWF+1ZfUM=", "requires": {"minimist": "^1.2.5"}}, "jsonfile": {"version": "6.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jsonfile/download/jsonfile-6.0.1.tgz", "integrity": "sha1-mJZsuiFDeMjIS4LghZB7QL9hQXk=", "requires": {"graceful-fs": "^4.1.6", "universalify": "^1.0.0"}}, "lodash": {"version": "4.17.15", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/lodash/download/lodash-4.17.15.tgz", "integrity": "sha1-tEf2ZwoEVbv+7dETku/zMOoJdUg="}, "mimic-fn": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/mimic-fn/download/mimic-fn-2.1.0.tgz", "integrity": "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs="}, "minimatch": {"version": "3.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/minimatch/download/minimatch-3.0.4.tgz", "integrity": "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=", "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/minimist/download/minimist-1.2.5.tgz", "integrity": "sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI="}, "mute-stream": {"version": "0.0.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/mute-stream/download/mute-stream-0.0.8.tgz", "integrity": "sha1-FjDEKyJR/4HiooPelqVJfqkuXg0="}, "once": {"version": "1.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/once/download/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "requires": {"wrappy": "1"}}, "onetime": {"version": "5.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/onetime/download/onetime-5.1.0.tgz", "integrity": "sha1-//DzyRYX/mK7UBiWNumayKbfe+U=", "requires": {"mimic-fn": "^2.1.0"}}, "os-tmpdir": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/os-tmpdir/download/os-tmpdir-1.0.2.tgz", "integrity": "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ="}, "path-is-absolute": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-is-absolute/download/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="}, "path-key": {"version": "3.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-key/download/path-key-3.1.1.tgz", "integrity": "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U="}, "path-parse": {"version": "1.0.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-parse/download/path-parse-1.0.6.tgz", "integrity": "sha1-1i27VnlAXXLEc37FhgDp3c8G0kw="}, "rechoir": {"version": "0.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/rechoir/download/rechoir-0.6.2.tgz", "integrity": "sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=", "requires": {"resolve": "^1.1.6"}}, "resolve": {"version": "1.17.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/resolve/download/resolve-1.17.0.tgz", "integrity": "sha1-sllBtUloIxzC0bt2p5y38sC/hEQ=", "requires": {"path-parse": "^1.0.6"}}, "restore-cursor": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/restore-cursor/download/restore-cursor-3.1.0.tgz", "integrity": "sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=", "requires": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}}, "run-async": {"version": "2.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/run-async/download/run-async-2.4.1.tgz", "integrity": "sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU="}, "rxjs": {"version": "6.5.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/rxjs/download/rxjs-6.5.5.tgz", "integrity": "sha1-xciE4wlMjP7jG/J+uH5UzPyH+ew=", "requires": {"tslib": "^1.9.0"}}, "safer-buffer": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/safer-buffer/download/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="}, "shebang-command": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/shebang-command/download/shebang-command-2.0.0.tgz", "integrity": "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=", "requires": {"shebang-regex": "^3.0.0"}}, "shebang-regex": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/shebang-regex/download/shebang-regex-3.0.0.tgz", "integrity": "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI="}, "shelljs": {"version": "0.8.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/shelljs/download/shelljs-0.8.4.tgz", "integrity": "sha1-3naE/ut2f4cWsyYHiooAh1iQ48I=", "requires": {"glob": "^7.0.0", "interpret": "^1.0.0", "rechoir": "^0.6.2"}}, "signal-exit": {"version": "3.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/signal-exit/download/signal-exit-3.0.3.tgz", "integrity": "sha1-oUEMLt2PB3sItOJTyOrPyvBXRhw="}, "string-width": {"version": "4.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/string-width/download/string-width-4.2.0.tgz", "integrity": "sha1-lSGCxGzHssMT0VluYjmSvRY7crU=", "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.0"}}, "strip-ansi": {"version": "6.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/strip-ansi/download/strip-ansi-6.0.0.tgz", "integrity": "sha1-CxVx3XZpzNTz4G4U7x7tJiJa5TI=", "requires": {"ansi-regex": "^5.0.0"}}, "supports-color": {"version": "7.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.1.0.tgz", "integrity": "sha1-aOMlkd9z4lrRxLSRCKLsUHliv9E=", "requires": {"has-flag": "^4.0.0"}}, "through": {"version": "2.3.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/through/download/through-2.3.8.tgz", "integrity": "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU="}, "tmp": {"version": "0.0.33", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tmp/download/tmp-0.0.33.tgz", "integrity": "sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=", "requires": {"os-tmpdir": "~1.0.2"}}, "tslib": {"version": "1.13.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tslib/download/tslib-1.13.0.tgz", "integrity": "sha1-yIHhPMcBWJTtkUhi0nZDb6mkcEM="}, "type-fest": {"version": "0.11.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/type-fest/download/type-fest-0.11.0.tgz", "integrity": "sha1-l6vwhyMQ/tiKXEZrJWgVdhReM/E="}, "universalify": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/universalify/download/universalify-1.0.0.tgz", "integrity": "sha1-thodoXPoQ1sv48Z9Kbmt+FlL0W0="}, "which": {"version": "2.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/which/download/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "requires": {"isexe": "^2.0.0"}}, "wrappy": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/wrappy/download/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="}}}
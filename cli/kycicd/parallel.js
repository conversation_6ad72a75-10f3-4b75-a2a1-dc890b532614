#! /usr/bin/env node
// const config = require('config');
const prePath = process.cwd();
const spawn = require('cross-spawn');
const fs = require('fs');
const path = require('path');
const shell = require('shelljs');
const inquirer = require('inquirer');
const jsonfile = require('jsonfile');
const program = require('commander');

function absPath(paths) {
  return path.resolve(prePath, paths);
}

// 注册步骤1
const step1 = function() {
  return inquirer.prompt([
    {
      name: 'conf',
      type: 'input',
      message: '请输入测试环境打包命令',
    },
  ]);
};
// 注册步骤2
const step2 = function() {
  return inquirer.prompt([
    {
      name: 'conf',
      type: 'input',
      message: '请输入生产环境打包命令',
    },
  ]);
};

// 注册步骤3
const step3 = function() {
  return inquirer.prompt([
    {
      name: 'conf',
      type: 'input',
      message: '请输入dist目录，不填默认为dist',
    },
  ]);
};

// 注册步骤4
const step4 = function() {
  return inquirer.prompt([
    {
      name: 'conf',
      type: 'confirm',
      message: '是否是多页应用',
    },
  ]);
};
// 注册步骤3
const step5 = function() {
  return inquirer.prompt([
    {
      name: 'conf',
      type: 'input',
      message: '请输入项目在ng的路径(单项目ng请不填)',
    },
  ]);
};
// 注册配置文件
const confFun = function() {
  let confFile = {
    cicdSit: {},
    cicdProd: {},
    ngPath: '',
  };
  return step1()
    .then(answer => {
      confFile.cicdSit.cmd = answer.conf;
      return step2();
    })
    .then(answer => {
      confFile.cicdProd.cmd = answer.conf;
      return step3();
    })
    .then(answer => {
      confFile.cicdSit.dist = answer.conf || 'dist';
      confFile.cicdProd.dist = answer.conf || 'dist';
      return step4();
    })
    .then(answer => {
      confFile.isMulti = answer.conf;
      return step5();
    })
    .then(answer => {
      confFile.ngPath = answer.conf || 'dist';

      console.log(confFile);
      try {
        const isConfigExist = fs.readdirSync(absPath('config'));
      } catch (error) {
        shell.mkdir('config');
      }
      jsonfile.writeFile(absPath('config/default.json'), confFile, function(
        err,
      ) {
        if (err) console.error(err);
      });
    });
};

const buildSit = function(config) {
  return new Promise((res, rej) => {
    const child = spawn(config.cmd[0], config.cmd.slice(1), {
      stdio: 'inherit',
      shell: true,
    });

    // child.stdout.on('data', function (data) {
    //   console.log('stdout: ' + data);
    // });

    // child.stderr.on('data', function (data) {
    //   console.log('stderr: ' + data);
    //   rej(data)
    // });
    child.on('error', err => {
      log.log('err', err);
    });

    child.on('close', function(code) {
      if (code !== 0) {
        process.exit(1);
      }
      console.log('构建测试包完毕 ');
      if (!config.isMulti) {
        shell.mv(
          `${config.dist}/${config.index}`,
          `${config.dist}/${config.indexRename}`,
        );
        shell.cp('-rf', config.dist + '/.', 'cicdDist/' + config.ngPath);
        console.log('cp', config.dist + '/.', 'cicdDist/' + config.ngPath);
      } else {
        shell.cp(
          '-rf',
          config.dist + '/.',
          'cicdDist/' + config.ngPath + '/sit',
        );
        console.log(
          'cp',
          config.dist + '/.',
          'cicdDist/' + config.ngPath + '/sit',
        );
      }
      console.log('移动文件完毕 ');
      try {
        shell.rm('-rf', config.dist);
      } catch (error) {
      }
      res();
    });
  });
};

const buildProd = function(config) {
  return new Promise((res, rej) => {
    const child = spawn(config.cmd[0], [...config.cmd.slice(1), '--', '--output-path', '$(pwd)/' + config.dist], {
      stdio: 'inherit',
      shell: true,
    });

    // child.stdout.on('data', function (data) {
    //   console.log('stdout: ' + data);
    // });

    // child.stderr.on('data', function (data) {
    //   console.log('stderr: ' + data);
    //   rej(data);
    // });
    child.on('error', err => {
      log.log('err', err);
    });
    child.on('close', function(code) {
      if (code !== 0) {
        process.exit(1);
      }
      console.log('构建生产包完毕 ' + code);
      console.log('cp2', config.dist + '/.', 'cicdDist/' + config.ngPath);
      shell.cp('-rf', config.dist + '/.', 'cicdDist/' + config.ngPath);
      console.log('移动文件完毕 ');
      try {
        shell.rm('-rf', config.dist);
      } catch (error) {
      }
      res();
    });
  });
};
// 定义版本和参数选项
program
  .version(require('./package.json').version)
  .option('-i, init', '初始化打包配置')
  .option('-b, build', '同时打测试包和生产包');

program.on('--help', function() {
  console.log('初次使用请运行kycicd init,打包请运行kycicd build');
});

program.parse(process.argv);
if (program.init) {
  confFun();
}
if (program.build) {
  // 获取配置文件
  jsonfile.readFile(absPath('config/default.json'), function(err, config) {
    if (err) {
      console.log('请先运行init命令');
    } else {
      try {
        shell.rm('-rf', 'cicdDist');
      } catch (error) {
      }
      console.log(config);
      const { cicdSit, cicdProd, ngPath, ...rest } = config;
      const sit = {
        cmd: cicdSit.cmd.split(' ').filter(item => item),
        dist: cicdSit.dist || 'dist',
        ngPath: ngPath || 'dist',
        ...rest,
        index: cicdSit.index || 'index.html',
        indexRename: cicdSit.indexRename || 'index.sit.html',
      };
      const prod = {
        cmd: cicdProd.cmd.split(' ').filter(item => item),
        // specify to prodDist 
        dist: 'prodDist',
        ngPath: ngPath || 'dist',
        ...rest,
      };
      console.log('开始并行构建SIT, PROD...')
      shell.mkdir('-p', 'cicdDist');
      Promise.all([buildProd(prod), buildSit(sit)])
        .catch(err => {
          console.log('构建错误', err);
          throw err;
        });
    }
    // console.log(obj)
  });
}

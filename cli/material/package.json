{"name": "@ky/mify", "version": "0.0.20", "description": "", "main": "index.js", "bin": {"mify": "./index.js"}, "scripts": {"eslint": "eslint .", "test": "echo \"Error: no test specified\" && exit 1", "release": "npm version patch && npm publish --registry=https://artifactory.sf-express.com/artifactory/api/npm/npm-sf-local/"}, "keywords": [], "author": "吴晋哲", "license": "ISC", "dependencies": {"@babel/core": "^7.21.8", "@babel/plugin-transform-modules-commonjs": "^7.21.5", "archiver": "^5.3.1", "axios": "^1.4.0", "chalk": "^4.1.2", "clean-webpack-plugin": "^4.0.0", "eslint": "^8.40.0", "eslint-loader": "^4.0.2", "execa": "^5.1.1", "form-data": "^4.0.0", "highlight.js": "^11.8.0", "html-webpack-plugin": "^5.5.1", "inquirer": "^8.2.0", "markdown-it": "^13.0.1", "mini-css-extract-plugin": "^2.7.5", "ora": "^5.4.1", "portfinder": "^1.0.28", "react": "^18.2.0", "react-dom": "^18.2.0", "webpack": "^5.81.0", "webpack-dev-server": "^4.13.3", "webpack-merge": "^5.8.0", "yargs": "^17.7.1"}, "devDependencies": {"eslint-config-google": "^0.14.0", "eslint-plugin-node": "^11.1.0"}}
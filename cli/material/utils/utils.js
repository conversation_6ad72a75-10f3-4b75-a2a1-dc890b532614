
const path = require('path');
const fs = require('fs');
const execa = require('execa');

module.exports = {
  // // 查找项目的根目录
  // findRoot: function findRoot(startDir) {
  //   const lernaJsonPath = path.join(startDir, 'lerna.json');

  //   if (fs.existsSync(lernaJsonPath)) {
  //     return startDir;
  //   } else {
  //     const parentDir = path.dirname(startDir);
  //     if (parentDir === startDir) {
  //       throw new Error('Could not find lerna.json in any parent directory');
  //     } else {
  //       return findRoot(parentDir);
  //     }
  //   }
  // },

  /** 查找package.json的路径，根路径 */
  findPackageJsonPwd: function findPackageJsonPwd(startDir) {
    if (fs.existsSync(path.resolve(startDir, 'package.json'))) {
      return startDir;
    } else {
      const parentDir = path.dirname(startDir);
      if (parentDir === startDir) {
        return ''
      } else {
        return findPackageJsonPwd(parentDir);
      }
    }
  },

  // 读取json文件
  readJson: function readJson(pwd) {
    const data = fs.readFileSync(pwd, 'utf8');

    return JSON.parse(data);
  },

  /** 项目的构建 */
  build(pwd) {
    const packageJson = require(path.resolve(pwd, 'package.json'));

    if (packageJson.nobuild || !packageJson.scripts || !packageJson.scripts.build) return Promise.resolve();

    return execa('npm', ['run', 'build', '--prefix', pwd]).catch((err) => {
      return Promise.reject(err);
    });
  },

  /** 从某个版本中获取可能更新的三个版本 */
  getUpdateVersion: function getUpdateVersion(v) {
    const [v1, v2, v3] = v.split('.');

    return [
      `${Number(v1) + 1}.0.0`,
      `${v1}.${Number(v2) + 1}.0`,
      `${v1}.${v2}.${Number(v3) + 1}`,
    ]
  },

  /** 更新版本 */
  version: function version(pwd, type) {
    let versionType = 'patch';

    if (type === 0) {
      versionType = 'major';
    } else if (type === 1) {
      versionType = 'minor';
    }

    return execa('npm', ['version', versionType, '--prefix', pwd]);
  },

  publish: function publish(pwd, registry = 'https://artifactory.sf-express.com/artifactory/api/npm/npm-sf-local/') {
    return execa('npm', ['publish', pwd, '--registry', registry]);
  },

  /** 合并builder
   * @param {Object} builder 通用的builder
   * @param {Object} argv 需要合并的builder
   * @return {Object} 合并后的builder
   */
  builderMerge: function builderMerge(builder, argv = {}) {
    const newBuilder = {...builder};

    for (const key in argv) {
      if (argv[key] === false && newBuilder[key]) {
        delete newBuilder[key];
      } else if (argv[key] && newBuilder[key]) {
        newBuilder[key] = {
          ...newBuilder[key],
          ...argv[key],
        };
      }
    }

    return newBuilder;
  },
};


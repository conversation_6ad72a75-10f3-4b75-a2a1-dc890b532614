const axios = require('axios')
const methods = ['get', 'post', 'put', 'delete', 'patch']

const axiosProdInstance = axios.create({
  // baseURL: 'http://localhost:8080/fopMaterialServices/'
  baseURL: 'http://material-node.sf-express.com/fopMaterialServices/'
})

const axiosTestInstance = axios.create({
  // baseURL: 'http://localhost:8080/fopMaterialServices/'
  baseURL: 'http://material-node.sit.sf-express.com/fopMaterialServices/'
})

const request = (type, url, method, data, config) => {
  return (type === 'prod' ? axiosProdInstance : axiosTestInstance)({
    url,
    method,
    data,
    ...config
  }).then(res => {
    if (res.data.success) {
      return res.data
    }

    return Promise.reject(new Error(res.data.errorMessage))
  })
}

methods.forEach(method => {
  request[method]
   = (url, data, config) => request(url, method, data, config)
})

module.exports = request
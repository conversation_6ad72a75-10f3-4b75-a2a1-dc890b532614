const fs = require('fs');
const path = require('path');
const { findPackageJsonPwd, readJson } = require('./utils');


const noop = () => {};

// 命令生成器
module.exports = function commandGenerator(config) {
  const builder = config.builder;
  const handler = config.handler || noop;

  return {
    ...config,
    builder: builder,
    handler: (argv) => {
      const rootPath = findPackageJsonPwd(process.cwd());
      let packageJson = readJson(path.resolve(rootPath, 'package.json')) || {};

      // 物料配置的校验
      if (!rootPath || !packageJson.material) {
        return console.log('当前项目不是物料项目')
      } else {
        if (!packageJson.material.cname) {
          return console.log('package.json中 缺少必要字段material.cname')
        } else if (!packageJson.material.component) {
          return console.log('package.json中 缺少必要字段material.component')
        } else if (!packageJson.material.doc) {
          return console.log('package.json中 缺少必要字段material.doc')
        } else if (!packageJson.material.alias) {
          return console.log('package.json中 缺少必要字段material.alias')
        }
      }

      const materialConfig = {
        ...packageJson.material,
        name: packageJson.name,
        version: packageJson.version,
      }

      // // mConfig = require(path.resolve(rootPath, 'm.config.js'))
      // // 将package.json中的name和version赋值到mConfig中，因为m.config.js中不需要填写name和version
      // // 但是可能后续需要用到，
      // mConfig.version = packageJson.version
      // mConfig.name = mConfig.name || packageJson.name

      handler(argv, {
        context: rootPath,
        mConfig: materialConfig
      });
    },
  };
};

const execa = require("execa");

  /** 合入当前分支 */
  function gitMerge(branch) {
    return execa('git', ['merge', branch]).then(res => {
      return { success: true }
    }).catch(err => {
      return { success: false, errMsg: `代码合并失败，请手动合并与${branch}分支，并解决冲突` }
    })
  }

module.exports = {
  /** 两个分支进行对比 */
  async diff (branch1, branch2) {
    const { stdout } = await execa('git', ['diff', '--name-only', branch1, branch2])
    const modifiedFiles = stdout.trim().split('\n');
    const diffDir = []

    modifiedFiles.forEach(item => {
      const [parentDir, childDir = ''] = item.split('/')
      const dirText = `${parentDir}/${childDir}`

      if (childDir && diffDir.indexOf(dirText) === -1) {
        diffDir.push(dirText)
      }
    })

    return diffDir
  },

  /** 是否是干净的（是否有未提交文件） */
  isCleaned() {
    return execa('git', ['status', '-s']).then(res => {
      // 如果有未提交文件，则不是干净的，否则为干净的
      return !res.stdout
    }).catch(err => {
      return Promise.reject(err)
    })
  },

  /** 将本地分支合并远端的feature/master */
  async fetchMaster() {
    let result = await gitMerge('origin/feature/master') // 将本地当前分支合并远端的feature/master
    
    if (!result.success) {
      return result
    }

    result = await gitMerge('origin/master') // 将本地当前分支合并远端的master

    if (!result.success) {
      return result
    }
  },

  /** 拉起远端代码 */
  fetch() {
    return execa('git', ['fetch'])
  },

  /** 获取所有分支 */
  getBranch() {
    const localBranch = []
    const remoteBranch = []
    let currentBranch = ''
  
    return new Promise((resolve, reject) => {
      execa('git', ['branch', '-a']).then(res => {
        if (res.exitCode === 0) {
          const branchs = res.stdout.split('\n')
  
          branchs.forEach((item) => {
            if (/^remotes.+/g.test(item.replace(/\s/g, ''))) {
              // 表示远端的分支
              remoteBranch.push(item.replace(/((remotes\/)|\s)/g, ''))
            } else {
              if (item[0] === '*') {
                // 表示当前分支
                currentBranch = item.replace(/(\*|\s)/g, '')
              }
              // 本地分支
              localBranch.push(item.replace(/(\*|\s)/g, ''))
            }
          })
  
          resolve({
            localBranch,
            remoteBranch,
            currentBranch
          })
        } else {
          reject(res.stderr)
        }
      }).catch(err => {
        reject(err)
      })
    })
  },

  /** 合入当前分支 */
  gitMerge,

  /** merge冲突后，可以回退之前的状态 */
  mergeBack() {
    return execa('git', ['merge', '--abort'])
  }
}
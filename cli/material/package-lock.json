{"name": "@ky/mify", "version": "0.0.19", "lockfileVersion": 1, "requires": true, "dependencies": {"@ampproject/remapping": {"version": "2.2.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@ampproject/remapping/-/remapping-2.2.1.tgz", "integrity": "sha1-mejhGFESi4cCzVfDNoTx0PJgtjA=", "requires": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}}, "@babel/code-frame": {"version": "7.21.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/code-frame/-/code-frame-7.21.4.tgz", "integrity": "sha1-0PqeRBOsqB8rI7lEJ5e9oYJu2zk=", "requires": {"@babel/highlight": "^7.18.6"}}, "@babel/compat-data": {"version": "7.21.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/compat-data/-/compat-data-7.21.7.tgz", "integrity": "sha1-Ycr/tgd25JpXumGojwK+3YcU9rw="}, "@babel/core": {"version": "7.21.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/core/-/core-7.21.8.tgz", "integrity": "sha1-Kox/D1PWAQC6TDJHC6AoHJKqmqQ=", "requires": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.21.4", "@babel/generator": "^7.21.5", "@babel/helper-compilation-targets": "^7.21.5", "@babel/helper-module-transforms": "^7.21.5", "@babel/helpers": "^7.21.5", "@babel/parser": "^7.21.8", "@babel/template": "^7.20.7", "@babel/traverse": "^7.21.5", "@babel/types": "^7.21.5", "convert-source-map": "^1.7.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.2", "semver": "^6.3.0"}, "dependencies": {"debug": {"version": "4.3.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/debug/-/debug-4.3.4.tgz", "integrity": "sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=", "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}}}, "@babel/generator": {"version": "7.21.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/generator/-/generator-7.21.5.tgz", "integrity": "sha1-wMDlRJUEx7fegjbZkzjD4qNAdF8=", "requires": {"@babel/types": "^7.21.5", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17", "jsesc": "^2.5.1"}}, "@babel/helper-compilation-targets": {"version": "7.21.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-compilation-targets/-/helper-compilation-targets-7.21.5.tgz", "integrity": "sha1-Yx5sx4THtmBBdCE0mqwwTJQRU2Y=", "requires": {"@babel/compat-data": "^7.21.5", "@babel/helper-validator-option": "^7.21.0", "browserslist": "^4.21.3", "lru-cache": "^5.1.1", "semver": "^6.3.0"}}, "@babel/helper-environment-visitor": {"version": "7.21.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-environment-visitor/-/helper-environment-visitor-7.21.5.tgz", "integrity": "sha1-x2mv79QdFxg298tj4pW+32idSLo="}, "@babel/helper-function-name": {"version": "7.21.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-function-name/-/helper-function-name-7.21.0.tgz", "integrity": "sha1-1VKCmxDqnxIJaTBAI80GRfoAsbQ=", "requires": {"@babel/template": "^7.20.7", "@babel/types": "^7.21.0"}}, "@babel/helper-hoist-variables": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-hoist-variables/-/helper-hoist-variables-7.18.6.tgz", "integrity": "sha1-1NLI+0uuqlxouZzIJFxWVU+SZng=", "requires": {"@babel/types": "^7.18.6"}}, "@babel/helper-module-imports": {"version": "7.21.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-module-imports/-/helper-module-imports-7.21.4.tgz", "integrity": "sha1-rIiy92CTY3SJ5xipDOxs+KmwKa8=", "requires": {"@babel/types": "^7.21.4"}}, "@babel/helper-module-transforms": {"version": "7.21.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-module-transforms/-/helper-module-transforms-7.21.5.tgz", "integrity": "sha1-2TfILpr2jTGrSQORNqIisXrAtCA=", "requires": {"@babel/helper-environment-visitor": "^7.21.5", "@babel/helper-module-imports": "^7.21.4", "@babel/helper-simple-access": "^7.21.5", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-validator-identifier": "^7.19.1", "@babel/template": "^7.20.7", "@babel/traverse": "^7.21.5", "@babel/types": "^7.21.5"}}, "@babel/helper-plugin-utils": {"version": "7.21.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.21.5.tgz", "integrity": "sha1-NF8jd9BacgpOXs+jnL9EdKTa7VY="}, "@babel/helper-simple-access": {"version": "7.21.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-simple-access/-/helper-simple-access-7.21.5.tgz", "integrity": "sha1-1penlxpcOerDLH5jwJIcBsiiSe4=", "requires": {"@babel/types": "^7.21.5"}}, "@babel/helper-split-export-declaration": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.18.6.tgz", "integrity": "sha1-c2eUm8dbIMbVpdSpe7ooJK6O8HU=", "requires": {"@babel/types": "^7.18.6"}}, "@babel/helper-string-parser": {"version": "7.21.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-string-parser/-/helper-string-parser-7.21.5.tgz", "integrity": "sha1-Kz7qZUQ8a9wxwi0DfGX20yO2sr0="}, "@babel/helper-validator-identifier": {"version": "7.19.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz", "integrity": "sha1-fuqDTPMpAf/cGn7lVeL5wn4knKI="}, "@babel/helper-validator-option": {"version": "7.21.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-validator-option/-/helper-validator-option-7.21.0.tgz", "integrity": "sha1-giTH4TrOS6/cQATaLPBk70JnMYA="}, "@babel/helpers": {"version": "7.21.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helpers/-/helpers-7.21.5.tgz", "integrity": "sha1-W6xm4ITXpNLZaWvfAXWpP3+2PAg=", "requires": {"@babel/template": "^7.20.7", "@babel/traverse": "^7.21.5", "@babel/types": "^7.21.5"}}, "@babel/highlight": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/highlight/-/highlight-7.18.6.tgz", "integrity": "sha1-gRWGAek+JWN5Wty/vfXWS+Py7N8=", "requires": {"@babel/helper-validator-identifier": "^7.18.6", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}, "dependencies": {"ansi-styles": {"version": "3.2.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/chalk/-/chalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "color-convert": {"version": "1.9.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/color-name/-/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="}, "escape-string-regexp": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="}, "has-flag": {"version": "3.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="}, "supports-color": {"version": "5.5.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "requires": {"has-flag": "^3.0.0"}}}}, "@babel/parser": {"version": "7.21.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/parser/-/parser-7.21.8.tgz", "integrity": "sha1-ZCr30DM+q5wK1wsUrF522957/fg="}, "@babel/plugin-transform-modules-commonjs": {"version": "7.21.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.21.5.tgz", "integrity": "sha1-1p+5R+7VGvkd6C5HCPZ2hk5eR7w=", "requires": {"@babel/helper-module-transforms": "^7.21.5", "@babel/helper-plugin-utils": "^7.21.5", "@babel/helper-simple-access": "^7.21.5"}}, "@babel/template": {"version": "7.20.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/template/-/template-7.20.7.tgz", "integrity": "sha1-oVCQwoOag7AqqZbAtJlABYQf1ag=", "requires": {"@babel/code-frame": "^7.18.6", "@babel/parser": "^7.20.7", "@babel/types": "^7.20.7"}}, "@babel/traverse": {"version": "7.21.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/traverse/-/traverse-7.21.5.tgz", "integrity": "sha1-rSI2HTUqUVS0mCmdUjz3KZiksTM=", "requires": {"@babel/code-frame": "^7.21.4", "@babel/generator": "^7.21.5", "@babel/helper-environment-visitor": "^7.21.5", "@babel/helper-function-name": "^7.21.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/parser": "^7.21.5", "@babel/types": "^7.21.5", "debug": "^4.1.0", "globals": "^11.1.0"}, "dependencies": {"debug": {"version": "4.3.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/debug/-/debug-4.3.4.tgz", "integrity": "sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=", "requires": {"ms": "2.1.2"}}, "globals": {"version": "11.12.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/globals/-/globals-11.12.0.tgz", "integrity": "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4="}, "ms": {"version": "2.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}}}, "@babel/types": {"version": "7.21.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/types/-/types-7.21.5.tgz", "integrity": "sha1-GN+9R8OdOQTV2z09wsyAvttg5bY=", "requires": {"@babel/helper-string-parser": "^7.21.5", "@babel/helper-validator-identifier": "^7.19.1", "to-fast-properties": "^2.0.0"}}, "@eslint-community/eslint-utils": {"version": "4.4.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@eslint-community/eslint-utils/-/eslint-utils-4.4.0.tgz", "integrity": "sha1-ojUU6Pua8SadX3eIqlVnmNYca1k=", "requires": {"eslint-visitor-keys": "^3.3.0"}}, "@eslint-community/regexpp": {"version": "4.5.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@eslint-community/regexpp/-/regexpp-4.5.1.tgz", "integrity": "sha1-zdNdzk+hqJpP1CsVmes1s69AiIQ="}, "@eslint/eslintrc": {"version": "2.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@eslint/eslintrc/-/eslintrc-2.0.3.tgz", "integrity": "sha1-SRDbVQX01QPyd3S/NW43BIGKAzE=", "requires": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.5.2", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "dependencies": {"debug": {"version": "4.3.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/debug/-/debug-4.3.4.tgz", "integrity": "sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=", "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}}}, "@eslint/js": {"version": "8.40.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@eslint/js/-/js-8.40.0.tgz", "integrity": "sha1-O6czWeEfWnvT5Af3CzUoq/rmnOw="}, "@humanwhocodes/config-array": {"version": "0.11.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@humanwhocodes/config-array/-/config-array-0.11.8.tgz", "integrity": "sha1-A1lawgdaTcDxkcwhMd4U+9fUELk=", "requires": {"@humanwhocodes/object-schema": "^1.2.1", "debug": "^4.1.1", "minimatch": "^3.0.5"}, "dependencies": {"debug": {"version": "4.3.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/debug/-/debug-4.3.4.tgz", "integrity": "sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=", "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}}}, "@humanwhocodes/module-importer": {"version": "1.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz", "integrity": "sha1-r1smkaIrRL6EewyoFkHF+2rQFyw="}, "@humanwhocodes/object-schema": {"version": "1.2.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz", "integrity": "sha1-tSBSnsIdjllFoYUd/Rwy6U45/0U="}, "@jridgewell/gen-mapping": {"version": "0.3.3", "resolved": "https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.3.3.tgz", "integrity": "sha512-HLhSWOLRi875zjjMG/r+Nv0oCW8umGb0BgEhyX3dDX3egwZtB8PqLnjz3yedt8R5StBrzcg4aBpnh8UA9D1BoQ==", "requires": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.9"}}, "@jridgewell/resolve-uri": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz", "integrity": "sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w=="}, "@jridgewell/set-array": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/@jridgewell/set-array/-/set-array-1.1.2.tgz", "integrity": "sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw=="}, "@jridgewell/source-map": {"version": "0.3.3", "resolved": "https://registry.npmmirror.com/@jridgewell/source-map/-/source-map-0.3.3.tgz", "integrity": "sha512-b+fsZXeLYi9fEULmfBrhxn4IrPlINf8fiNarzTof004v3lFdntdwa9PF7vFJqm3mg7s+ScJMxXaE3Acp1irZcg==", "requires": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}}, "@jridgewell/sourcemap-codec": {"version": "1.4.14", "resolved": "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz", "integrity": "sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw=="}, "@jridgewell/trace-mapping": {"version": "0.3.18", "resolved": "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.18.tgz", "integrity": "sha512-w+niJYzMHdd7USdiH2U6869nqhD2nbfZXND5Yp93qIbEmnDNk7PD48o+YchRVpzMU7M6jVCbenTR7PA1FLQ9pA==", "requires": {"@jridgewell/resolve-uri": "3.1.0", "@jridgewell/sourcemap-codec": "1.4.14"}}, "@leichtgewicht/ip-codec": {"version": "2.0.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@leichtgewicht/ip-codec/-/ip-codec-2.0.4.tgz", "integrity": "sha1-sqxibWy5yHGKtFkWbUu0Bbj/p4s="}, "@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "integrity": "sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=", "requires": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}}, "@nodelib/fs.stat": {"version": "2.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "integrity": "sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos="}, "@nodelib/fs.walk": {"version": "1.2.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "integrity": "sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=", "requires": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}}, "@types/body-parser": {"version": "1.19.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/body-parser/-/body-parser-1.19.2.tgz", "integrity": "sha1-rqIFnii3ZYY5CBNHrE+rPeFm5vA=", "requires": {"@types/connect": "*", "@types/node": "*"}}, "@types/bonjour": {"version": "3.5.10", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/bonjour/-/bonjour-3.5.10.tgz", "integrity": "sha1-D2qt/gDqQU7chvXRBjV82pcB4nU=", "requires": {"@types/node": "*"}}, "@types/connect": {"version": "3.4.35", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/connect/-/connect-3.4.35.tgz", "integrity": "sha1-X89q5EXkAh0fwiGaSHPMc6O7KtE=", "requires": {"@types/node": "*"}}, "@types/connect-history-api-fallback": {"version": "1.5.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/connect-history-api-fallback/-/connect-history-api-fallback-1.5.0.tgz", "integrity": "sha1-n9ILOXS9wrzUrGVn4uD2iFyyz0E=", "requires": {"@types/express-serve-static-core": "*", "@types/node": "*"}}, "@types/eslint": {"version": "8.37.0", "resolved": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.37.0.tgz", "integrity": "sha512-Piet7dG2JBuDIfohBngQ3rCt7MgO9xCO4xIMKxBThCq5PNRB91IjlJ10eJVwfoNtvTErmxLzwBZ7rHZtbOMmFQ==", "requires": {"@types/estree": "*", "@types/json-schema": "*"}}, "@types/eslint-scope": {"version": "3.7.4", "resolved": "https://registry.npmmirror.com/@types/eslint-scope/-/eslint-scope-3.7.4.tgz", "integrity": "sha512-9K<PERSON>zoImiZc3HlIp6AVUDE4CWYx22a+lhSZMYNpbjW04+YF0KWj4pJXnEMjdnFTiQibFFmElcsasJXDbdI/EPhA==", "requires": {"@types/eslint": "*", "@types/estree": "*"}}, "@types/estree": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/@types/estree/-/estree-1.0.1.tgz", "integrity": "sha512-LG4opVs2ANWZ1TJoKc937iMmNstM/d0ae1vNbnBvBhqCSezgVUOzcLCqbI5elV8Vy6WKwKjaqR+zO9VKirBBCA=="}, "@types/express": {"version": "4.17.17", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/express/-/express-4.17.17.tgz", "integrity": "sha1-AdVDf275z6hmjmFuE8LyrJpJGuQ=", "requires": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}}, "@types/express-serve-static-core": {"version": "4.17.34", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/express-serve-static-core/-/express-serve-static-core-4.17.34.tgz", "integrity": "sha1-wRnoW3UhUXi8En3liOkxAGmKtMw=", "requires": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "@types/glob": {"version": "7.2.0", "resolved": "https://registry.npmmirror.com/@types/glob/-/glob-7.2.0.tgz", "integrity": "sha512-ZUxbzKl0IfJILTS6t7ip5fQQM/J3TJYubDm3nMbgubNNYS62eXeUpoLUC8/7fJNiFYHTrGPQn7hspDUzIHX3UA==", "requires": {"@types/minimatch": "*", "@types/node": "*"}}, "@types/html-minifier-terser": {"version": "6.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz", "integrity": "sha1-T8M6AMHQwWmHsaIM+S0gYUxVrDU="}, "@types/http-proxy": {"version": "1.17.11", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/http-proxy/-/http-proxy-1.17.11.tgz", "integrity": "sha1-DKIZSaVYjVWsK2WbaQNchL1dopM=", "requires": {"@types/node": "*"}}, "@types/json-schema": {"version": "7.0.11", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/json-schema/-/json-schema-7.0.11.tgz", "integrity": "sha1-1CG2xSejA398hEM/0sQingFoY9M="}, "@types/mime": {"version": "1.3.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/mime/-/mime-1.3.2.tgz", "integrity": "sha1-k+Jb+e51/g/YC1lLxP6w6GIRG1o="}, "@types/minimatch": {"version": "5.1.2", "resolved": "https://registry.npmmirror.com/@types/minimatch/-/minimatch-5.1.2.tgz", "integrity": "sha512-K0VQKziLUWkVKiRVrx4a40iPaxTUefQmjtkQofBkYRcoaaL/8rhwDWww9qWbrgicNOgnpIsMxyNIUM4+n6dUIA=="}, "@types/node": {"version": "18.16.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/node/-/node-18.16.2.tgz", "integrity": "sha1-L2EOpxA0s5ccMSGSN3+KcXjrV/E="}, "@types/qs": {"version": "6.9.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/qs/-/qs-6.9.7.tgz", "integrity": "sha1-Y7t9Bn2xB8weRXwwO8JdUR/r9ss="}, "@types/range-parser": {"version": "1.2.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/range-parser/-/range-parser-1.2.4.tgz", "integrity": "sha1-zWZ7z90CUhOq+3ylkVqTJZCs3Nw="}, "@types/retry": {"version": "0.12.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/retry/-/retry-0.12.0.tgz", "integrity": "sha1-KzXsz87n04zXKtmSMvvVi/+zyE0="}, "@types/send": {"version": "0.17.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/send/-/send-0.17.1.tgz", "integrity": "sha1-7UkyuKKoBfH+Nipw9OYtCsmU4wE=", "requires": {"@types/mime": "^1", "@types/node": "*"}}, "@types/serve-index": {"version": "1.9.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/serve-index/-/serve-index-1.9.1.tgz", "integrity": "sha1-G16FNwoZLAHsbOxHNc8pFzN6Yng=", "requires": {"@types/express": "*"}}, "@types/serve-static": {"version": "1.15.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/serve-static/-/serve-static-1.15.1.tgz", "integrity": "sha1-hrF1Pwvk+aG+5o1Fn82lvk6lK10=", "requires": {"@types/mime": "*", "@types/node": "*"}}, "@types/sockjs": {"version": "0.3.33", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/sockjs/-/sockjs-0.3.33.tgz", "integrity": "sha1-Vw06C5msmVNg4xNv1gRRE7G9I28=", "requires": {"@types/node": "*"}}, "@types/ws": {"version": "8.5.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/ws/-/ws-8.5.4.tgz", "integrity": "sha1-uxDjYRbW5XDdlDc1+GyTPBWHuKU=", "requires": {"@types/node": "*"}}, "@webassemblyjs/ast": {"version": "1.11.5", "resolved": "https://registry.npmmirror.com/@webassemblyjs/ast/-/ast-1.11.5.tgz", "integrity": "sha512-LHY/GSAZZRpsNQH+/oHqhRQ5FT7eoULcBqgfyTB5nQHogFnK3/7QoN7dLnwSE/JkUAF0SrRuclT7ODqMFtWxxQ==", "requires": {"@webassemblyjs/helper-numbers": "1.11.5", "@webassemblyjs/helper-wasm-bytecode": "1.11.5"}}, "@webassemblyjs/floating-point-hex-parser": {"version": "1.11.5", "resolved": "https://registry.npmmirror.com/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.11.5.tgz", "integrity": "sha512-1j1zTIC5EZOtCplMBG/IEwLtUojtwFVwdyVMbL/hwWqbzlQoJsWCOavrdnLkemwNoC/EOwtUFch3fuo+cbcXYQ=="}, "@webassemblyjs/helper-api-error": {"version": "1.11.5", "resolved": "https://registry.npmmirror.com/@webassemblyjs/helper-api-error/-/helper-api-error-1.11.5.tgz", "integrity": "sha512-L65bDPmfpY0+yFrsgz8b6LhXmbbs38OnwDCf6NpnMUYqa+ENfE5Dq9E42ny0qz/PdR0LJyq/T5YijPnU8AXEpA=="}, "@webassemblyjs/helper-buffer": {"version": "1.11.5", "resolved": "https://registry.npmmirror.com/@webassemblyjs/helper-buffer/-/helper-buffer-1.11.5.tgz", "integrity": "sha512-fDKo1gstwFFSfacIeH5KfwzjykIE6ldh1iH9Y/8YkAZrhmu4TctqYjSh7t0K2VyDSXOZJ1MLhht/k9IvYGcIxg=="}, "@webassemblyjs/helper-numbers": {"version": "1.11.5", "resolved": "https://registry.npmmirror.com/@webassemblyjs/helper-numbers/-/helper-numbers-1.11.5.tgz", "integrity": "sha512-DhykHXM0ZABqfIGYNv93A5KKDw/+ywBFnuWybZZWcuzWHfbp21wUfRkbtz7dMGwGgT4iXjWuhRMA2Mzod6W4WA==", "requires": {"@webassemblyjs/floating-point-hex-parser": "1.11.5", "@webassemblyjs/helper-api-error": "1.11.5", "@xtuc/long": "4.2.2"}}, "@webassemblyjs/helper-wasm-bytecode": {"version": "1.11.5", "resolved": "https://registry.npmmirror.com/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.11.5.tgz", "integrity": "sha512-oC4Qa0bNcqnjAowFn7MPCETQgDYytpsfvz4ujZz63Zu/a/v71HeCAAmZsgZ3YVKec3zSPYytG3/PrRCqbtcAvA=="}, "@webassemblyjs/helper-wasm-section": {"version": "1.11.5", "resolved": "https://registry.npmmirror.com/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.11.5.tgz", "integrity": "sha512-uEoThA1LN2NA+K3B9wDo3yKlBfVtC6rh0i4/6hvbz071E8gTNZD/pT0MsBf7MeD6KbApMSkaAK0XeKyOZC7CIA==", "requires": {"@webassemblyjs/ast": "1.11.5", "@webassemblyjs/helper-buffer": "1.11.5", "@webassemblyjs/helper-wasm-bytecode": "1.11.5", "@webassemblyjs/wasm-gen": "1.11.5"}}, "@webassemblyjs/ieee754": {"version": "1.11.5", "resolved": "https://registry.npmmirror.com/@webassemblyjs/ieee754/-/ieee754-1.11.5.tgz", "integrity": "sha512-37aGq6qVL8A8oPbPrSGMBcp38YZFXcHfiROflJn9jxSdSMMM5dS5P/9e2/TpaJuhE+wFrbukN2WI6Hw9MH5acg==", "requires": {"@xtuc/ieee754": "^1.2.0"}}, "@webassemblyjs/leb128": {"version": "1.11.5", "resolved": "https://registry.npmmirror.com/@webassemblyjs/leb128/-/leb128-1.11.5.tgz", "integrity": "sha512-ajqrRSXaTJoPW+xmkfYN6l8VIeNnR4vBOTQO9HzR7IygoCcKWkICbKFbVTNMjMgMREqXEr0+2M6zukzM47ZUfQ==", "requires": {"@xtuc/long": "4.2.2"}}, "@webassemblyjs/utf8": {"version": "1.11.5", "resolved": "https://registry.npmmirror.com/@webassemblyjs/utf8/-/utf8-1.11.5.tgz", "integrity": "sha512-Wi<PERSON>hulHKTZU5UPlRl53gHR8OxdGsSOxqfpqWeA2FmcwBMaoEdz6b2x2si3IwC9/fSPLfe8pBMRTHVMk5nlwnFQ=="}, "@webassemblyjs/wasm-edit": {"version": "1.11.5", "resolved": "https://registry.npmmirror.com/@webassemblyjs/wasm-edit/-/wasm-edit-1.11.5.tgz", "integrity": "sha512-C0p9D2fAu3Twwqvygvf42iGCQ4av8MFBLiTb+08SZ4cEdwzWx9QeAHDo1E2k+9s/0w1DM40oflJOpkZ8jW4HCQ==", "requires": {"@webassemblyjs/ast": "1.11.5", "@webassemblyjs/helper-buffer": "1.11.5", "@webassemblyjs/helper-wasm-bytecode": "1.11.5", "@webassemblyjs/helper-wasm-section": "1.11.5", "@webassemblyjs/wasm-gen": "1.11.5", "@webassemblyjs/wasm-opt": "1.11.5", "@webassemblyjs/wasm-parser": "1.11.5", "@webassemblyjs/wast-printer": "1.11.5"}}, "@webassemblyjs/wasm-gen": {"version": "1.11.5", "resolved": "https://registry.npmmirror.com/@webassemblyjs/wasm-gen/-/wasm-gen-1.11.5.tgz", "integrity": "sha512-14vteRlRjxLK9eSyYFvw1K8Vv+iPdZU0Aebk3j6oB8TQiQYuO6hj9s4d7qf6f2HJr2khzvNldAFG13CgdkAIfA==", "requires": {"@webassemblyjs/ast": "1.11.5", "@webassemblyjs/helper-wasm-bytecode": "1.11.5", "@webassemblyjs/ieee754": "1.11.5", "@webassemblyjs/leb128": "1.11.5", "@webassemblyjs/utf8": "1.11.5"}}, "@webassemblyjs/wasm-opt": {"version": "1.11.5", "resolved": "https://registry.npmmirror.com/@webassemblyjs/wasm-opt/-/wasm-opt-1.11.5.tgz", "integrity": "sha512-tc<PERSON>wlIXstBQgbKy1MlbDMlXaxpucn42eb17H29rawYLxm5+MsEmgPzeCP8B1Cl69hCice8LeKgZpRUAPtqYPgw==", "requires": {"@webassemblyjs/ast": "1.11.5", "@webassemblyjs/helper-buffer": "1.11.5", "@webassemblyjs/wasm-gen": "1.11.5", "@webassemblyjs/wasm-parser": "1.11.5"}}, "@webassemblyjs/wasm-parser": {"version": "1.11.5", "resolved": "https://registry.npmmirror.com/@webassemblyjs/wasm-parser/-/wasm-parser-1.11.5.tgz", "integrity": "sha512-SVXUIwsLQlc8srSD7jejsfTU83g7pIGr2YYNb9oHdtldSxaOhvA5xwvIiWIfcX8PlSakgqMXsLpLfbbJ4cBYew==", "requires": {"@webassemblyjs/ast": "1.11.5", "@webassemblyjs/helper-api-error": "1.11.5", "@webassemblyjs/helper-wasm-bytecode": "1.11.5", "@webassemblyjs/ieee754": "1.11.5", "@webassemblyjs/leb128": "1.11.5", "@webassemblyjs/utf8": "1.11.5"}}, "@webassemblyjs/wast-printer": {"version": "1.11.5", "resolved": "https://registry.npmmirror.com/@webassemblyjs/wast-printer/-/wast-printer-1.11.5.tgz", "integrity": "sha512-f7Pq3wvg3GSPUPzR0F6bmI89Hdb+u9WXrSKc4v+N0aV0q6r42WoF92Jp2jEorBEBRoRNXgjp53nBniDXcqZYPA==", "requires": {"@webassemblyjs/ast": "1.11.5", "@xtuc/long": "4.2.2"}}, "@xtuc/ieee754": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/@xtuc/ieee754/-/ieee754-1.2.0.tgz", "integrity": "sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA=="}, "@xtuc/long": {"version": "4.2.2", "resolved": "https://registry.npmmirror.com/@xtuc/long/-/long-4.2.2.tgz", "integrity": "sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ=="}, "accepts": {"version": "1.3.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/accepts/-/accepts-1.3.8.tgz", "integrity": "sha1-C/C+EltnAUrcsLCSHmLbe//hay4=", "requires": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}}, "acorn": {"version": "8.8.2", "resolved": "https://registry.npmmirror.com/acorn/-/acorn-8.8.2.tgz", "integrity": "sha512-xjIYgE8HBrkpd/sJqOGNspf8uHG+NOHGOw6a/Urj8taM2EXfdNAH2oFcPeIFfsv3+kz/mJrS5VuMqbNLjCa2vw=="}, "acorn-import-assertions": {"version": "1.8.0", "resolved": "https://registry.npmmirror.com/acorn-import-assertions/-/acorn-import-assertions-1.8.0.tgz", "integrity": "sha512-m7VZ3jwz4eK6A4Vtt8Ew1/mNbP24u0FhdyfA7fSvnJR6LMdfOYnmuIrrJAgrYfYJ10F/otaHTtrtrtmHdMNzEw=="}, "acorn-jsx": {"version": "5.3.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "integrity": "sha1-ftW7VZCLOy8bxVxq8WU7rafweTc="}, "ajv": {"version": "6.12.6", "resolved": "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz", "integrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==", "requires": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ajv-formats": {"version": "2.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ajv-formats/-/ajv-formats-2.1.1.tgz", "integrity": "sha1-bmaUAGWet0lzu/LjMycYCgmWtSA=", "requires": {"ajv": "^8.0.0"}, "dependencies": {"ajv": {"version": "8.12.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ajv/-/ajv-8.12.0.tgz", "integrity": "sha1-0aBScyPiL1NWLFZ8AJkVd9++GdE=", "requires": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}}, "json-schema-traverse": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "integrity": "sha1-rnvLNlard6c7pcSb9lTzjmtoYOI="}}}, "ajv-keywords": {"version": "3.5.2", "resolved": "https://registry.npmmirror.com/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "integrity": "sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ=="}, "ansi-escapes": {"version": "4.3.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "integrity": "sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=", "requires": {"type-fest": "^0.21.3"}, "dependencies": {"type-fest": {"version": "0.21.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/type-fest/-/type-fest-0.21.3.tgz", "integrity": "sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc="}}}, "ansi-html-community": {"version": "0.0.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ansi-html-community/-/ansi-html-community-0.0.8.tgz", "integrity": "sha1-afvE1sy+OD+XNpNK40w/gpDxv0E="}, "ansi-regex": {"version": "5.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ="}, "ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "anymatch": {"version": "3.1.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/anymatch/-/anymatch-3.1.3.tgz", "integrity": "sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=", "requires": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}}, "archiver": {"version": "5.3.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/archiver/-/archiver-5.3.1.tgz", "integrity": "sha1-IekoEdbwns/OZJ++/v6MeeV8u7Y=", "requires": {"archiver-utils": "^2.1.0", "async": "^3.2.3", "buffer-crc32": "^0.2.1", "readable-stream": "^3.6.0", "readdir-glob": "^1.0.0", "tar-stream": "^2.2.0", "zip-stream": "^4.1.0"}, "dependencies": {"async": {"version": "3.2.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/async/-/async-3.2.4.tgz", "integrity": "sha1-LSLgD4zd61/eXdM1IrVtHPVpqBw="}}}, "archiver-utils": {"version": "2.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/archiver-utils/-/archiver-utils-2.1.0.tgz", "integrity": "sha1-6KRg6UtpPD49oYKgmMpihbqSSeI=", "requires": {"glob": "^7.1.4", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash.defaults": "^4.2.0", "lodash.difference": "^4.5.0", "lodash.flatten": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.union": "^4.6.0", "normalize-path": "^3.0.0", "readable-stream": "^2.0.0"}, "dependencies": {"readable-stream": {"version": "2.3.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/readable-stream/-/readable-stream-2.3.8.tgz", "integrity": "sha1-kRJegEK7obmIf0k0X2J3Anzovps=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "safe-buffer": {"version": "5.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="}, "string_decoder": {"version": "1.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "requires": {"safe-buffer": "~5.1.0"}}}}, "argparse": {"version": "2.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/argparse/-/argparse-2.0.1.tgz", "integrity": "sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg="}, "array-flatten": {"version": "2.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/array-flatten/-/array-flatten-2.1.2.tgz", "integrity": "sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk="}, "array-union": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/array-union/-/array-union-1.0.2.tgz", "integrity": "sha512-Dxr6QJj/RdU/hCaBjOfxW+q6lyuVE6JFWIrAUpuOOhoJJoQ99cUn3igRaHVB5P9WrgFVN0FfArM3x0cueOU8ng==", "requires": {"array-uniq": "^1.0.1"}}, "array-uniq": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/array-uniq/-/array-uniq-1.0.3.tgz", "integrity": "sha512-MNha4BWQ6JbwhFhj03YK552f7cb3AzoE8SzeljgChvL1dl3IcvggXVz1DilzySZkCja+CXuZbdW7yATchWn8/Q=="}, "async": {"version": "2.6.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/async/-/async-2.6.4.tgz", "integrity": "sha1-cGt/9ghGZM1+rnE/b5ZUM7VQQiE=", "requires": {"lodash": "^4.17.14"}}, "asynckit": {"version": "0.4.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k="}, "axios": {"version": "1.4.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/axios/-/axios-1.4.0.tgz", "integrity": "sha1-OKe/EiTNMI3icRRgOLVR1yXwvh8=", "requires": {"follow-redirects": "^1.15.0", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "balanced-match": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4="}, "base64-js": {"version": "1.5.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha1-GxtEAWClv3rUC2UPCVljSBkDkwo="}, "batch": {"version": "0.6.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/batch/-/batch-0.6.1.tgz", "integrity": "sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY="}, "big.js": {"version": "5.2.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/big.js/-/big.js-5.2.2.tgz", "integrity": "sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg="}, "binary-extensions": {"version": "2.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/binary-extensions/-/binary-extensions-2.2.0.tgz", "integrity": "sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0="}, "bl": {"version": "4.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/bl/-/bl-4.1.0.tgz", "integrity": "sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo=", "requires": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "body-parser": {"version": "1.20.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/body-parser/-/body-parser-1.20.1.tgz", "integrity": "sha1-sYEqiRLBlc03Gj7l5m+qIzilxmg=", "requires": {"bytes": "3.1.2", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.11.0", "raw-body": "2.5.1", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "dependencies": {"bytes": {"version": "3.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/bytes/-/bytes-3.1.2.tgz", "integrity": "sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU="}}}, "bonjour-service": {"version": "1.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/bonjour-service/-/bonjour-service-1.1.1.tgz", "integrity": "sha1-lglI+g4BU/XSZ0OrFbr44zdSwTU=", "requires": {"array-flatten": "^2.1.2", "dns-equal": "^1.0.0", "fast-deep-equal": "^3.1.3", "multicast-dns": "^7.2.5"}}, "boolbase": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/boolbase/-/boolbase-1.0.0.tgz", "integrity": "sha1-aN/1++YMUes3cl6p4+0xDcwed24="}, "brace-expansion": {"version": "1.1.11", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "3.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/braces/-/braces-3.0.2.tgz", "integrity": "sha1-NFThpGLujVmeI23zNs2epPiv4Qc=", "requires": {"fill-range": "^7.0.1"}}, "browserslist": {"version": "4.21.5", "resolved": "https://registry.npmmirror.com/browserslist/-/browserslist-4.21.5.tgz", "integrity": "sha512-tUkiguQGW7S3IhB7N+c2MV/HZPSCPAAiYBZXLsBhFB/PCy6ZKKsZrmBayHV9fdGV/ARIfJ14NkxKzRDjvp7L6w==", "requires": {"caniuse-lite": "^1.0.30001449", "electron-to-chromium": "^1.4.284", "node-releases": "^2.0.8", "update-browserslist-db": "^1.0.10"}}, "buffer": {"version": "5.7.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/buffer/-/buffer-5.7.1.tgz", "integrity": "sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=", "requires": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "buffer-crc32": {"version": "0.2.13", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/buffer-crc32/-/buffer-crc32-0.2.13.tgz", "integrity": "sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI="}, "buffer-from": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/buffer-from/-/buffer-from-1.1.2.tgz", "integrity": "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ=="}, "bytes": {"version": "3.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/bytes/-/bytes-3.0.0.tgz", "integrity": "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg="}, "call-bind": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "callsites": {"version": "3.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/callsites/-/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M="}, "camel-case": {"version": "4.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/camel-case/-/camel-case-4.1.2.tgz", "integrity": "sha1-lygHKpVPgFIoIlpt7qazhGHhvVo=", "requires": {"pascal-case": "^3.1.2", "tslib": "^2.0.3"}}, "caniuse-lite": {"version": "1.0.30001482", "resolved": "https://registry.npmmirror.com/caniuse-lite/-/caniuse-lite-1.0.30001482.tgz", "integrity": "sha512-F1ZInsg53cegyjroxLNW9DmrEQ1SuGRTO1QlpA0o2/6OpQ0gFeDRoq1yFmnr8Sakn9qwwt9DmbxHB6w167OSuQ=="}, "chalk": {"version": "4.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/chalk/-/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "chardet": {"version": "0.7.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/chardet/-/chardet-0.7.0.tgz", "integrity": "sha1-kAlISfCTfy7twkJdDSip5fDLrZ4="}, "chokidar": {"version": "3.5.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/chokidar/-/chokidar-3.5.3.tgz", "integrity": "sha1-HPN8hwe5Mr0a8a4iwEMuKs0ZA70=", "requires": {"anymatch": "~3.1.2", "braces": "~3.0.2", "fsevents": "~2.3.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}}, "chrome-trace-event": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz", "integrity": "sha512-p3KULyQg4S7NIHixdwbGX+nFHkoBiA4YQmyWtjb8XngSKV124nJmRysgAeujbUVb15vh+RvFUfCPqU7rXk+hZg=="}, "clean-css": {"version": "5.3.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/clean-css/-/clean-css-5.3.2.tgz", "integrity": "sha1-cOzH1NQRSSH10pg0n/hqMamXUiQ=", "requires": {"source-map": "~0.6.0"}}, "clean-webpack-plugin": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/clean-webpack-plugin/-/clean-webpack-plugin-4.0.0.tgz", "integrity": "sha512-WuWE1nyTNAyW5T7oNyys2EN0cfP2fdRxhxnIQWiAp0bMabPdHhoGxM8A6YL2GhqwgrPnnaemVE7nv5XJ2Fhh2w==", "requires": {"del": "^4.1.1"}}, "cli-cursor": {"version": "3.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/cli-cursor/-/cli-cursor-3.1.0.tgz", "integrity": "sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=", "requires": {"restore-cursor": "^3.1.0"}}, "cli-spinners": {"version": "2.9.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/cli-spinners/-/cli-spinners-2.9.0.tgz", "integrity": "sha1-WIHQrZY4HhF7vgetkfIAj+b/2Ns="}, "cli-width": {"version": "3.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/cli-width/-/cli-width-3.0.0.tgz", "integrity": "sha1-ovSEN6LKqaIkNueUvwceyeYc7fY="}, "cliui": {"version": "8.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/cliui/-/cliui-8.0.1.tgz", "integrity": "sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=", "requires": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}}, "clone": {"version": "1.0.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/clone/-/clone-1.0.4.tgz", "integrity": "sha1-2jCcwmPfFZlMaIypAheco8fNfH4="}, "clone-deep": {"version": "4.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/clone-deep/-/clone-deep-4.0.1.tgz", "integrity": "sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c=", "requires": {"is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^3.0.0"}}, "color-convert": {"version": "2.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/color-name/-/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI="}, "colorette": {"version": "2.0.20", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/colorette/-/colorette-2.0.20.tgz", "integrity": "sha1-nreT5oMwZ/cjWQL807CZF6AAqVo="}, "combined-stream": {"version": "1.0.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=", "requires": {"delayed-stream": "~1.0.0"}}, "commander": {"version": "2.20.3", "resolved": "https://registry.npmmirror.com/commander/-/commander-2.20.3.tgz", "integrity": "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="}, "commondir": {"version": "1.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/commondir/-/commondir-1.0.1.tgz", "integrity": "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs="}, "compress-commons": {"version": "4.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/compress-commons/-/compress-commons-4.1.1.tgz", "integrity": "sha1-3yoJp+0XRHZCutEKhcyaGeXEKn0=", "requires": {"buffer-crc32": "^0.2.13", "crc32-stream": "^4.0.2", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}}, "compressible": {"version": "2.0.18", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/compressible/-/compressible-2.0.18.tgz", "integrity": "sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=", "requires": {"mime-db": ">= 1.43.0 < 2"}}, "compression": {"version": "1.7.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/compression/-/compression-1.7.4.tgz", "integrity": "sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48=", "requires": {"accepts": "~1.3.5", "bytes": "3.0.0", "compressible": "~2.0.16", "debug": "2.6.9", "on-headers": "~1.0.2", "safe-buffer": "5.1.2", "vary": "~1.1.2"}, "dependencies": {"safe-buffer": {"version": "5.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="}}}, "concat-map": {"version": "0.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="}, "connect-history-api-fallback": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/connect-history-api-fallback/-/connect-history-api-fallback-2.0.0.tgz", "integrity": "sha1-ZHJkhFJRoNryW5fOh4NMrOD18cg="}, "content-disposition": {"version": "0.5.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/content-disposition/-/content-disposition-0.5.4.tgz", "integrity": "sha1-i4K076yCUSoCuwsdzsnSxejrW/4=", "requires": {"safe-buffer": "5.2.1"}}, "content-type": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/content-type/-/content-type-1.0.5.tgz", "integrity": "sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg="}, "convert-source-map": {"version": "1.9.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/convert-source-map/-/convert-source-map-1.9.0.tgz", "integrity": "sha1-f6rmI1P7QhM2bQypg1jSLoNosF8="}, "cookie": {"version": "0.5.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/cookie/-/cookie-0.5.0.tgz", "integrity": "sha1-0fXXGt7GVYxY84mYfDZqpH6ZT4s="}, "cookie-signature": {"version": "1.0.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="}, "core-util-is": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/core-util-is/-/core-util-is-1.0.3.tgz", "integrity": "sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U="}, "crc-32": {"version": "1.2.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/crc-32/-/crc-32-1.2.2.tgz", "integrity": "sha1-PK01qTS4v3HyXKUkttpR+36s4v8="}, "crc32-stream": {"version": "4.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/crc32-stream/-/crc32-stream-4.0.2.tgz", "integrity": "sha1-ySKtIrODlavp04cPAvqBNO1wkAc=", "requires": {"crc-32": "^1.2.0", "readable-stream": "^3.4.0"}}, "cross-spawn": {"version": "7.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/cross-spawn/-/cross-spawn-7.0.3.tgz", "integrity": "sha1-9zqFudXUHQRVUcF34ogtSshXKKY=", "requires": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}}, "css-select": {"version": "4.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/css-select/-/css-select-4.3.0.tgz", "integrity": "sha1-23EpsoRmYv2GKM/ElquytZ5BUps=", "requires": {"boolbase": "^1.0.0", "css-what": "^6.0.1", "domhandler": "^4.3.1", "domutils": "^2.8.0", "nth-check": "^2.0.1"}}, "css-what": {"version": "6.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/css-what/-/css-what-6.1.0.tgz", "integrity": "sha1-+17/z3bx3eosgb36pN5E55uscPQ="}, "debug": {"version": "2.6.9", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "deep-is": {"version": "0.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/deep-is/-/deep-is-0.1.4.tgz", "integrity": "sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE="}, "default-gateway": {"version": "6.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/default-gateway/-/default-gateway-6.0.3.tgz", "integrity": "sha1-gZSUyIgFO9t0PtvzQ9bN9/KUOnE=", "requires": {"execa": "^5.0.0"}}, "defaults": {"version": "1.0.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/defaults/-/defaults-1.0.4.tgz", "integrity": "sha1-sLAgYsHiqmL/XZUo8PmLqpCXjXo=", "requires": {"clone": "^1.0.2"}}, "define-lazy-prop": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz", "integrity": "sha1-P3rkIRKbyqrJvHSQXJigAJ7J7n8="}, "del": {"version": "4.1.1", "resolved": "https://registry.npmmirror.com/del/-/del-4.1.1.tgz", "integrity": "sha512-QwGuEUouP2kVwQenAsOof5Fv8K9t3D8Ca8NxcXKrIpEHjTXK5J2nXLdP+ALI1cgv8wj7KuwBhTwBkOZSJKM5XQ==", "requires": {"@types/glob": "^7.1.1", "globby": "^6.1.0", "is-path-cwd": "^2.0.0", "is-path-in-cwd": "^2.0.0", "p-map": "^2.0.0", "pify": "^4.0.1", "rimraf": "^2.6.3"}, "dependencies": {"rimraf": {"version": "2.7.1", "resolved": "https://registry.npmmirror.com/rimraf/-/rimraf-2.7.1.tgz", "integrity": "sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==", "requires": {"glob": "^7.1.3"}}}}, "delayed-stream": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="}, "depd": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/depd/-/depd-2.0.0.tgz", "integrity": "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8="}, "destroy": {"version": "1.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/destroy/-/destroy-1.2.0.tgz", "integrity": "sha1-SANzVQmti+VSk0xn32FPlOZvoBU="}, "detect-node": {"version": "2.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/detect-node/-/detect-node-2.1.0.tgz", "integrity": "sha1-yccHdaScPQO8LAbZpzvlUPl4+LE="}, "dns-equal": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/dns-equal/-/dns-equal-1.0.0.tgz", "integrity": "sha1-s55/HabrCnW6nBcySzR1PEfgZU0="}, "dns-packet": {"version": "5.6.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/dns-packet/-/dns-packet-5.6.0.tgz", "integrity": "sha1-IgLJR4RcemPCPs5Y8vcP9qtML30=", "requires": {"@leichtgewicht/ip-codec": "^2.0.1"}}, "doctrine": {"version": "3.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/doctrine/-/doctrine-3.0.0.tgz", "integrity": "sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=", "requires": {"esutils": "^2.0.2"}}, "dom-converter": {"version": "0.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/dom-converter/-/dom-converter-0.2.0.tgz", "integrity": "sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=", "requires": {"utila": "~0.4"}}, "dom-serializer": {"version": "1.4.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/dom-serializer/-/dom-serializer-1.4.1.tgz", "integrity": "sha1-3l1Bsa6ikCFdxFptrorc8dMuLTA=", "requires": {"domelementtype": "^2.0.1", "domhandler": "^4.2.0", "entities": "^2.0.0"}}, "domelementtype": {"version": "2.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/domelementtype/-/domelementtype-2.3.0.tgz", "integrity": "sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0="}, "domhandler": {"version": "4.3.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/domhandler/-/domhandler-4.3.1.tgz", "integrity": "sha1-jXkgM0FvWdaLwDpap7AYwcqJJ5w=", "requires": {"domelementtype": "^2.2.0"}}, "domutils": {"version": "2.8.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/domutils/-/domutils-2.8.0.tgz", "integrity": "sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=", "requires": {"dom-serializer": "^1.0.1", "domelementtype": "^2.2.0", "domhandler": "^4.2.0"}}, "dot-case": {"version": "3.0.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/dot-case/-/dot-case-3.0.4.tgz", "integrity": "sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=", "requires": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "ee-first": {"version": "1.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="}, "electron-to-chromium": {"version": "1.4.380", "resolved": "https://registry.npmmirror.com/electron-to-chromium/-/electron-to-chromium-1.4.380.tgz", "integrity": "sha512-XKGdI4pWM78eLH2cbXJHiBnWUwFSzZM7XujsB6stDiGu9AeSqziedP6amNLpJzE3i0rLTcfAwdCTs5ecP5yeSg=="}, "emoji-regex": {"version": "8.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc="}, "emojis-list": {"version": "3.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/emojis-list/-/emojis-list-3.0.0.tgz", "integrity": "sha1-VXBmIEatKeLpFucariYKvf9Pang="}, "encodeurl": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="}, "end-of-stream": {"version": "1.4.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/end-of-stream/-/end-of-stream-1.4.4.tgz", "integrity": "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=", "requires": {"once": "^1.4.0"}}, "enhanced-resolve": {"version": "5.13.0", "resolved": "https://registry.npmmirror.com/enhanced-resolve/-/enhanced-resolve-5.13.0.tgz", "integrity": "sha512-eyV8f0y1+bzyfh8xAwW/WTSZpLbjhqc4ne9eGSH4Zo2ejdyiNG9pU6mf9DG8a7+Auk6MFTlNOT4Y2y/9k8GKVg==", "requires": {"graceful-fs": "^4.2.4", "tapable": "^2.2.0"}}, "entities": {"version": "2.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/entities/-/entities-2.2.0.tgz", "integrity": "sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU="}, "es-module-lexer": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/es-module-lexer/-/es-module-lexer-1.2.1.tgz", "integrity": "sha512-9978wrXM50Y4rTMmW5kXIC09ZdXQZqkE4mxhwkd8VbzsGkXGPgV4zWuqQJgCEzYngdo2dYDa0l8xhX4fkSwJSg=="}, "escalade": {"version": "3.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/escalade/-/escalade-3.1.1.tgz", "integrity": "sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA="}, "escape-html": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="}, "escape-string-regexp": {"version": "4.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "integrity": "sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ="}, "eslint": {"version": "8.40.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eslint/-/eslint-8.40.0.tgz", "integrity": "sha1-pWTNAJnzhULE6aL2MPpFvzO8QqQ=", "requires": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.4.0", "@eslint/eslintrc": "^2.0.3", "@eslint/js": "8.40.0", "@humanwhocodes/config-array": "^0.11.8", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "ajv": "^6.10.0", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.0", "eslint-visitor-keys": "^3.4.1", "espree": "^9.5.2", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "grapheme-splitter": "^1.0.4", "ignore": "^5.2.0", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-sdsl": "^4.1.4", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.1", "strip-ansi": "^6.0.1", "strip-json-comments": "^3.1.0", "text-table": "^0.2.0"}, "dependencies": {"chalk": {"version": "4.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/chalk/-/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "debug": {"version": "4.3.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/debug/-/debug-4.3.4.tgz", "integrity": "sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=", "requires": {"ms": "2.1.2"}}, "eslint-scope": {"version": "7.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eslint-scope/-/eslint-scope-7.2.0.tgz", "integrity": "sha1-8h69r9oCNS8QNjS5bdR9n4HKEXs=", "requires": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}}, "estraverse": {"version": "5.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/estraverse/-/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM="}, "glob-parent": {"version": "6.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/glob-parent/-/glob-parent-6.0.2.tgz", "integrity": "sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=", "requires": {"is-glob": "^4.0.3"}}, "is-path-inside": {"version": "3.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-path-inside/-/is-path-inside-3.0.3.tgz", "integrity": "sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM="}, "ms": {"version": "2.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}}}, "eslint-config-google": {"version": "0.14.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eslint-config-google/-/eslint-config-google-0.14.0.tgz", "integrity": "sha1-T1+HWbpuEbQkKUohnb+hjFCLzBo=", "dev": true}, "eslint-loader": {"version": "4.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eslint-loader/-/eslint-loader-4.0.2.tgz", "integrity": "sha1-OGoeIby2E7PPLSUqO3CAI8z7Qew=", "requires": {"find-cache-dir": "^3.3.1", "fs-extra": "^8.1.0", "loader-utils": "^2.0.0", "object-hash": "^2.0.3", "schema-utils": "^2.6.5"}, "dependencies": {"schema-utils": {"version": "2.7.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/schema-utils/-/schema-utils-2.7.1.tgz", "integrity": "sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=", "requires": {"@types/json-schema": "^7.0.5", "ajv": "^6.12.4", "ajv-keywords": "^3.5.2"}}}}, "eslint-plugin-es": {"version": "3.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eslint-plugin-es/-/eslint-plugin-es-3.0.1.tgz", "integrity": "sha1-dafN/czdwFiZNK7rOEF18iHFeJM=", "dev": true, "requires": {"eslint-utils": "^2.0.0", "regexpp": "^3.0.0"}}, "eslint-plugin-node": {"version": "11.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eslint-plugin-node/-/eslint-plugin-node-11.1.0.tgz", "integrity": "sha1-yVVEQW7kraJnQKMEdO78VALcZx0=", "dev": true, "requires": {"eslint-plugin-es": "^3.0.0", "eslint-utils": "^2.0.0", "ignore": "^5.1.1", "minimatch": "^3.0.4", "resolve": "^1.10.1", "semver": "^6.1.0"}}, "eslint-scope": {"version": "5.1.1", "resolved": "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-5.1.1.tgz", "integrity": "sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==", "requires": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}}, "eslint-utils": {"version": "2.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eslint-utils/-/eslint-utils-2.1.0.tgz", "integrity": "sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=", "dev": true, "requires": {"eslint-visitor-keys": "^1.1.0"}, "dependencies": {"eslint-visitor-keys": {"version": "1.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz", "integrity": "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=", "dev": true}}}, "eslint-visitor-keys": {"version": "3.4.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eslint-visitor-keys/-/eslint-visitor-keys-3.4.1.tgz", "integrity": "sha1-wixI9IlC0IyoJMxSYhGuQAR4qZQ="}, "espree": {"version": "9.5.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/espree/-/espree-9.5.2.tgz", "integrity": "sha1-6ZTn3DOggqeoLc6vEog6gpNTIVs=", "requires": {"acorn": "^8.8.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}}, "esquery": {"version": "1.5.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/esquery/-/esquery-1.5.0.tgz", "integrity": "sha1-bOF3ON6Fd2lO3XNhxXGCrIyw2ws=", "requires": {"estraverse": "^5.1.0"}, "dependencies": {"estraverse": {"version": "5.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/estraverse/-/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM="}}}, "esrecurse": {"version": "4.3.0", "resolved": "https://registry.npmmirror.com/esrecurse/-/esrecurse-4.3.0.tgz", "integrity": "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==", "requires": {"estraverse": "^5.2.0"}, "dependencies": {"estraverse": {"version": "5.3.0", "resolved": "https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz", "integrity": "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA=="}}}, "estraverse": {"version": "4.3.0", "resolved": "https://registry.npmmirror.com/estraverse/-/estraverse-4.3.0.tgz", "integrity": "sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw=="}, "esutils": {"version": "2.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/esutils/-/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q="}, "etag": {"version": "1.8.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/etag/-/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="}, "eventemitter3": {"version": "4.0.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eventemitter3/-/eventemitter3-4.0.7.tgz", "integrity": "sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8="}, "events": {"version": "3.3.0", "resolved": "https://registry.npmmirror.com/events/-/events-3.3.0.tgz", "integrity": "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q=="}, "execa": {"version": "5.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/execa/-/execa-5.1.1.tgz", "integrity": "sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=", "requires": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}}, "express": {"version": "4.18.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/express/-/express-4.18.2.tgz", "integrity": "sha1-P6vggpbpMMeWwZ48UWl5OGup/Vk=", "requires": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.1", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.5.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.2.0", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.7", "qs": "6.11.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.18.0", "serve-static": "1.15.0", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "dependencies": {"array-flatten": {"version": "1.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="}}}, "external-editor": {"version": "3.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/external-editor/-/external-editor-3.1.0.tgz", "integrity": "sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=", "requires": {"chardet": "^0.7.0", "iconv-lite": "^0.4.24", "tmp": "^0.0.33"}}, "fast-deep-equal": {"version": "3.1.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU="}, "fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="}, "fast-levenshtein": {"version": "2.0.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc="}, "fastq": {"version": "1.15.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/fastq/-/fastq-1.15.0.tgz", "integrity": "sha1-0E0HxqKmj+RZn+qNLhA6k3+uazo=", "requires": {"reusify": "^1.0.4"}}, "faye-websocket": {"version": "0.11.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/faye-websocket/-/faye-websocket-0.11.4.tgz", "integrity": "sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo=", "requires": {"websocket-driver": ">=0.5.1"}}, "figures": {"version": "3.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/figures/-/figures-3.2.0.tgz", "integrity": "sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=", "requires": {"escape-string-regexp": "^1.0.5"}, "dependencies": {"escape-string-regexp": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="}}}, "file-entry-cache": {"version": "6.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/file-entry-cache/-/file-entry-cache-6.0.1.tgz", "integrity": "sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=", "requires": {"flat-cache": "^3.0.4"}}, "fill-range": {"version": "7.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/fill-range/-/fill-range-7.0.1.tgz", "integrity": "sha1-GRmmp8df44ssfHflGYU12prN2kA=", "requires": {"to-regex-range": "^5.0.1"}}, "finalhandler": {"version": "1.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/finalhandler/-/finalhandler-1.2.0.tgz", "integrity": "sha1-fSP+VzGyB7RkDk/NAK7B+SB6ezI=", "requires": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}}, "find-cache-dir": {"version": "3.3.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/find-cache-dir/-/find-cache-dir-3.3.2.tgz", "integrity": "sha1-swxbbv8HMHMa6pu9nb7L2AJW1ks=", "requires": {"commondir": "^1.0.1", "make-dir": "^3.0.2", "pkg-dir": "^4.1.0"}}, "find-up": {"version": "5.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/find-up/-/find-up-5.0.0.tgz", "integrity": "sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=", "requires": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}}, "flat-cache": {"version": "3.0.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/flat-cache/-/flat-cache-3.0.4.tgz", "integrity": "sha1-YbAzgwKy/p+Vfcwy/CqH8cMEixE=", "requires": {"flatted": "^3.1.0", "rimraf": "^3.0.2"}}, "flatted": {"version": "3.2.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/flatted/-/flatted-3.2.7.tgz", "integrity": "sha1-YJ85IHy2FLidB2W0d8stQ3+/l4c="}, "follow-redirects": {"version": "1.15.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/follow-redirects/-/follow-redirects-1.15.2.tgz", "integrity": "sha1-tGCGQUS6Y/JoEJbydMTlcCbaLBM="}, "form-data": {"version": "4.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/form-data/-/form-data-4.0.0.tgz", "integrity": "sha1-k5Gdrq82HuUpWEubMWZNwSyfpFI=", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}}, "forwarded": {"version": "0.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/forwarded/-/forwarded-0.2.0.tgz", "integrity": "sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE="}, "fresh": {"version": "0.5.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/fresh/-/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="}, "fs-constants": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/fs-constants/-/fs-constants-1.0.0.tgz", "integrity": "sha1-a+Dem+mYzhavivwkSXue6bfM2a0="}, "fs-extra": {"version": "8.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/fs-extra/-/fs-extra-8.1.0.tgz", "integrity": "sha1-SdQ8RaiM2Wd2aMt74bRu/bjS4cA=", "requires": {"graceful-fs": "^4.2.0", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}}, "fs-monkey": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/fs-monkey/-/fs-monkey-1.0.3.tgz", "integrity": "sha1-rjrJLVO7Mo7+DpodlUH2rY1I4tM="}, "fs.realpath": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="}, "fsevents": {"version": "2.3.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/fsevents/-/fsevents-2.3.2.tgz", "integrity": "sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro=", "optional": true}, "function-bind": {"version": "1.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/function-bind/-/function-bind-1.1.1.tgz", "integrity": "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0="}, "gensync": {"version": "1.0.0-beta.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA="}, "get-caller-file": {"version": "2.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34="}, "get-intrinsic": {"version": "1.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/get-intrinsic/-/get-intrinsic-1.2.0.tgz", "integrity": "sha1-etHcBTXzopBLugdXcnY+UFH20F8=", "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}}, "get-stream": {"version": "6.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/get-stream/-/get-stream-6.0.1.tgz", "integrity": "sha1-omLY7vZ6ztV8KFKtYWdSakPL97c="}, "glob": {"version": "7.2.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/glob/-/glob-7.2.3.tgz", "integrity": "sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=", "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-parent": {"version": "5.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "requires": {"is-glob": "^4.0.1"}}, "glob-to-regexp": {"version": "0.4.1", "resolved": "https://registry.npmmirror.com/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz", "integrity": "sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw=="}, "globals": {"version": "13.20.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/globals/-/globals-13.20.0.tgz", "integrity": "sha1-6idqHlCP/U8WEoiPnRutHicXv4I=", "requires": {"type-fest": "^0.20.2"}}, "globby": {"version": "6.1.0", "resolved": "https://registry.npmmirror.com/globby/-/globby-6.1.0.tgz", "integrity": "sha512-KVbFv2TQtbzCoxAnfD6JcHZTYCzyliEaaeM/gH8qQdkKr5s0OP9scEgvdcngyk7AVdY6YVW/TJHd+lQ/Df3Daw==", "requires": {"array-union": "^1.0.1", "glob": "^7.0.3", "object-assign": "^4.0.1", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}, "dependencies": {"pify": {"version": "2.3.0", "resolved": "https://registry.npmmirror.com/pify/-/pify-2.3.0.tgz", "integrity": "sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog=="}}}, "graceful-fs": {"version": "4.2.11", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/graceful-fs/-/graceful-fs-4.2.11.tgz", "integrity": "sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM="}, "grapheme-splitter": {"version": "1.0.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/grapheme-splitter/-/grapheme-splitter-1.0.4.tgz", "integrity": "sha1-nPOmZcYkdHmJaDSvNc8du0QAdn4="}, "handle-thing": {"version": "2.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/handle-thing/-/handle-thing-2.0.1.tgz", "integrity": "sha1-hX95zjWVgMNA1DCBzGSJcNC7I04="}, "has": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has/-/has-1.0.3.tgz", "integrity": "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=", "requires": {"function-bind": "^1.1.1"}}, "has-flag": {"version": "4.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s="}, "has-symbols": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg="}, "he": {"version": "1.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/he/-/he-1.2.0.tgz", "integrity": "sha1-hK5l+n6vsWX922FWauFLrwVmTw8="}, "highlight.js": {"version": "11.8.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/highlight.js/-/highlight.js-11.8.0.tgz", "integrity": "sha1-lmUY6oMle64ufJpIWWIxhWVVu2U="}, "hpack.js": {"version": "2.1.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/hpack.js/-/hpack.js-2.1.6.tgz", "integrity": "sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=", "requires": {"inherits": "^2.0.1", "obuf": "^1.0.0", "readable-stream": "^2.0.1", "wbuf": "^1.1.0"}, "dependencies": {"readable-stream": {"version": "2.3.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/readable-stream/-/readable-stream-2.3.8.tgz", "integrity": "sha1-kRJegEK7obmIf0k0X2J3Anzovps=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "safe-buffer": {"version": "5.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="}, "string_decoder": {"version": "1.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "requires": {"safe-buffer": "~5.1.0"}}}}, "html-entities": {"version": "2.3.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/html-entities/-/html-entities-2.3.3.tgz", "integrity": "sha1-EX12Jr7OMn/Iuqzoho+m9e+FbkY="}, "html-minifier-terser": {"version": "6.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz", "integrity": "sha1-v8gYk0zAeRj2s2afV3Ts39SPMqs=", "requires": {"camel-case": "^4.1.2", "clean-css": "^5.2.2", "commander": "^8.3.0", "he": "^1.2.0", "param-case": "^3.0.4", "relateurl": "^0.2.7", "terser": "^5.10.0"}, "dependencies": {"commander": {"version": "8.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/commander/-/commander-8.3.0.tgz", "integrity": "sha1-SDfqGy2me5xhamevuw+v7lZ7ymY="}}}, "html-webpack-plugin": {"version": "5.5.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/html-webpack-plugin/-/html-webpack-plugin-5.5.1.tgz", "integrity": "sha1-gmg44xtCf19/MJcfjY+iQi36Z2M=", "requires": {"@types/html-minifier-terser": "^6.0.0", "html-minifier-terser": "^6.0.2", "lodash": "^4.17.21", "pretty-error": "^4.0.0", "tapable": "^2.0.0"}}, "htmlparser2": {"version": "6.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/htmlparser2/-/htmlparser2-6.1.0.tgz", "integrity": "sha1-xNditsM3GgXb5l6UrkOp+EX7j7c=", "requires": {"domelementtype": "^2.0.1", "domhandler": "^4.0.0", "domutils": "^2.5.2", "entities": "^2.0.0"}}, "http-deceiver": {"version": "1.2.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/http-deceiver/-/http-deceiver-1.2.7.tgz", "integrity": "sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc="}, "http-errors": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/http-errors/-/http-errors-2.0.0.tgz", "integrity": "sha1-t3dKFIbvc892Z6ya4IWMASxXudM=", "requires": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}}, "http-parser-js": {"version": "0.5.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/http-parser-js/-/http-parser-js-0.5.8.tgz", "integrity": "sha1-ryMJDZrE4kVz3m9q7MnYSki/IOM="}, "http-proxy": {"version": "1.18.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/http-proxy/-/http-proxy-1.18.1.tgz", "integrity": "sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=", "requires": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}}, "http-proxy-middleware": {"version": "2.0.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/http-proxy-middleware/-/http-proxy-middleware-2.0.6.tgz", "integrity": "sha1-4aTdaXlXLHq1pOS1UJXR8yp0lj8=", "requires": {"@types/http-proxy": "^1.17.8", "http-proxy": "^1.18.1", "is-glob": "^4.0.1", "is-plain-obj": "^3.0.0", "micromatch": "^4.0.2"}}, "human-signals": {"version": "2.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/human-signals/-/human-signals-2.1.0.tgz", "integrity": "sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA="}, "iconv-lite": {"version": "0.4.24", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "ieee754": {"version": "1.2.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I="}, "ignore": {"version": "5.2.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ignore/-/ignore-5.2.4.tgz", "integrity": "sha1-opHAxheP8blgvv5H/N7DAWdKYyQ="}, "import-fresh": {"version": "3.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/import-fresh/-/import-fresh-3.3.0.tgz", "integrity": "sha1-NxYsJfy566oublPVtNiM4X2eDCs=", "requires": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}}, "imurmurhash": {"version": "0.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o="}, "inflight": {"version": "1.0.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/inherits/-/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="}, "inquirer": {"version": "8.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/inquirer/-/inquirer-8.2.0.tgz", "integrity": "sha1-9E8AjdNEu/xLMAMfRdmE4DSjrDo=", "requires": {"ansi-escapes": "^4.2.1", "chalk": "^4.1.1", "cli-cursor": "^3.1.0", "cli-width": "^3.0.0", "external-editor": "^3.0.3", "figures": "^3.0.0", "lodash": "^4.17.21", "mute-stream": "0.0.8", "ora": "^5.4.1", "run-async": "^2.4.0", "rxjs": "^7.2.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0", "through": "^2.3.6"}}, "ipaddr.js": {"version": "2.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ipaddr.js/-/ipaddr.js-2.0.1.tgz", "integrity": "sha1-7KJWp6h36Reus2iwp0l930LvgcA="}, "is-binary-path": {"version": "2.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-binary-path/-/is-binary-path-2.1.0.tgz", "integrity": "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=", "requires": {"binary-extensions": "^2.0.0"}}, "is-core-module": {"version": "2.12.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-core-module/-/is-core-module-2.12.0.tgz", "integrity": "sha1-Nq1i9vc8glP9ZHJRehJIPPA+fsQ=", "dev": true, "requires": {"has": "^1.0.3"}}, "is-docker": {"version": "2.2.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-docker/-/is-docker-2.2.1.tgz", "integrity": "sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao="}, "is-extglob": {"version": "2.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0="}, "is-glob": {"version": "4.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "requires": {"is-extglob": "^2.1.1"}}, "is-interactive": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-interactive/-/is-interactive-1.0.0.tgz", "integrity": "sha1-zqbmrlyHCnsKAAQHC3tYfgJSkS4="}, "is-number": {"version": "7.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-number/-/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss="}, "is-path-cwd": {"version": "2.2.0", "resolved": "https://registry.npmmirror.com/is-path-cwd/-/is-path-cwd-2.2.0.tgz", "integrity": "sha512-w942bTcih8fdJPJmQHFzkS76NEP8Kzzvmw92cXsazb8intwLqPibPPdXf4ANdKV3rYMuuQYGIWtvz9JilB3NFQ=="}, "is-path-in-cwd": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/is-path-in-cwd/-/is-path-in-cwd-2.1.0.tgz", "integrity": "sha512-rNocXHgipO+rvnP6dk3zI20RpOtrAM/kzbB258Uw5BWr3TpXi861yzjo16Dn4hUox07iw5AyeMLHWsujkjzvRQ==", "requires": {"is-path-inside": "^2.1.0"}}, "is-path-inside": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/is-path-inside/-/is-path-inside-2.1.0.tgz", "integrity": "sha512-wiyhTzfDWsvwAW53OBWF5zuvaOGlZ6PwYxAbPVDhpm+gM09xKQGjBq/8uYN12aDvMxnAnq3dxTyoSoRNmg5YFg==", "requires": {"path-is-inside": "^1.0.2"}}, "is-plain-obj": {"version": "3.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-plain-obj/-/is-plain-obj-3.0.0.tgz", "integrity": "sha1-r28uoUrFpkYYOlu9tbqrvBVq2dc="}, "is-plain-object": {"version": "2.0.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-plain-object/-/is-plain-object-2.0.4.tgz", "integrity": "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=", "requires": {"isobject": "^3.0.1"}}, "is-stream": {"version": "2.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-stream/-/is-stream-2.0.1.tgz", "integrity": "sha1-+sHj1TuXrVqdCunO8jifWBClwHc="}, "is-unicode-supported": {"version": "0.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz", "integrity": "sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc="}, "is-wsl": {"version": "2.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-wsl/-/is-wsl-2.2.0.tgz", "integrity": "sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=", "requires": {"is-docker": "^2.0.0"}}, "isarray": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="}, "isexe": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="}, "isobject": {"version": "3.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/isobject/-/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="}, "jest-worker": {"version": "27.5.1", "resolved": "https://registry.npmmirror.com/jest-worker/-/jest-worker-27.5.1.tgz", "integrity": "sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==", "requires": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "dependencies": {"supports-color": {"version": "8.1.1", "resolved": "https://registry.npmmirror.com/supports-color/-/supports-color-8.1.1.tgz", "integrity": "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==", "requires": {"has-flag": "^4.0.0"}}}}, "js-sdsl": {"version": "4.4.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/js-sdsl/-/js-sdsl-4.4.0.tgz", "integrity": "sha1-i0N9vmQtqpV2BAC2AjeO2P/qhDA="}, "js-tokens": {"version": "4.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk="}, "js-yaml": {"version": "4.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/js-yaml/-/js-yaml-4.1.0.tgz", "integrity": "sha1-wftl+PUBeQHN0slRhkuhhFihBgI=", "requires": {"argparse": "^2.0.1"}}, "jsesc": {"version": "2.5.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/jsesc/-/jsesc-2.5.2.tgz", "integrity": "sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q="}, "json-parse-even-better-errors": {"version": "2.3.1", "resolved": "https://registry.npmmirror.com/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w=="}, "json-schema-traverse": {"version": "0.4.1", "resolved": "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="}, "json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE="}, "json5": {"version": "2.2.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/json5/-/json5-2.2.3.tgz", "integrity": "sha1-eM1vGhm9wStz21rQxh79ZsHikoM="}, "jsonfile": {"version": "4.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/jsonfile/-/jsonfile-4.0.0.tgz", "integrity": "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=", "requires": {"graceful-fs": "^4.1.6"}}, "kind-of": {"version": "6.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/kind-of/-/kind-of-6.0.3.tgz", "integrity": "sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0="}, "launch-editor": {"version": "2.6.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/launch-editor/-/launch-editor-2.6.0.tgz", "integrity": "sha1-TAwaasEmxXK9n/mjDaHSyuZt79c=", "requires": {"picocolors": "^1.0.0", "shell-quote": "^1.7.3"}}, "lazystream": {"version": "1.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/lazystream/-/lazystream-1.0.1.tgz", "integrity": "sha1-SUyDEGLx+UCCUexE2xy6KSQqJjg=", "requires": {"readable-stream": "^2.0.5"}, "dependencies": {"readable-stream": {"version": "2.3.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/readable-stream/-/readable-stream-2.3.8.tgz", "integrity": "sha1-kRJegEK7obmIf0k0X2J3Anzovps=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "safe-buffer": {"version": "5.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="}, "string_decoder": {"version": "1.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "requires": {"safe-buffer": "~5.1.0"}}}}, "levn": {"version": "0.4.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/levn/-/levn-0.4.1.tgz", "integrity": "sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=", "requires": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}}, "linkify-it": {"version": "4.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/linkify-it/-/linkify-it-4.0.1.tgz", "integrity": "sha1-AfHV5QgZDQZmmYK6MafZ9WpXUew=", "requires": {"uc.micro": "^1.0.1"}}, "loader-runner": {"version": "4.3.0", "resolved": "https://registry.npmmirror.com/loader-runner/-/loader-runner-4.3.0.tgz", "integrity": "sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg=="}, "loader-utils": {"version": "2.0.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/loader-utils/-/loader-utils-2.0.4.tgz", "integrity": "sha1-i1yzi1w0qaAY7h/A5qBm0d/MUow=", "requires": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^2.1.2"}}, "locate-path": {"version": "6.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/locate-path/-/locate-path-6.0.0.tgz", "integrity": "sha1-VTIeswn+u8WcSAHZMackUqaB0oY=", "requires": {"p-locate": "^5.0.0"}}, "lodash": {"version": "4.17.21", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/lodash/-/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw="}, "lodash.defaults": {"version": "4.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/lodash.defaults/-/lodash.defaults-4.2.0.tgz", "integrity": "sha1-0JF4cW/+pN3p5ft7N/bwgCJ0WAw="}, "lodash.difference": {"version": "4.5.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/lodash.difference/-/lodash.difference-4.5.0.tgz", "integrity": "sha1-nMtOUF1Ia5FlE0V3KIWi3yf9AXw="}, "lodash.flatten": {"version": "4.4.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/lodash.flatten/-/lodash.flatten-4.4.0.tgz", "integrity": "sha1-8xwiIlqWMtK7+OSt2+8kCqdlph8="}, "lodash.isplainobject": {"version": "4.0.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz", "integrity": "sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs="}, "lodash.merge": {"version": "4.6.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/lodash.merge/-/lodash.merge-4.6.2.tgz", "integrity": "sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo="}, "lodash.union": {"version": "4.6.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/lodash.union/-/lodash.union-4.6.0.tgz", "integrity": "sha1-SLtQiECfFvGCFmZkHETdGqrjzYg="}, "log-symbols": {"version": "4.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/log-symbols/-/log-symbols-4.1.0.tgz", "integrity": "sha1-P727lbRoOsn8eFER55LlWNSr1QM=", "requires": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}, "dependencies": {"chalk": {"version": "4.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/chalk/-/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}}}, "loose-envify": {"version": "1.4.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/loose-envify/-/loose-envify-1.4.0.tgz", "integrity": "sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=", "requires": {"js-tokens": "^3.0.0 || ^4.0.0"}}, "lower-case": {"version": "2.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/lower-case/-/lower-case-2.0.2.tgz", "integrity": "sha1-b6I3xj29xKgsoP2ILkci3F5jTig=", "requires": {"tslib": "^2.0.3"}}, "lru-cache": {"version": "5.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=", "requires": {"yallist": "^3.0.2"}}, "make-dir": {"version": "3.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/make-dir/-/make-dir-3.1.0.tgz", "integrity": "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=", "requires": {"semver": "^6.0.0"}}, "markdown-it": {"version": "13.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/markdown-it/-/markdown-it-13.0.1.tgz", "integrity": "sha1-xuzEMcrPGl2lMUI/xqQoB4FK9DA=", "requires": {"argparse": "^2.0.1", "entities": "~3.0.1", "linkify-it": "^4.0.1", "mdurl": "^1.0.1", "uc.micro": "^1.0.5"}, "dependencies": {"entities": {"version": "3.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/entities/-/entities-3.0.1.tgz", "integrity": "sha1-K4h8piWF6W2zkDSC0zbBAGwwAdQ="}}}, "mdurl": {"version": "1.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/mdurl/-/mdurl-1.0.1.tgz", "integrity": "sha1-/oWy7HWlkDfyrf7BAP1sYBdhFS4="}, "media-typer": {"version": "0.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="}, "memfs": {"version": "3.5.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/memfs/-/memfs-3.5.1.tgz", "integrity": "sha1-8M0eK/rvWPb+Cb+5wiiPB/6gmew=", "requires": {"fs-monkey": "^1.0.3"}}, "merge-descriptors": {"version": "1.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "integrity": "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="}, "merge-stream": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/merge-stream/-/merge-stream-2.0.0.tgz", "integrity": "sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A="}, "methods": {"version": "1.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/methods/-/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="}, "micromatch": {"version": "4.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/micromatch/-/micromatch-4.0.5.tgz", "integrity": "sha1-vImZp8u/d83InxMvbkZwUbSQkMY=", "requires": {"braces": "^3.0.2", "picomatch": "^2.3.1"}}, "mime": {"version": "1.6.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/mime/-/mime-1.6.0.tgz", "integrity": "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE="}, "mime-db": {"version": "1.52.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha1-u6vNwChZ9JhzAchW4zh85exDv3A="}, "mime-types": {"version": "2.1.35", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=", "requires": {"mime-db": "1.52.0"}}, "mimic-fn": {"version": "2.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/mimic-fn/-/mimic-fn-2.1.0.tgz", "integrity": "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs="}, "mini-css-extract-plugin": {"version": "2.7.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/mini-css-extract-plugin/-/mini-css-extract-plugin-2.7.5.tgz", "integrity": "sha1-r7s0SXdlnsDx9uBQx66kVrEhz8U=", "requires": {"schema-utils": "^4.0.0"}, "dependencies": {"ajv": {"version": "8.12.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ajv/-/ajv-8.12.0.tgz", "integrity": "sha1-0aBScyPiL1NWLFZ8AJkVd9++GdE=", "requires": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}}, "ajv-keywords": {"version": "5.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ajv-keywords/-/ajv-keywords-5.1.0.tgz", "integrity": "sha1-adTThaRzPNvqtElkoRcKiPh/DhY=", "requires": {"fast-deep-equal": "^3.1.3"}}, "json-schema-traverse": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "integrity": "sha1-rnvLNlard6c7pcSb9lTzjmtoYOI="}, "schema-utils": {"version": "4.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/schema-utils/-/schema-utils-4.0.1.tgz", "integrity": "sha1-6y0ELfiwH0tcJ2ot/UG6D6q3Lo0=", "requires": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}}}}, "minimalistic-assert": {"version": "1.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz", "integrity": "sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc="}, "minimatch": {"version": "3.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/minimist/-/minimist-1.2.8.tgz", "integrity": "sha1-waRk52kzAuCCoHXO4MBXdBrEdyw="}, "mkdirp": {"version": "0.5.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/mkdirp/-/mkdirp-0.5.6.tgz", "integrity": "sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=", "requires": {"minimist": "^1.2.6"}}, "ms": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "multicast-dns": {"version": "7.2.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/multicast-dns/-/multicast-dns-7.2.5.tgz", "integrity": "sha1-d+tGBX9NetvRbZKQ+nKZ9vpkzO0=", "requires": {"dns-packet": "^5.2.2", "thunky": "^1.0.2"}}, "mute-stream": {"version": "0.0.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/mute-stream/-/mute-stream-0.0.8.tgz", "integrity": "sha1-FjDEKyJR/4HiooPelqVJfqkuXg0="}, "natural-compare": {"version": "1.4.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc="}, "negotiator": {"version": "0.6.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/negotiator/-/negotiator-0.6.3.tgz", "integrity": "sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0="}, "neo-async": {"version": "2.6.2", "resolved": "https://registry.npmmirror.com/neo-async/-/neo-async-2.6.2.tgz", "integrity": "sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw=="}, "no-case": {"version": "3.0.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/no-case/-/no-case-3.0.4.tgz", "integrity": "sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=", "requires": {"lower-case": "^2.0.2", "tslib": "^2.0.3"}}, "node-forge": {"version": "1.3.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/node-forge/-/node-forge-1.3.1.tgz", "integrity": "sha1-vo2iryQ7JBfV9kancGY6krfp3tM="}, "node-releases": {"version": "2.0.10", "resolved": "https://registry.npmmirror.com/node-releases/-/node-releases-2.0.10.tgz", "integrity": "sha512-5GFldHPXVG/YZmFzJvKK2zDSzPKhEp0+ZR5SVaoSag9fsL5YgHbUHDfnG5494ISANDcK4KwPXAx2xqVEydmd7w=="}, "normalize-path": {"version": "3.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/normalize-path/-/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU="}, "npm-run-path": {"version": "4.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/npm-run-path/-/npm-run-path-4.0.1.tgz", "integrity": "sha1-t+zR5e1T2o43pV4cImnguX7XSOo=", "requires": {"path-key": "^3.0.0"}}, "nth-check": {"version": "2.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/nth-check/-/nth-check-2.1.1.tgz", "integrity": "sha1-yeq0KO/842zWuSySS9sADvHx7R0=", "requires": {"boolbase": "^1.0.0"}}, "object-assign": {"version": "4.1.1", "resolved": "https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg=="}, "object-hash": {"version": "2.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object-hash/-/object-hash-2.2.0.tgz", "integrity": "sha1-WtUYWB7vxEO9djRyuP8unCwNVKU="}, "object-inspect": {"version": "1.12.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object-inspect/-/object-inspect-1.12.3.tgz", "integrity": "sha1-umLf/WfuJWyMCG365p4BbNHxmLk="}, "obuf": {"version": "1.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/obuf/-/obuf-1.1.2.tgz", "integrity": "sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4="}, "on-finished": {"version": "2.4.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/on-finished/-/on-finished-2.4.1.tgz", "integrity": "sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=", "requires": {"ee-first": "1.1.1"}}, "on-headers": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/on-headers/-/on-headers-1.0.2.tgz", "integrity": "sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8="}, "once": {"version": "1.4.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "requires": {"wrappy": "1"}}, "onetime": {"version": "5.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/onetime/-/onetime-5.1.2.tgz", "integrity": "sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=", "requires": {"mimic-fn": "^2.1.0"}}, "open": {"version": "8.4.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/open/-/open-8.4.2.tgz", "integrity": "sha1-W1/+Ko95Pc0qrXPlUMuHtZywhPk=", "requires": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}}, "optionator": {"version": "0.9.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/optionator/-/optionator-0.9.1.tgz", "integrity": "sha1-TyNqY3Pa4FZqbUPhMmZ09QwpFJk=", "requires": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.3"}}, "ora": {"version": "5.4.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ora/-/ora-5.4.1.tgz", "integrity": "sha1-GyZ4Qmr0rEpQkAjl5KyemVnbnhg=", "requires": {"bl": "^4.1.0", "chalk": "^4.1.0", "cli-cursor": "^3.1.0", "cli-spinners": "^2.5.0", "is-interactive": "^1.0.0", "is-unicode-supported": "^0.1.0", "log-symbols": "^4.1.0", "strip-ansi": "^6.0.0", "wcwidth": "^1.0.1"}, "dependencies": {"chalk": {"version": "4.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/chalk/-/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}}}, "os-tmpdir": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "integrity": "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ="}, "p-limit": {"version": "3.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/p-limit/-/p-limit-3.1.0.tgz", "integrity": "sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=", "requires": {"yocto-queue": "^0.1.0"}}, "p-locate": {"version": "5.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/p-locate/-/p-locate-5.0.0.tgz", "integrity": "sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=", "requires": {"p-limit": "^3.0.2"}}, "p-map": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/p-map/-/p-map-2.1.0.tgz", "integrity": "sha512-y3b8Kpd8OAN444hxfBbFfj1FY/RjtTd8tzYwhUqNYXx0fXx2iX4maP4Qr6qhIKbQXI02wTLAda4fYUbDagTUFw=="}, "p-retry": {"version": "4.6.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/p-retry/-/p-retry-4.6.2.tgz", "integrity": "sha1-m6rnGEBX7dThcjHO4EJkEG4JKhY=", "requires": {"@types/retry": "0.12.0", "retry": "^0.13.1"}}, "p-try": {"version": "2.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/p-try/-/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY="}, "param-case": {"version": "3.0.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/param-case/-/param-case-3.0.4.tgz", "integrity": "sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU=", "requires": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "parent-module": {"version": "1.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/parent-module/-/parent-module-1.0.1.tgz", "integrity": "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=", "requires": {"callsites": "^3.0.0"}}, "parseurl": {"version": "1.3.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ="}, "pascal-case": {"version": "3.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/pascal-case/-/pascal-case-3.1.2.tgz", "integrity": "sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs=", "requires": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "path-exists": {"version": "4.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM="}, "path-is-absolute": {"version": "1.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="}, "path-is-inside": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/path-is-inside/-/path-is-inside-1.0.2.tgz", "integrity": "sha512-DUWJr3+ULp4zXmol/SZkFf3JGsS9/SIv+Y3Rt93/UjPpDpklB5f1er4O3POIbUuUJ3FXgqte2Q7SrU6zAqwk8w=="}, "path-key": {"version": "3.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/path-key/-/path-key-3.1.1.tgz", "integrity": "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U="}, "path-parse": {"version": "1.0.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=", "dev": true}, "path-to-regexp": {"version": "0.1.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "integrity": "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="}, "picocolors": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/picocolors/-/picocolors-1.0.0.tgz", "integrity": "sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw="}, "picomatch": {"version": "2.3.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI="}, "pify": {"version": "4.0.1", "resolved": "https://registry.npmmirror.com/pify/-/pify-4.0.1.tgz", "integrity": "sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g=="}, "pinkie": {"version": "2.0.4", "resolved": "https://registry.npmmirror.com/pinkie/-/pinkie-2.0.4.tgz", "integrity": "sha512-MnUuEycAemtSaeFSjXKW/aroV7akBbY+Sv+RkyqFjgAe73F+MR0TBWKBRDkmfWq/HiFmdavfZ1G7h4SPZXaCSg=="}, "pinkie-promise": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/pinkie-promise/-/pinkie-promise-2.0.1.tgz", "integrity": "sha512-0Gni6D4UcLTbv9c57DfxDGdr41XfgUjqWZu492f0cIGr16zDU06BWP/RAEvOuo7CQ0CNjHaLlM59YJJFm3NWlw==", "requires": {"pinkie": "^2.0.0"}}, "pkg-dir": {"version": "4.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/pkg-dir/-/pkg-dir-4.2.0.tgz", "integrity": "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=", "requires": {"find-up": "^4.0.0"}, "dependencies": {"find-up": {"version": "4.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/find-up/-/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}, "locate-path": {"version": "5.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "requires": {"p-locate": "^4.1.0"}}, "p-limit": {"version": "2.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "4.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "requires": {"p-limit": "^2.2.0"}}}}, "portfinder": {"version": "1.0.28", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/portfinder/-/portfinder-1.0.28.tgz", "integrity": "sha1-Z8RiKFK9U3TdHdkA93n1NGL6x3g=", "requires": {"async": "^2.6.2", "debug": "^3.1.1", "mkdirp": "^0.5.5"}, "dependencies": {"debug": {"version": "3.2.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/debug/-/debug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "requires": {"ms": "^2.1.1"}}, "ms": {"version": "2.1.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ms/-/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI="}}}, "prelude-ls": {"version": "1.2.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/prelude-ls/-/prelude-ls-1.2.1.tgz", "integrity": "sha1-3rxkidem5rDnYRiIzsiAM30xY5Y="}, "pretty-error": {"version": "4.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/pretty-error/-/pretty-error-4.0.0.tgz", "integrity": "sha1-kKcD9G3XI0rbRtD4SCPp0cuPENY=", "requires": {"lodash": "^4.17.20", "renderkid": "^3.0.0"}}, "process-nextick-args": {"version": "2.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha1-eCDZsWEgzFXKmud5JoCufbptf+I="}, "proxy-addr": {"version": "2.0.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/proxy-addr/-/proxy-addr-2.0.7.tgz", "integrity": "sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=", "requires": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "dependencies": {"ipaddr.js": {"version": "1.9.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM="}}}, "proxy-from-env": {"version": "1.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "integrity": "sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I="}, "punycode": {"version": "2.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/punycode/-/punycode-2.3.0.tgz", "integrity": "sha1-9n+mfJTaj00M//mBruQRgGQZm48="}, "qs": {"version": "6.11.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/qs/-/qs-6.11.0.tgz", "integrity": "sha1-/Q2WNEb3pl4TZ+AavYVClFPww3o=", "requires": {"side-channel": "^1.0.4"}}, "queue-microtask": {"version": "1.2.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha1-SSkii7xyTfrEPg77BYyve2z7YkM="}, "randombytes": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/randombytes/-/randombytes-2.1.0.tgz", "integrity": "sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==", "requires": {"safe-buffer": "^5.1.0"}}, "range-parser": {"version": "1.2.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE="}, "raw-body": {"version": "2.5.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/raw-body/-/raw-body-2.5.1.tgz", "integrity": "sha1-/hsWKLGBtwAhXl/UI4n5i3E5KFc=", "requires": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "dependencies": {"bytes": {"version": "3.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/bytes/-/bytes-3.1.2.tgz", "integrity": "sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU="}}}, "react": {"version": "18.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/react/-/react-18.2.0.tgz", "integrity": "sha1-VVvZhZKIMlX6AN4U8RUakXtdd9U=", "requires": {"loose-envify": "^1.1.0"}}, "react-dom": {"version": "18.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/react-dom/-/react-dom-18.2.0.tgz", "integrity": "sha1-IqrzhwjbJnTtmtoiTKSqcI2CHj0=", "requires": {"loose-envify": "^1.1.0", "scheduler": "^0.23.0"}}, "readable-stream": {"version": "3.6.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/readable-stream/-/readable-stream-3.6.2.tgz", "integrity": "sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=", "requires": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}}, "readdir-glob": {"version": "1.1.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/readdir-glob/-/readdir-glob-1.1.3.tgz", "integrity": "sha1-w9gx9R9ee/pi+i/75LUIxkDwlYQ=", "requires": {"minimatch": "^5.1.0"}, "dependencies": {"brace-expansion": {"version": "2.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/brace-expansion/-/brace-expansion-2.0.1.tgz", "integrity": "sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=", "requires": {"balanced-match": "^1.0.0"}}, "minimatch": {"version": "5.1.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/minimatch/-/minimatch-5.1.6.tgz", "integrity": "sha1-HPy4z1Ui6mmVLNKvla4JR38SKpY=", "requires": {"brace-expansion": "^2.0.1"}}}}, "readdirp": {"version": "3.6.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/readdirp/-/readdirp-3.6.0.tgz", "integrity": "sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=", "requires": {"picomatch": "^2.2.1"}}, "regexpp": {"version": "3.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/regexpp/-/regexpp-3.2.0.tgz", "integrity": "sha1-BCWido2PI7rXDKS5BGH6LxIT4bI=", "dev": true}, "relateurl": {"version": "0.2.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/relateurl/-/relateurl-0.2.7.tgz", "integrity": "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk="}, "renderkid": {"version": "3.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/renderkid/-/renderkid-3.0.0.tgz", "integrity": "sha1-X9gj5NaVHTc1jsyaWLHwaDa2Joo=", "requires": {"css-select": "^4.1.3", "dom-converter": "^0.2.0", "htmlparser2": "^6.1.0", "lodash": "^4.17.21", "strip-ansi": "^6.0.1"}}, "require-directory": {"version": "2.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I="}, "require-from-string": {"version": "2.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/require-from-string/-/require-from-string-2.0.2.tgz", "integrity": "sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk="}, "requires-port": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/requires-port/-/requires-port-1.0.0.tgz", "integrity": "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8="}, "resolve": {"version": "1.22.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/resolve/-/resolve-1.22.2.tgz", "integrity": "sha1-DtCUPU4wGGeVV2bJ8+GubQHGhF8=", "dev": true, "requires": {"is-core-module": "^2.11.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}}, "resolve-from": {"version": "4.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/resolve-from/-/resolve-from-4.0.0.tgz", "integrity": "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY="}, "restore-cursor": {"version": "3.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/restore-cursor/-/restore-cursor-3.1.0.tgz", "integrity": "sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=", "requires": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}}, "retry": {"version": "0.13.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/retry/-/retry-0.13.1.tgz", "integrity": "sha1-GFsVh6z2eRnWOzVzSeA1N7JIRlg="}, "reusify": {"version": "1.0.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/reusify/-/reusify-1.0.4.tgz", "integrity": "sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY="}, "rimraf": {"version": "3.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/rimraf/-/rimraf-3.0.2.tgz", "integrity": "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=", "requires": {"glob": "^7.1.3"}}, "run-async": {"version": "2.4.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/run-async/-/run-async-2.4.1.tgz", "integrity": "sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU="}, "run-parallel": {"version": "1.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/run-parallel/-/run-parallel-1.2.0.tgz", "integrity": "sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=", "requires": {"queue-microtask": "^1.2.2"}}, "rxjs": {"version": "7.8.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/rxjs/-/rxjs-7.8.1.tgz", "integrity": "sha1-b289meqARCke/ZLnx/z1YsQFdUM=", "requires": {"tslib": "^2.1.0"}}, "safe-buffer": {"version": "5.2.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY="}, "safer-buffer": {"version": "2.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="}, "scheduler": {"version": "0.23.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha1-uoBBr8PTDrIGpIe2s4QALk5h/f4=", "requires": {"loose-envify": "^1.1.0"}}, "schema-utils": {"version": "3.1.2", "resolved": "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.1.2.tgz", "integrity": "sha512-pvjEHOgWc9OWA/f/DE3ohBWTD6EleVLf7iFUkoSwAxttdBhB9QUebQgxER2kWueOvRJXPHNnyrvvh9eZINB8Eg==", "requires": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}}, "select-hose": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/select-hose/-/select-hose-2.0.0.tgz", "integrity": "sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo="}, "selfsigned": {"version": "2.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/selfsigned/-/selfsigned-2.1.1.tgz", "integrity": "sha1-GKdhPXFMDNM4XEivAHWr8/Jmr2E=", "requires": {"node-forge": "^1"}}, "semver": {"version": "6.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="}, "send": {"version": "0.18.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/send/-/send-0.18.0.tgz", "integrity": "sha1-ZwFnzGVLBfWqSnZ/kRO7NxvHBr4=", "requires": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "dependencies": {"ms": {"version": "2.1.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ms/-/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI="}}}, "serialize-javascript": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/serialize-javascript/-/serialize-javascript-6.0.1.tgz", "integrity": "sha512-owoXEFjWRllis8/M1Q+Cw5k8ZH40e3zhp/ovX+Xr/vi1qj6QesbyXXViFbpNvWvPNAD62SutwEXavefrLJWj7w==", "requires": {"randombytes": "^2.1.0"}}, "serve-index": {"version": "1.9.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/serve-index/-/serve-index-1.9.1.tgz", "integrity": "sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=", "requires": {"accepts": "~1.3.4", "batch": "0.6.1", "debug": "2.6.9", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "mime-types": "~2.1.17", "parseurl": "~1.3.2"}, "dependencies": {"depd": {"version": "1.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/depd/-/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="}, "http-errors": {"version": "1.6.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/http-errors/-/http-errors-1.6.3.tgz", "integrity": "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=", "requires": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}}, "inherits": {"version": "2.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="}, "setprototypeof": {"version": "1.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/setprototypeof/-/setprototypeof-1.1.0.tgz", "integrity": "sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY="}, "statuses": {"version": "1.5.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/statuses/-/statuses-1.5.0.tgz", "integrity": "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="}}}, "serve-static": {"version": "1.15.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/serve-static/-/serve-static-1.15.0.tgz", "integrity": "sha1-+q7wjP/goaYvYMrQxOUTz/CslUA=", "requires": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.18.0"}}, "setprototypeof": {"version": "1.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/setprototypeof/-/setprototypeof-1.2.0.tgz", "integrity": "sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ="}, "shallow-clone": {"version": "3.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/shallow-clone/-/shallow-clone-3.0.1.tgz", "integrity": "sha1-jymBrZJTH1UDWwH7IwdppA4C76M=", "requires": {"kind-of": "^6.0.2"}}, "shebang-command": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=", "requires": {"shebang-regex": "^3.0.0"}}, "shebang-regex": {"version": "3.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI="}, "shell-quote": {"version": "1.8.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/shell-quote/-/shell-quote-1.8.1.tgz", "integrity": "sha1-bb9Nt1UVrVusY7TxiUw6FUx2ZoA="}, "side-channel": {"version": "1.0.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/side-channel/-/side-channel-1.0.4.tgz", "integrity": "sha1-785cj9wQTudRslxY1CkAEfpeos8=", "requires": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}}, "signal-exit": {"version": "3.0.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/signal-exit/-/signal-exit-3.0.7.tgz", "integrity": "sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk="}, "sockjs": {"version": "0.3.24", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/sockjs/-/sockjs-0.3.24.tgz", "integrity": "sha1-ybyJlfM6ERvqA5XsMKoyBr21zM4=", "requires": {"faye-websocket": "^0.11.3", "uuid": "^8.3.2", "websocket-driver": "^0.7.4"}}, "source-map": {"version": "0.6.1", "resolved": "https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="}, "source-map-support": {"version": "0.5.21", "resolved": "https://registry.npmmirror.com/source-map-support/-/source-map-support-0.5.21.tgz", "integrity": "sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==", "requires": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "spdy": {"version": "4.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/spdy/-/spdy-4.0.2.tgz", "integrity": "sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=", "requires": {"debug": "^4.1.0", "handle-thing": "^2.0.0", "http-deceiver": "^1.2.7", "select-hose": "^2.0.0", "spdy-transport": "^3.0.0"}, "dependencies": {"debug": {"version": "4.3.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/debug/-/debug-4.3.4.tgz", "integrity": "sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=", "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}}}, "spdy-transport": {"version": "3.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/spdy-transport/-/spdy-transport-3.0.0.tgz", "integrity": "sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=", "requires": {"debug": "^4.1.0", "detect-node": "^2.0.4", "hpack.js": "^2.1.6", "obuf": "^1.1.2", "readable-stream": "^3.0.6", "wbuf": "^1.7.3"}, "dependencies": {"debug": {"version": "4.3.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/debug/-/debug-4.3.4.tgz", "integrity": "sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=", "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}}}, "statuses": {"version": "2.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/statuses/-/statuses-2.0.1.tgz", "integrity": "sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M="}, "string-width": {"version": "4.2.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string-width/-/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "string_decoder": {"version": "1.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=", "requires": {"safe-buffer": "~5.2.0"}}, "strip-ansi": {"version": "6.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "requires": {"ansi-regex": "^5.0.1"}}, "strip-final-newline": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/strip-final-newline/-/strip-final-newline-2.0.0.tgz", "integrity": "sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0="}, "strip-json-comments": {"version": "3.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "integrity": "sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY="}, "supports-color": {"version": "7.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}, "supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha1-btpL00SjyUrqN21MwxvHcxEDngk=", "dev": true}, "tapable": {"version": "2.2.1", "resolved": "https://registry.npmmirror.com/tapable/-/tapable-2.2.1.tgz", "integrity": "sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ=="}, "tar-stream": {"version": "2.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/tar-stream/-/tar-stream-2.2.0.tgz", "integrity": "sha1-rK2EwoQTawYNw/qmRHSqmuvXcoc=", "requires": {"bl": "^4.0.3", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}}, "terser": {"version": "5.17.1", "resolved": "https://registry.npmmirror.com/terser/-/terser-5.17.1.tgz", "integrity": "sha512-hVl35zClmpisy6oaoKALOpS0rDYLxRFLHhRuDlEGTKey9qHjS1w9GMORjuwIMt70Wan4lwsLYyWDVnWgF+KUEw==", "requires": {"@jridgewell/source-map": "^0.3.2", "acorn": "^8.5.0", "commander": "^2.20.0", "source-map-support": "~0.5.20"}}, "terser-webpack-plugin": {"version": "5.3.7", "resolved": "https://registry.npmmirror.com/terser-webpack-plugin/-/terser-webpack-plugin-5.3.7.tgz", "integrity": "sha512-AfKwIktyP7Cu50xNjXF/6Qb5lBNzYaWpU6YfoX3uZicTx0zTy0stDDCsvjDapKsSDvOeWo5MEq4TmdBy2cNoHw==", "requires": {"@jridgewell/trace-mapping": "^0.3.17", "jest-worker": "^27.4.5", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.1", "terser": "^5.16.5"}}, "text-table": {"version": "0.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/text-table/-/text-table-0.2.0.tgz", "integrity": "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ="}, "through": {"version": "2.3.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/through/-/through-2.3.8.tgz", "integrity": "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU="}, "thunky": {"version": "1.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/thunky/-/thunky-1.1.0.tgz", "integrity": "sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30="}, "tmp": {"version": "0.0.33", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/tmp/-/tmp-0.0.33.tgz", "integrity": "sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=", "requires": {"os-tmpdir": "~1.0.2"}}, "to-fast-properties": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/to-fast-properties/-/to-fast-properties-2.0.0.tgz", "integrity": "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4="}, "to-regex-range": {"version": "5.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "requires": {"is-number": "^7.0.0"}}, "toidentifier": {"version": "1.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/toidentifier/-/toidentifier-1.0.1.tgz", "integrity": "sha1-O+NDIaiKgg7RvYDfqjPkefu43TU="}, "tslib": {"version": "2.5.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/tslib/-/tslib-2.5.0.tgz", "integrity": "sha1-Qr/thvV4eutB0DGGbI9AJCng/d8="}, "type-check": {"version": "0.4.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/type-check/-/type-check-0.4.0.tgz", "integrity": "sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=", "requires": {"prelude-ls": "^1.2.1"}}, "type-fest": {"version": "0.20.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/type-fest/-/type-fest-0.20.2.tgz", "integrity": "sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ="}, "type-is": {"version": "1.6.18", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/type-is/-/type-is-1.6.18.tgz", "integrity": "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=", "requires": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}}, "uc.micro": {"version": "1.0.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/uc.micro/-/uc.micro-1.0.6.tgz", "integrity": "sha1-nEEagCpAmpH8bPdAgbq6NLJEmaw="}, "universalify": {"version": "0.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/universalify/-/universalify-0.1.2.tgz", "integrity": "sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY="}, "unpipe": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="}, "update-browserslist-db": {"version": "1.0.11", "resolved": "https://registry.npmmirror.com/update-browserslist-db/-/update-browserslist-db-1.0.11.tgz", "integrity": "sha512-dCwEFf0/oT85M1fHBg4F0jtLwJrutGoHSQXCh7u4o2t1drG+c0a9Flnqww6XUKSfQMPpJBRjU8d4RXB09qtvaA==", "requires": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}}, "uri-js": {"version": "4.4.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=", "requires": {"punycode": "^2.1.0"}}, "util-deprecate": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="}, "utila": {"version": "0.4.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/utila/-/utila-0.4.0.tgz", "integrity": "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw="}, "utils-merge": {"version": "1.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="}, "uuid": {"version": "8.3.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/uuid/-/uuid-8.3.2.tgz", "integrity": "sha1-gNW1ztJxu5r2xEXyGhoExgbO++I="}, "vary": {"version": "1.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="}, "watchpack": {"version": "2.4.0", "resolved": "https://registry.npmmirror.com/watchpack/-/watchpack-2.4.0.tgz", "integrity": "sha512-Lcvm7MGST/4fup+ifyKi2hjyIAwcdI4HRgtvTpIUxBRhB+RFtUh8XtDOxUfctVCnhVi+QQj49i91OyvzkJl6cg==", "requires": {"glob-to-regexp": "^0.4.1", "graceful-fs": "^4.1.2"}}, "wbuf": {"version": "1.7.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/wbuf/-/wbuf-1.7.3.tgz", "integrity": "sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=", "requires": {"minimalistic-assert": "^1.0.0"}}, "wcwidth": {"version": "1.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/wcwidth/-/wcwidth-1.0.1.tgz", "integrity": "sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=", "requires": {"defaults": "^1.0.3"}}, "webpack": {"version": "5.81.0", "resolved": "https://registry.npmmirror.com/webpack/-/webpack-5.81.0.tgz", "integrity": "sha512-AAjaJ9S4hYCVODKLQTgG5p5e11hiMawBwV2v8MYLE0C/6UAGLuAF4n1qa9GOwdxnicaP+5k6M5HrLmD4+gIB8Q==", "requires": {"@types/eslint-scope": "^3.7.3", "@types/estree": "^1.0.0", "@webassemblyjs/ast": "^1.11.5", "@webassemblyjs/wasm-edit": "^1.11.5", "@webassemblyjs/wasm-parser": "^1.11.5", "acorn": "^8.7.1", "acorn-import-assertions": "^1.7.6", "browserslist": "^4.14.5", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^5.13.0", "es-module-lexer": "^1.2.1", "eslint-scope": "5.1.1", "events": "^3.2.0", "glob-to-regexp": "^0.4.1", "graceful-fs": "^4.2.9", "json-parse-even-better-errors": "^2.3.1", "loader-runner": "^4.2.0", "mime-types": "^2.1.27", "neo-async": "^2.6.2", "schema-utils": "^3.1.2", "tapable": "^2.1.1", "terser-webpack-plugin": "^5.3.7", "watchpack": "^2.4.0", "webpack-sources": "^3.2.3"}}, "webpack-dev-middleware": {"version": "5.3.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/webpack-dev-middleware/-/webpack-dev-middleware-5.3.3.tgz", "integrity": "sha1-765nwnk5COcxHx2bBvKgjcyX5R8=", "requires": {"colorette": "^2.0.10", "memfs": "^3.4.3", "mime-types": "^2.1.31", "range-parser": "^1.2.1", "schema-utils": "^4.0.0"}, "dependencies": {"ajv": {"version": "8.12.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ajv/-/ajv-8.12.0.tgz", "integrity": "sha1-0aBScyPiL1NWLFZ8AJkVd9++GdE=", "requires": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}}, "ajv-keywords": {"version": "5.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ajv-keywords/-/ajv-keywords-5.1.0.tgz", "integrity": "sha1-adTThaRzPNvqtElkoRcKiPh/DhY=", "requires": {"fast-deep-equal": "^3.1.3"}}, "json-schema-traverse": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "integrity": "sha1-rnvLNlard6c7pcSb9lTzjmtoYOI="}, "schema-utils": {"version": "4.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/schema-utils/-/schema-utils-4.0.1.tgz", "integrity": "sha1-6y0ELfiwH0tcJ2ot/UG6D6q3Lo0=", "requires": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}}}}, "webpack-dev-server": {"version": "4.13.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/webpack-dev-server/-/webpack-dev-server-4.13.3.tgz", "integrity": "sha1-n+t0C4tWuIYmC64TYChoGKIhuug=", "requires": {"@types/bonjour": "^3.5.9", "@types/connect-history-api-fallback": "^1.3.5", "@types/express": "^4.17.13", "@types/serve-index": "^1.9.1", "@types/serve-static": "^1.13.10", "@types/sockjs": "^0.3.33", "@types/ws": "^8.5.1", "ansi-html-community": "^0.0.8", "bonjour-service": "^1.0.11", "chokidar": "^3.5.3", "colorette": "^2.0.10", "compression": "^1.7.4", "connect-history-api-fallback": "^2.0.0", "default-gateway": "^6.0.3", "express": "^4.17.3", "graceful-fs": "^4.2.6", "html-entities": "^2.3.2", "http-proxy-middleware": "^2.0.3", "ipaddr.js": "^2.0.1", "launch-editor": "^2.6.0", "open": "^8.0.9", "p-retry": "^4.5.0", "rimraf": "^3.0.2", "schema-utils": "^4.0.0", "selfsigned": "^2.1.1", "serve-index": "^1.9.1", "sockjs": "^0.3.24", "spdy": "^4.0.2", "webpack-dev-middleware": "^5.3.1", "ws": "^8.13.0"}, "dependencies": {"ajv": {"version": "8.12.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ajv/-/ajv-8.12.0.tgz", "integrity": "sha1-0aBScyPiL1NWLFZ8AJkVd9++GdE=", "requires": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}}, "ajv-keywords": {"version": "5.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ajv-keywords/-/ajv-keywords-5.1.0.tgz", "integrity": "sha1-adTThaRzPNvqtElkoRcKiPh/DhY=", "requires": {"fast-deep-equal": "^3.1.3"}}, "json-schema-traverse": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "integrity": "sha1-rnvLNlard6c7pcSb9lTzjmtoYOI="}, "schema-utils": {"version": "4.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/schema-utils/-/schema-utils-4.0.1.tgz", "integrity": "sha1-6y0ELfiwH0tcJ2ot/UG6D6q3Lo0=", "requires": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}}}}, "webpack-merge": {"version": "5.8.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/webpack-merge/-/webpack-merge-5.8.0.tgz", "integrity": "sha1-Kznb8ir4d3atdEw5AiNzHTCmj2E=", "requires": {"clone-deep": "^4.0.1", "wildcard": "^2.0.0"}}, "webpack-sources": {"version": "3.2.3", "resolved": "https://registry.npmmirror.com/webpack-sources/-/webpack-sources-3.2.3.tgz", "integrity": "sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w=="}, "websocket-driver": {"version": "0.7.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/websocket-driver/-/websocket-driver-0.7.4.tgz", "integrity": "sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=", "requires": {"http-parser-js": ">=0.5.1", "safe-buffer": ">=5.1.0", "websocket-extensions": ">=0.1.1"}}, "websocket-extensions": {"version": "0.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/websocket-extensions/-/websocket-extensions-0.1.4.tgz", "integrity": "sha1-f4RzvIOd/YdgituV1+sHUhFXikI="}, "which": {"version": "2.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/which/-/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "requires": {"isexe": "^2.0.0"}}, "wildcard": {"version": "2.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/wildcard/-/wildcard-2.0.1.tgz", "integrity": "sha1-WrENAkhxmJVINrY0n3T/+WHhD2c="}, "word-wrap": {"version": "1.2.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/word-wrap/-/word-wrap-1.2.3.tgz", "integrity": "sha1-YQY29rH3A4kb00dxzLF/uTtHB5w="}, "wrap-ansi": {"version": "7.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "requires": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}}, "wrappy": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="}, "ws": {"version": "8.13.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ws/-/ws-8.13.0.tgz", "integrity": "sha1-mp+5L5PPQVEqBzXI9N0JuKEhHNA="}, "y18n": {"version": "5.0.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/y18n/-/y18n-5.0.8.tgz", "integrity": "sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU="}, "yallist": {"version": "3.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/yallist/-/yallist-3.1.1.tgz", "integrity": "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0="}, "yargs": {"version": "17.7.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/yargs/-/yargs-17.7.1.tgz", "integrity": "sha1-NKd2RSAdGo/FITrOeHwiDqu9CWc=", "requires": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}}, "yargs-parser": {"version": "21.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/yargs-parser/-/yargs-parser-21.1.1.tgz", "integrity": "sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU="}, "yocto-queue": {"version": "0.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/yocto-queue/-/yocto-queue-0.1.0.tgz", "integrity": "sha1-ApTrPe4FAo0x7hpfosVWpqrxChs="}, "zip-stream": {"version": "4.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/zip-stream/-/zip-stream-4.1.0.tgz", "integrity": "sha1-Ud0yZXFUTjaqP3VkMLMTV23I/Hk=", "requires": {"archiver-utils": "^2.1.0", "compress-commons": "^4.1.0", "readable-stream": "^3.6.0"}}}}
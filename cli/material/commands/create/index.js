const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { findPackageJsonPwd } = require('../../utils/utils');
const readdir = promisify(fs.readdir);
const mkdir = promisify(fs.mkdir);
const copyFile = promisify(fs.copyFile);
const stat = promisify(fs.stat);

async function copyFolder(sourcePath, destinationPath) {
  try {
    // 创建目标文件夹
    await mkdir(destinationPath, { recursive: true });

    // 读取源文件夹中的所有文件和子文件夹
    const files = await readdir(sourcePath);

    // 遍历文件和子文件夹
    for (const file of files) {
      const sourceFilePath = path.join(sourcePath, file);
      const destinationFilePath = path.join(destinationPath, file);

      // 获取文件/文件夹的信息
      const fileStat = await stat(sourceFilePath);

      if (fileStat.isFile()) {
        // 如果是文件，则直接复制到目标文件夹
        await copyFile(sourceFilePath, destinationFilePath);
      } else if (fileStat.isDirectory()) {
        // 如果是文件夹，则递归调用复制文件夹函数
        await copyFolder(sourceFilePath, destinationFilePath);
      }
    }

  } catch (error) {
    console.error('创建失败', error);
    return
  }
}

module.exports = async function create(argv) {
  const { name } = argv;
  const context = findPackageJsonPwd(process.cwd());

  // 文件是否存在
  if (!fs.existsSync(path.resolve(context, 'material.json'))) {
    return console.log('当前请在根目录下执行create命令');
  }

  // 当前项目已存在
  if (fs.existsSync(path.resolve(context, name))) {
    return console.log('当前项目已存在');
  }

  // TODO: 需要在创建的时候根据命令行来修改某些字段

  await copyFolder(path.resolve(__dirname, '../../template'), path.resolve(context, name))
  console.log('创建成功')

  // TODO: 进入到项目中开始安装依赖
};

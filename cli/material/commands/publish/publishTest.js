const path = require('path');
const fs = require('fs');
const git = require('../../utils/git');
const execa = require('execa');
const { build } = require('../../core/build/index');
const upload = require('../../core/upload/index');
const chalk = require('chalk');
const ora = require('ora');
const inquirer = require('inquirer');
const { readJson, getUpdateVersion, version } = require('../../utils/utils');


module.exports = async function publishTest(argv, branchs, context, mConfig) {
  let spinner = null
    // 获取版本号信息
    // 获取生产环境的版本号列表
    // 获取测试环境的版本号列表

    // 发布测试
    if (argv.test !== 'update' && argv.test !== 'add') {
      return console.log('test参数只能是update或者add')
    }

    // 这两个变量也只有在新增版本的时候才会有值
    let updateVersion // 最新的版本号
    let versionType // 版本的更新类型

    if (argv.test === 'update') {
      console.log(`\n${chalk.green('===========测试发布【更新】===========')}\n`)
      // 更新的条件，只有存在测试环境的版本号比生产环境的版本号多一个，并且多的那个版本恰好是当前版本
      
      
      // 更新当前版本
      // TODO: 判断当前组件，当前版本是否有发送到生产，如果发到生产
      // 则无法使用更新，只能新增版本，询问是否需要新增版本，如果选择是
      // 则开始执行新增逻辑

      // 如果当前可以进行更新，则运行更新逻辑
      // 与生产发布基本一致，但是有一些区别：
      // 1. 不需要校验该分支是否已经发布到测试环境
      // 2. 发布之后只需要推送当前分支即可，不需要合并master
    } else {
      console.log(`\n${chalk.green('===========测试发布【新增】===========')}\n`)
      // 当前组件，生产版本与测试版本的版本列表完全一致，才能进行新增版本
      // 只能继续修改当前版本

      // 1. 选择最新的版本号
      // 2. 使用新的版本号进行上传
      // 3. 上传成功之后，更新当前版本号

      // 询问想要更新的版本号
      const versions = getUpdateVersion(mConfig.version)

      versionType = await inquirer
        .prompt([
          {
            type: 'list',
            name: 'reptile',
            message: '选择更新类型（版本）',
            choices: [
              {
                name: `版本升级${versions[0]}`,
                value: 0,
              },
              {
                name: `特性更新${versions[1]}`,
                value: 1,
              },
              {
                name: `修订补丁${versions[2]}`,
                value: 2,
              },
            ],
          },
        ])
        .then(answers => answers.reptile);

      mConfig.version = versions[versionType]
      updateVersion = versions[versionType] // 最新的版本号
    }

    // 获取当前的git hash值

    try {
      // 测试发布的组件构建与上传
      spinner = ora('构建组件及文档')
    
      spinner.start()
      await build(context, mConfig)
      spinner.succeed(chalk.green(`构建组件及文档`))
  
  
      // 压缩上传，上传到测试环境
      spinner = ora('组件上传')
  
      spinner.start()

      const folderPath = path.resolve(context, 'component-dist', `${mConfig.name}@${mConfig.version}`)
      const gitHash = await execa('git', ['rev-parse', 'HEAD'])
      await upload(folderPath, {
        ...mConfig,
        // alias: mConfig.cname,
        gitHash: gitHash.stdout.slice(0, 16)
      }, 'test')
      await execa('rm', ['-rf', `${folderPath}.zip`])
      spinner.succeed(chalk.green(`上传成功`))

      // 如果有更新版本号时，说明进行了版本更新，所以需要先提交版本号更新的代码
      if (updateVersion) {
        // 如果有更新的版本号，需要等到上传成功之后再进行更新
        spinner = ora('更新版本号')
    
        spinner.start()
        await version(context, versionType)
        await execa('git', ['add', '.'])
        await execa('git', ['commit', '-m', `【MIFY BUILD】${mConfig.name}@${mConfig.version}`])
  
        spinner.succeed(chalk.green(`更新后版本${updateVersion}`))
      }
  
      spinner = ora('代码提交')
      spinner.start()
      await execa('git', ['push', 'origin', branchs.currentBranch])
  
      spinner.succeed(chalk.green('代码提交成功'))
    } catch(err) {
      console.log('err', err)
      return spinner.fail(chalk.red(err.message))
    }
}
const path = require('path');
const fs = require('fs');
const git = require('../../utils/git');
const execa = require('execa');
const { build } = require('../../core/build/index');
const upload = require('../../core/upload/index');
const chalk = require('chalk');
const ora = require('ora');
const inquirer = require('inquirer');
const { readJson, getUpdateVersion, version } = require('../../utils/utils');
const api = require('../../request/api');


module.exports = async function publishProd(branchs, context, mConfig) {
  let spinner = null

  try {
    console.log(`\n${chalk.green('===============生产发布===============')}\n`)
    // 校验当前节点，git的hash值，是否已经发布到测试环境
    spinner = ora('校验是否发布到测试')

    spinner.start()
    let gitHash = await execa('git', ['rev-parse', 'HEAD'])
    gitHash = gitHash.stdout.slice(0, 16)

    const testComponent = await api.getComponentVersionList({
      name: mConfig.name,
    }, 'test')

    const prodComponent = await api.getComponentVersionList({
      name: mConfig.name,
    }, 'prod')

    // 如果在测试环境找不到对应的githash 则表示对应的节点没有发到测试环境
    if (testComponent.list.find(item => item.gitHash === gitHash)) {
      spinner.succeed(chalk.green(`已经发布到测试`))
    } else {
      return spinner.fail(chalk.red(`当前节点未发布到测试环境，请先发布测试环境`))
    }

    if (prodComponent.list.find(item => item.gitHash === gitHash)) {
      return spinner.fail(chalk.red(`当前节点已经发布到生产环境`))
    }

    spinner = ora('构建组件及文档')

    spinner.start()
    await build(context, mConfig)
    spinner.succeed(chalk.green(`构建组件及文档`))

    try {
      // 压缩上传，上传到生产环境
      spinner = ora('组件上传')
      spinner.start()
      const folderPath = path.resolve(context, 'component-dist', `${mConfig.name}@${mConfig.version}`)
      await upload(folderPath, {
        ...mConfig,
        gitHash
      }, 'prod')
  
      // 上传完之后删除zip
      await execa('rm', ['-rf', `${folderPath}.zip`])
      spinner.succeed(chalk.green(`上传成功`))
    } catch(err) {
      console.log('err', err)
      spinner.fail(chalk.red(`组件上传 ${err}`))
      return
    }


    // TODO: 先将生产的上传注销
    // try {
    //   const folderPath = path.resolve(context, 'component-dist', `${mConfig.name}@${mConfig.version}`)
    //   await upload(folderPath, mConfig)
      
    //   spinner.succeed(chalk.green(`上传成功`))
    // } catch(err) {
    //   console.log('err', err)
    //   spinner.fail(chalk.red(err.message))
    // }
    
  } catch(err) {
    spinner.fail(chalk.red(err.message))
    return
  }

  spinner = ora('代码提交')
  spinner.start()
  await execa('git', ['push', 'origin', branchs.currentBranch])
  await execa('git', ['push', 'origin', `${branchs.currentBranch}:feature/master`])

  spinner.succeed(chalk.green('代码提交成功'))
}

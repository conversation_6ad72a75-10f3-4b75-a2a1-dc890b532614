const path = require('path');
const fs = require('fs');
const git = require('../../utils/git');
const execa = require('execa');
const { build } = require('../../core/build/index');
const upload = require('../../core/upload/index');
const chalk = require('chalk');
const ora = require('ora');
const inquirer = require('inquirer');
const { readJson, getUpdateVersion, version } = require('../../utils/utils');
const publishTest = require('./publishTest');
const publishProd = require('./publishProd');

module.exports = async function publishCommand(argv, {
  context,
  mConfig
}) {
  const env = argv.test ? 'test' : 'prod'
  const testType = env === 'test' ? argv.test : null
  let spinner = null

  // 这里只是想进行警告⚠️，不想中断程序
  if (argv.prod && argv.test) {
    console.log('publish命令不能同时存在prod参数和test参数，同时存在时使用测试发布')
  } else if (!argv.prod && !argv.test) {
    console.log('未指定发布参数publish，默认为生产发布')
  } else if (env === 'test' && !testType) {
    return console.log('当如果是发布测试环境是，需要指定发布的类型：add（添加新版本），update（更新现有版本）')
  }


  // TODO: 构建之前删除所有文件夹

  // 检查当前分支是否有未提交的代码
  const isCleaned = await git.isCleaned()

  if (!isCleaned) {
    console.log(`${chalk.red('你本地还存在未提交的文件，请提交后重试')}`)
    return
  }

  try {
    console.log(`\n${chalk.green('===============代码更新===============')}\n`)
    spinner = ora('拉取最新代码')
    // 拉取最新代码
    spinner.start()

    await git.fetch() // 先拉取最新
    branchs = await git.getBranch() // 获取所有分支

    if (branchs.currentBranch === 'master' || branchs.currentBranch === 'feature/master') {
      spinner.fail(`拉取最新代码 ${chalk.red('不能在master或者feature/master分支上进行发布')}`)
      return
    } else if (!branchs.remoteBranch.find(item => item === 'origin/feature/master')) {
      // TODO: 如何远端没有feature/master分支，则在远端master上创建feature/master分支
      spinner.fail(`拉取最新代码 ${chalk.red('需要在远端创建feature/master分支')}`)
      return
    } else {
      // await git.fetchMaster() // 拉取和同步 master 分支和 feature/master 分支
      spinner.succeed(`拉取最新代码 当前分支：${chalk.green(branchs.currentBranch)}`)
    }

    // 代码合并
    spinner = ora('代码合并中')
    spinner.start()

    const result = await git.fetchMaster() // 拉取和同步 master 分支和 feature/master 分支

    if (result && !result.success) {
      spinner.fail(result.errMsg)
      git.mergeBack()
      return
    }
    
    spinner.succeed(`${chalk.green('合并成功')}`)
  } catch(err) {
    spinner.fail(err.message)
    return
  }

  if (env === 'prod') {
    await publishProd(branchs, context, mConfig)
  } else {
    await publishTest(argv, branchs, context, mConfig)
  }
};


const axios = require('axios')
const methods = ['get', 'post', 'put', 'delete', 'patch']

const axiosProdInstance = axios.create({
  // baseURL: 'http://localhost:8080/fopMaterialServices/'
  baseURL: 'https://fop.sf-express.com/fopMaterialServices/'
})

const axiosTestInstance = axios.create({
  baseURL: 'http://material-node.sit.sf-express.com/fopMaterialServices/'
})

const request = (type, url, method, data, config) => {
  return (type === 'prod' ? axiosProdInstance : axiosTestInstance)({
    url,
    method,
    data,
    ...config
  }).then(res => {
    if (res.data.success) {
      return res.data.obj
    }

    return Promise.reject(new Error(res.data.message))
  })
}

methods.forEach(method => {
  request[method] = (type, url, data, config) => request(type, url, method, data, config)
})

module.exports = request
{"name": "com", "version": "0.0.1", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev:local": "webpack-dev-server --config ./webpack.config.js", "build:local": "webpack --config ./webpack.config.js", "dev:doc": "mify doc --type dev --config m.config.js", "build:com": "mify com --config m.config.js", "build:doc": "mify doc --type build --config m.config.js", "upload": "mify upload --config m.config.js", "release": "npm run v && npm run build:com && npm run build:doc && npm run upload", "v": "mify version --config m.config.js"}, "keywords": [], "author": "吴晋哲", "license": "ISC", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@babel/core": "^7.21.5", "@babel/preset-env": "^7.21.5", "@babel/preset-react": "^7.18.6", "babel-loader": "^9.1.2", "css-loader": "^6.7.3", "css-minimizer-webpack-plugin": "^5.0.0", "html-webpack-plugin": "^5.5.1", "less": "^4.1.3", "less-loader": "^11.1.0", "mini-css-extract-plugin": "^2.7.5", "style-loader": "^3.3.2", "webpack": "^5.81.0", "webpack-cli": "^5.0.2", "webpack-dev-server": "^4.13.3"}, "material": true}
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require("css-minimizer-webpack-plugin");

module.exports = {
  mode: 'development',  // 开发模式
  entry: './example/main.js',  // 入口文件
  output: {  // 输出文件
    // path: 'dist',
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].js',
  },
  module: {  // 模块配置
    rules: [
      {
        test: /\.(js|jsx)$/,  // 匹配文件
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',  // 使用 Babel 转译
          options: {
            presets: ['@babel/preset-env', '@babel/preset-react']  // 预设
          }
        }
      },
      {
        test: /\.css$/,  // 匹配 CSS 文件
        use: ['style-loader', 'css-loader']  // 使用 style-loader 和 css-loader
      },
      {
        test: /\.less$/,  // 匹配 CSS 文件
        use: [MiniCssExtractPlugin.loader, 'css-loader', 'less-loader']  // 使用 style-loader 和 css-loader
      }
    ]
  },
  plugins: [  // 插件
    new HtmlWebpackPlugin({
      template: './example/index.html'  // HTML 模板文件
    }),
    new MiniCssExtractPlugin({
      filename: '[name].css',
      chunkFilename: '[id].css',
    }),
  ],
  optimization: {
    minimizer: [
      // For webpack@5 you can use the `...` syntax to extend existing minimizers (i.e. `terser-webpack-plugin`), uncomment the next line
      // `...`,
      new CssMinimizerPlugin(),
    ],
  },
  devtool: 'source-map',  // 开启 source map
  devServer: {  // 开发服务器配置
    // contentBase: path.join(__dirname, 'dist'),
    // compress: true,
    port: 3000
  }
};

#!/usr/bin/env node

const yargs = require('yargs');
const {hideBin} = require('yargs/helpers');
const createCommand = require('./commands/create/command');
const publishCommand = require('./commands/publish/command');
const devCommand = require('./commands/dev/command');
const buildCommand = require('./commands/build/command');

yargs(hideBin(process.argv))
    .command(createCommand)
    .command(devCommand)
    .command(publishCommand)
    .command(buildCommand)
    .demandCommand()
    .alias('help', 'h')
    .alias('version', 'v')
    .argv;

// const path = require('path');
// const {CleanWebpackPlugin} = require('clean-webpack-plugin');
// const MiniCssExtractPlugin = require('mini-css-extract-plugin');

module.exports = {
  mode: 'development',
  // context: mConfig.projectContext,
  output: {
    // path在build.js中进行改写，改写为执行的目录
    filename: '[name].js',
    // chunkFilename: (pathData) => {
    //   // 将组件的chunkName改写为index.js
    //   if (pathData.chunk.name === id) {
    //     return 'index.js';
    //   }

    //   return '[name].js';
    // },
    // 使用时如何保证所引用项目的webpackJsonp字符串
    chunkLoadingGlobal: 'webpackJsonp',
    // self["myCustomFunc"] 改写使用jsonp加载模块的全局变量名称
  },
  module: { // 模块配置
    rules: [
      {
        test: /\.(js|jsx)$/, // 匹配文件
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader', // 使用 Babel 转译
          options: {
            presets: ['@babel/preset-env', '@babel/preset-react'], // 预设
          },
        },
      },
      {
        test: /\.css$/, // 匹配 CSS 文件
        // use: [MiniCssExtractPlugin.loader, 'css-loader'], // 使用 style-loader 和 css-loader
        use: ['style-loader', 'css-loader'], // 使用 style-loader 和 css-loader
      },
      {
        test: /\.less$/, // 匹配 CSS 文件
        // ./src/index.less
        use: [
          'style-loader',
          'css-loader',
          'less-loader',
        ], // 使用 style-loader 和 css-loader
      },
    ],
  },
  plugins: [
    // new CleanWebpackPlugin(),
    // new MiniCssExtractPlugin({
    //   filename: '[name].css',
    //   chunkFilename: '[id].css',
    // }),
  ],
  optimization: {
    chunkIds: 'named',
    moduleIds: 'named',
    splitChunks: {
      chunks: 'all',
      name: 'vendor',
    },
  },
  devtool: 'source-map', // 开启 source map
};

const TerserPlugin = require('terser-webpack-plugin');
// const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const path = require('path');

module.exports = (context, mConfig) => {
  const id = `${mConfig.name}@${mConfig.version}`;

  return {
    mode: 'none',
    entry: {
      'main': path.resolve(__dirname, 'main.js'),
    },
    module: {
      rules: [
        // 添加主函数的loader
        {
          test: /main\.js$/,
          use: {
            loader: path.resolve(__dirname, '../../modifyEntryLoader'),
            options: {
              path: path.resolve(context, mConfig.component),
              chunkName: id,
            },
          },
        },
      ],
    },
    plugins: [
      // 移除多余的chunk
      function(compiler) {
        compiler.hooks.compilation.tap('CustomChunkIdPlugin', (compilation) => {
          compilation.hooks.afterOptimizeChunkIds.tap('CustomChunkIdPlugin', (chunks) => {
            chunks.forEach((chunk) => {
              if (chunk.id !== id) {
                // 删除多余chunk
                compilation.chunks.delete(chunk);
              }
            });
          });
        });
      },

      // 添加index.json
      function(compiler) {
        compiler.hooks.compilation.tap('RemoveOutputFilesPlugin', (compilation) => {
          compilation.hooks.processAssets.tap(
              {
                name: 'RemoveOutputFilesPlugin',
                stage: compilation.PROCESS_ASSETS_STAGE_ADDITIONS,
              },
              (assets) => {
                const jsonData = JSON.stringify({
                  id,
                  cname: mConfig.cname,
                  name: mConfig.name,
                  version: mConfig.version,
                  alias: mConfig.alias,
                }, null, 2);

                assets['index.json'] = {
                  source: () => jsonData,
                  size: () => jsonData.length,
                };
              },
          );
        });
      },

      function(compiler) {
        // 修改module id 将为特定id
        compiler.hooks.compilation.tap('CustomModuleIdPlugin', (compilation) => {
          compilation.hooks.afterOptimizeModuleIds.tap('CustomModuleIdPlugin', (modules) => {
            const chunkGraph = compilation.chunkGraph;

            modules.forEach((module) => {
              // 目前先用路径拼接判断，当前的module是否为构建的组件模块，从而改写组件的moduleId
              if (path.resolve(context, mConfig.component) === path.resolve(context, chunkGraph.getModuleId(module) || '')) {
                // 路径判断
                chunkGraph.setModuleId(module, id);
              }
            });
          });
        });
      },

      // new MiniCssExtractPlugin({
      //   filename: '[name].[contenthash].css',
      //   chunkFilename: '[id].[contenthash].css',
      // }),
    ],
    optimization: {
      // minimize: true, // 压缩
      minimizer: [
        new TerserPlugin({
          extractComments: false, // 不单独生成LICENSE.txt
        }),
      ],
    },
  };
};

const webpack = require('webpack');
const webpackBaseConfig = require('../webpack.base.config');
const webpackBuildConfigFun = require(`./webpackConfig/webpack.config`);
const { merge } = require('webpack-merge');
const path = require('path');

module.exports = function buildComponent(context, mConfig) {
  const webpackConfig = merge(webpackBaseConfig, webpackBuildConfigFun(context, mConfig));

  webpackConfig.context = context
  // 修改构建出来的项目path
  webpackConfig.output.path = path.resolve(context, 'component-dist', `${mConfig.name}@${mConfig.version}`); // 构建出的文件目录

  return new Promise((resolve, reject) => {
    webpack(webpackConfig, (err, stats) => {
      if (err) {
        console.log('err', err.message);
        reject(err)
        process.exit(1);
      } else if (stats.compilation.errors.length > 0) {
        console.log(stats.compilation.errors);
        reject(err)
        process.exit(1);
      } else {
        resolve(0)
      }
    });
  })
}
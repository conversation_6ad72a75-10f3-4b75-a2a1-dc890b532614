
const MarkdownIt = require('markdown-it');
// const container = require('markdown-it-container');
const hljs = require('highlight.js');
const path = require('path');
const babel = require('@babel/core');
const babelParser = require('@babel/parser');
const babelTypes = require('@babel/types');
const babelGenerator = require('@babel/generator');
const traverse = require('@babel/traverse');

const md = new MarkdownIt()

module.exports = function(source) {
  const { mConfig, context } = this.getOptions();

  const jsCodeList = [];
  const componentNames = [];
  let id = 0;
  const prefix = 'App';

  // TODO: 根据title渲染出右边的菜单
  md.renderer.rules.heading_open = (tokens, idx, options, env, self) => {
    const token = tokens[idx];
    const title = token.content; // 获取标题内容

    // console.log('title', token)
    // titles.push(title); // 存储到数组中
    return self.renderToken(tokens, idx, options);
  };

  md.renderer.rules.html_inline = (tokens, idx, options, env, self) => {
    const token = tokens[idx];

    // if (token.tag === 'code') {
    //   // 将 <code> 包裹在 <span class="custom-code">...</span> 中
    //   return `<span class="custom-code">${token.content}</span>`;
    // } else {
    //   // 其他情况，保持默认渲染
    //   return self.renderToken(tokens, idx, options);
    // }
  };

  md.renderer.rules.fence = (tokens, idx, options, env, self) => {
    // 获取当前代码块的 token
    const token = tokens[idx];


    // 如果token不是代码块，则调用默认的渲染方法
    if (!token.info) {
      return self.renderToken(tokens, idx, options);
    }

    jsCodeList.push(token.content);

    // 使用highlight 解析代码
    const code = hljs.highlight(token.content, {
      language: token.info,
    }).value.replace(/\n/gi, '<br />');
    const language = options.langPrefix + token.info;

    const componentName = `${prefix}${id}`;
    componentNames.push(componentName);
    id++;

    // 渲染自定义代码块样式
    return `
      <div>
        <div className="render-content">
          <${componentName} />
        </div>
        <div className="custom-code-block">
          <pre className="${language}"><code className="${language}">${code}</code></pre>
        </div>
      </div>
      `;
  };
  let result = md.render(source);

  // 转译以下字符
  result = result.replace(/\(/g, '&#40;');
  result = result.replace(/\)/g, '&#41;');
  result = result.replace(/\{/g, '&#123;');
  result = result.replace(/\}/g, '&#125;');

  let allCode = '';

  jsCodeList.forEach((code, index) => {
    let ast = null;
    let transformCode = '';
    try {
      // 先将code解析为ast
      ast = babelParser.parse(code, {
        sourceType: 'module',
        plugins: ['jsx'],
      });

      let isExportDefaultToReturn = false;

      // 操作ast  转换，将export default 转成return
      traverse.default(ast, {
        ExportDefaultDeclaration(path) {
          const declaration = path.node.declaration;
          if (declaration.type !== 'FunctionDeclaration') {
            throw new Error('必须默认导出一个函数式组件');
          }

          const returnStatement = babelTypes.returnStatement(babelTypes.functionExpression(
              null, // 函数名 babelTypes.identifier(declaration.id.name)
              [], // 参数列表
              declaration.body,
          ));
          isExportDefaultToReturn = true;
          path.replaceWith(returnStatement);
        },
      });

      if (!isExportDefaultToReturn) {
        throw new Error('必须默认导出一个函数式组件');
      }

      // 操作ast 将import 转换成require
      const result = babel.transformFromAstSync(
          ast,
          null,
          {
            ast: true,
            code: false,
            plugins: [
              ['@babel/plugin-transform-modules-commonjs', {
                importInterop: 'none',
                strict: false,
              }],
            ],
          },
      );

      ast = result.ast;

      // 操作ast
      // 将所有代码都包裹在一个iife中，然后再赋值给一个变量，
      // 使用函数的方式隔离变量
      traverse.default(ast, {
        Program(path) {
          const statements = path.node.body;
          // 立即执行函数包裹所有代码
          const IIFE = babelTypes.callExpression(
              babelTypes.functionExpression(
                  null,
                  [],
                  babelTypes.blockStatement(statements),
              ),
              [],
          );

          // 将函数赋值给变量
          const variable = babelTypes.variableDeclaration(
              'const',
              [babelTypes.variableDeclarator(babelTypes.identifier(componentNames[index]), IIFE)],
          );

          path.node.body = [variable];
        },
      });

      // 将ast转换成code
      const {code: newCode} = babelGenerator.default(ast);

      transformCode = newCode;
    } catch (err) {
      console.log('err', err);
      const errMessage = err.message;
      // 如果解析失败，则将代码包裹在一个函数中，返回错误信息
      // 则把错误信息的展示当成组件渲染，给用户错误提示
      transformCode = `
        const ${componentNames[index]} = function ${componentNames[index]}() {
          const React = require('react');

          return <div>${errMessage}</div>
        }
      `;
    }

    allCode += ('\n' + transformCode);
  });

  // console.log('allCode', allCode);
  const [p1, p2] = mConfig.alias.split('.')

  // ${code}
  const newSrouce = `
  import React from 'react';
  require('${path.resolve(__dirname, './a11y-light.css')}');

  require('${path.resolve(__dirname, './doc.css')}');
  const Com = require('${path.resolve(context, mConfig.component)}').default

  const ${p1} = {};
  window.${p1} = ${p1};
  window.${p1}.${p2} = Com

  ${allCode}

  export default function() {
    return <div>
    ${result}
    </div>
  }
  `;
  return newSrouce;
};



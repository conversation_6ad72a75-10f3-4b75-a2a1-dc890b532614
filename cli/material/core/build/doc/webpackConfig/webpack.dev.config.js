const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

module.exports = (context, mConfig) => {
  const id = `${mConfig.name}@${mConfig.version}@doc`;

  return {
    mode: 'development',
    entry: {
      'doc': path.resolve(__dirname, 'doc.js'),
    },
    module: { // 模块配置
      rules: [
        {
          test: /doc\.js$/,
          use: [
            {
              loader: 'babel-loader', // 使用 Babel 转译
              options: {
                presets: ['@babel/preset-env', '@babel/preset-react'], // 预设
              },
            },
            {
            loader:
              path.resolve(__dirname, '../../modifyEntryLoader'),
              options: {
                path: path.resolve(context, mConfig.doc),
                chunkName: id,
              },
            }
          ],
        },
        {
          test: /\.md$/,
          use: [
            {
              loader: 'babel-loader', // 使用 Babel 转译
              options: {
                presets: ['@babel/preset-env', '@babel/preset-react'], // 预设
              },
            },
            {
              loader: path.resolve(__dirname, 'markdownLoader'),
              options: {
                context,
                mConfig,
              },
            },
          ],
        },
      ],
    },
    plugins: [
      new HtmlWebpackPlugin({
        templateContent: `
          <!DOCTYPE html>
          <html lang="en">
          <head>
            <meta charset="UTF-8">
            <meta http-equiv="X-UA-Compatible" content="IE=edge">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Document</title>
          </head>
          <body>
            <div id="app"></div>
          </body>
          </html>
        `,
      }),
        // new CleanWebpackPlugin(),
      // new MiniCssExtractPlugin({
      //   filename: '[name].css',
      //   chunkFilename: '[id].css',
      // }),
      
    ],
    devServer: { // 开发服务器配置
      port: 8080,
      // open: true,
      host: '127.0.0.1',
    },
  };
};

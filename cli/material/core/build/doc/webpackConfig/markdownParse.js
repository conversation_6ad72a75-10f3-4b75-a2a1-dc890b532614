
const MarkdownIt = require('markdown-it');
// const container = require('markdown-it-container');
const hljs = require('highlight.js');

function codeInlinePlugin(md) {
  // 自定义处理函数
  function handleCodeInline(state, silent) {
    // 获取当前解析位置
    const pos = state.pos;
    
    // 检查是否以 `<code src="..."></code>` 形式开始
    if (state.src.charCodeAt(pos) !== 0x3C /* < */ ||
        state.src.charCodeAt(pos + 1) !== 0x63 /* c */ ||
        state.src.charCodeAt(pos + 2) !== 0x6F /* o */ ||
        state.src.charCodeAt(pos + 3) !== 0x64 /* d */ ||
        state.src.charCodeAt(pos + 4) !== 0x65 /* e */) {
      return false;
    }
    
    // 查找结束标签 `</code>`
    let endTagPos = state.src.indexOf('</code>', pos + 5);
    if (endTagPos === -1) {
      return false;
    }

    // 提取 src 属性的值
    let src = state.src.slice(pos + 5, endTagPos);
    
    // 处理 src 属性的值，生成完整路径
    const fullPath = src// 进行路径处理的逻辑...
    
    // 生成替换的 HTML 代码
    const replacement = `<xcode src={() => import(${fullPath})}></xcode>`;
    
    // 替换原始的 Markdown 文本
    if (!silent) {
      state.push('xcode', '', 0);
      state.tokens[state.tokens.length - 1].content = replacement;
    }
    
    // 调整解析位置
    state.pos = endTagPos + 7; // 调整到结束标签之后
    
    return true;
  }

  // 在 inline 解析规则之后插入新规则
  md.inline.ruler.after('codespan', 'codeInline', handleCodeInline);
}


module.exports = function markdownParse(markdown) {
  const md = new MarkdownIt({ html: true })

  md.use(codeInlinePlugin);
  // 解析code html标签
  // md.block.ruler.before('fence', 'code', (state, startLine, endLine, silent) => {

  // })

//   md.renderer.rules.html_inline = (tokens, idx, options, env, self) => {
// console.log('token', tokens, tokens[idx].attrs, tokens[idx].attrIndex('src'))
//   }

  // md.inline.ruler.after('backticks', 'codeWithSrc', (state) => {
  //   const codeRegex = /<code.*?src=["']([^"']+)["'].*?>/g;
  
  //   state.src = state.src.replace(codeRegex, (match, src) => {
  //     // 处理匹配到的 <code> 标签中的 src 属性
  //     console.log('src:', src);
  //     return match;
  //   });
  // });
  
  // 使用 markdown-it 进行解析
  // const markdownText = 'Custom code: <code src="./base.js"></code>';
  // md.render(markdownText);

  
  
  
  
  
  
  
  
  // console.log('xxx', md.parse(markdown))

  return md.render(markdown)
}


/**
 <code src="./base.js"></code>

 <xcode src={() => import(完整路径)}></xcode>
 * 
 */


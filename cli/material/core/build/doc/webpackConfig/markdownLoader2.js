
const MarkdownIt = require('markdown-it');
// const container = require('markdown-it-container');
const hljs = require('highlight.js');
const path = require('path');
const babel = require('@babel/core');
const babelParser = require('@babel/parser');
const babelTypes = require('@babel/types');
const babelGenerator = require('@babel/generator');
const traverse = require('@babel/traverse');

const md = new MarkdownIt()

module.exports = function(source) {
  const {mConfig} = this.getOptions();

  const jsCodeList = [];
  const componentNames = [];
  let id = 0;
  const prefix = 'App';


  md.renderer.rules.text = (tokens, idx, options, env, self) => {
    const token = tokens[idx];

    const codePattern = /<code src="(.+?)"><\/code>/;

    console.log('taokn', token, token.tag, idx)
    const match = code.match(codeSrcPattern);
      if (match) {
        const src = match[1]; // 提取 src 属性的值

        // 自定义解析逻辑，替换成你想要的内容
        return `<div class="custom-code-block">Custom code block: ${src}</div>`;
      }
    //   // 其他情况，保持默认渲染
      return self.renderToken(tokens, idx, options);
    // }
  };

  // md.renderer.rules.inline = (tokens, idx, options, env, self) => {
  //   const token = tokens[idx];

  //   console.log('taokn', token, token.tag, idx)
  //   // 其他情况，保持默认渲染
  //   return self.renderToken(tokens, idx, options);
  // };

  let result = md.render(source);
  console.log('result', result);

  // 转译以下字符
  result = result.replace(/\(/g, '&#40;');
  result = result.replace(/\)/g, '&#41;');
  result = result.replace(/\{/g, '&#123;');
  result = result.replace(/\}/g, '&#125;');

  // ${code}
  const newSrouce = `
  import React from 'react';
  require('${path.resolve(__dirname, './a11y-light.css')}');

  // require('${path.resolve(__dirname, '../../../node_modules/highlight.js/styles/a11y-light.css')}');
  require('${path.resolve(__dirname, './doc.css')}');
  const Button = require('${path.resolve(mConfig.context, mConfig.component)}').default

  const M = { Button }


  export default function() {
    return <div>
    </div>
  }
  `;
  return newSrouce;
};



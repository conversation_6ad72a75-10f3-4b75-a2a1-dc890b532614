
const MarkdownIt = require('markdown-it');
// const container = require('markdown-it-container');
const hljs = require('highlight.js');
const path = require('path');
const babel = require('@babel/core');
const babelParser = require('@babel/parser');
const babelTypes = require('@babel/types');
const babelGenerator = require('@babel/generator');
const traverse = require('@babel/traverse');
const markdownParse = require('./markdownParse');
// const md = new MarkdownIt()

module.exports = function(source) {
  const {mConfig} = this.getOptions();

  const result = markdownParse(source);

  console.log('result', result);
  // ${code}
  const newSrouce = `
  import React from 'react';
  require('${path.resolve(__dirname, './a11y-light.css')}');

  // require('${path.resolve(__dirname, '../../../node_modules/highlight.js/styles/a11y-light.css')}');
  require('${path.resolve(__dirname, './doc.css')}');
  const Button = require('${path.resolve(mConfig.context, mConfig.component)}').default

  const M = { Button }


  export default function() {
    return <div>
    </div>
  }
  `;
  return newSrouce;
};



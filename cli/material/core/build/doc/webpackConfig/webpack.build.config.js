const path = require('path');
const webpackDocDevConfigFun = require('./webpack.dev.config');
const { merge } = require('webpack-merge');
// const MiniCssExtractPlugin = require('mini-css-extract-plugin');


module.exports = (context, mConfig) => {
  const id = `${mConfig.name}@${mConfig.version}@doc`;

  const webpackDocDevConfig = webpackDocDevConfigFun(context, mConfig);
  webpackDocDevConfig.plugins = []; // 删除掉doc dev的html插件 保证打包后没有HTML文件

  return merge(webpackDocDevConfig, {
    mode: 'development',
    // module: { // 模块配置
    //   rules: [
    //     {
    //       test: /\.css$/, // 匹配 CSS 文件
    //       // use: [MiniCssExtractPlugin.loader, 'css-loader'], // 使用 style-loader 和 css-loader
    //       use: ['style-loader', 'css-loader'], // 使用 style-loader 和 css-loader
    //     },
    //   ]
    // },
    plugins: [
      // 移除多余的chunk
      function(compiler) {
        compiler.hooks.compilation.tap('CustomChunkIdPlugin', (compilation) => {
          compilation.hooks.afterOptimizeChunkIds.tap('CustomChunkIdPlugin', (chunks) => {
            chunks.forEach((chunk) => {
              if (chunk.id !== id) {
                // 删除多余chunk
                compilation.chunks.delete(chunk);
              }
            });
          });
        });
      },

      // 修改module id，防止项目的id冲突
      function(compiler) {
        // 修改module id 将为特定id
        compiler.hooks.compilation.tap('CustomModuleIdPlugin', (compilation) => {
          compilation.hooks.afterOptimizeModuleIds.tap('CustomModuleIdPlugin', (modules) => {
            const chunkGraph = compilation.chunkGraph;

            modules.forEach((module) => {
              // 目前先用路径拼接判断，当前的module是否为构建的组件模块，从而改写组件的moduleId
              if (path.resolve(context, mConfig.doc) === path.resolve(context, chunkGraph.getModuleId(module) || '')) {
                // 路径判断
                chunkGraph.setModuleId(module, id);
              } else if (path.resolve(context, mConfig.component) === path.resolve(context, chunkGraph.getModuleId(module) || '')) {
                // 路径判断
                chunkGraph.setModuleId(module, `${mConfig.name}@${mConfig.version}`);
              }
            });
          });
        });
      },
    ],
    optimization: {
      chunkIds: 'named',
      moduleIds: 'named',
      // minimize: true, // 压缩
      splitChunks: {
        name: 'vendor',
      },
    },
  });
};

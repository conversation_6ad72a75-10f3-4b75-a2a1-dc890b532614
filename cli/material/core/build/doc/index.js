const webpack = require('webpack');
const webpackBaseConfig = require('../webpack.base.config');
const WebpackDevServer = require('webpack-dev-server');
const { merge } = require('webpack-merge');
const path = require('path');
const portfinder = require('portfinder');

module.exports = function buildDoc(type, context, mConfig) {
  const webpackDocConfigFun = require(`./webpackConfig/webpack.${type}.config`);

  const webpackConfig = merge(webpackBaseConfig, webpackDocConfigFun(context, mConfig));
  webpackConfig.context = context;
  webpackConfig.output.path = path.resolve(
      context,
      'component-dist',
      `${mConfig.name}@${mConfig.version}`,
  ); // 构建出的文件目录

  if (type === 'dev') {
    // 运行server
    const compiler = webpack(webpackConfig);

    portfinder.basePort = webpackConfig.devServer.port;

    return new Promise((resolve, reject) => {
      portfinder.getPort(async (_, port) => {
        webpackConfig.devServer.port = port;
        const server = new WebpackDevServer(webpackConfig.devServer, compiler);
        await server.start();
        console.log(`Dev server started on http://${webpackConfig.devServer.host}:${port}`);
        resolve()
      });
    })
  } else {
    // 构建
    return new Promise((resolve, reject) => {
      webpack(webpackConfig, (err, stats) => {
        if (err) {
          console.log('err', err.message);
          reject(err)
          process.exit(1);
        } else if (stats.compilation.errors.length > 0) {
          console.log(stats.compilation.errors);
          reject(stats.compilation.errors[0])
          process.exit(1);
        } else {
          resolve(0)
        }
      });
    })
  }
}
const request = require('../../utils/request');
const zip = require('./zip');
const path = require('path');
const FormData = require('form-data');
const fs = require('fs');
const api = require('../../request/api')

/** 压缩且上传 */
module.exports = function upload(folderPath, extraData, type) {
  return zip(folderPath).then(p => {
    console.log('extraData', extraData)
    const formData = new FormData()
    // path.resolve(context, 'component-dist', `${packageJson.name}@${packageJson.version}.zip`
    formData.append('attachment', fs.createReadStream(p))
    for(const i in extraData) {
      formData.append(i, extraData[i])
    }

    // TODO: 获取当前的git hash值
    // formData.append('name', packageJson.name)
    // formData.append('version', packageJson.version)
    // formData.append('alias', mConfig.cname)
    return api.createComponent(formData, type).then(res => {
      if (res.success && res.obj) {
        return true
      }

      return Promise.resolve(new Error(res.errorMessage))
    })
  })
}
const fs = require('fs');
const archiver = require('archiver');

module.exports = function zip(folderPath) {
  return new Promise((resolve, reject) => {
    // 创建输出流，指定压缩后文件的路径和名称
    const output = fs.createWriteStream(`${folderPath}.zip`);
    const archive = archiver('zip', {
      zlib: { level: 9 } // Sets the compression level.
    });
    
    // 监听输出流结束事件
    output.on('close', function() {
      resolve(`${folderPath}.zip`)
    });
    
    // 监听输出流错误事件
    output.on('end', function(err) {
      reject(err);
    });
    
    // 将输出流绑定到archiver
    archive.pipe(output);
    
    // 添加文件夹
    archive.directory(folderPath, false);
    
    // 执行压缩
    archive.finalize();
  })
}

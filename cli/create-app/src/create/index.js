// const inquirer = require('inquirer')
const inquirer = require("inquirer");
const initObject = require("./default");
const { DEFAULT_QUESTIONS } = require("../constants/questions");
module.exports.run = async () => {
  try {
    // 获取输入的信息
    const answers = await inquirer.prompt(DEFAULT_QUESTIONS);
    await $`mkdir ${answers.PROJECT_NAME}`;
    await initObject(answers);
    // 跳转至目录
    cd(answers.PROJECT_NAME);
    // // 执行安装命令
    await $`yarn`;
  } catch (error) {
    console.log(
      chalk.red(`Create objet defeat! The error message is: ${error}`)
    );
  }
};

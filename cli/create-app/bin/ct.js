#! /usr/bin/env node

const process = require("process");
const packageJson = require("../package.json");
const { run } = require('../src/create/index')

/*
 * 接收命令行传过来的指令参数
 * 由于第一第二个为path信息，我们用不到，所以从第三个开始取
 */
const argvs = process.argv.slice(2);

switch (argvs[0] || "-c") {
  /** 查看当前插件版本 */
  case "-v":
  case "--version":
    console.log(`v${packageJson.version}`);
    break;
  /** 创建项目 */
  case "-c":
  case "--create":
    run()
    break;
}

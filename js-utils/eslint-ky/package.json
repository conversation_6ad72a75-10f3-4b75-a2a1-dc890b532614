{"name": "@ky/eslint-config-kyfe", "description": "ky react项目eslint规范", "version": "1.9.0", "author": {"name": "kyfe"}, "main": "index.js", "dependencies": {"@babel/eslint-parser": "^7.18.9", "eslint": "^5.3.0", "eslint-config-airbnb": "^17.1.1", "eslint-config-prettier": "^7.2.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-node": "^7.0.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-promise": "^4.3.1", "eslint-plugin-react": "^7.31.7", "eslint-plugin-standard": "^4.0.0"}, "publishConfig": {"registry": "https://artifactory.sf-express.com/artifactory/api/npm/npm-sf-local/"}, "license": "MIT", "gitHead": "cda8a4d059bd387cb9046075a1cbff030e593eb1"}
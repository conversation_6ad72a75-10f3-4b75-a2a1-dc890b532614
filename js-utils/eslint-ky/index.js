/*
 * @Author: dong
 * @LastEditors: dong
 */
const fs = require('fs');
const path = require('path');

const prettierConf = require('./prettierConf');
const presentPath = path.resolve(
  __dirname.split('node_modules')[0],
  './.prettierrc',
);
const prettierOptions = Object.assign(
  {},
  prettierConf,
  JSON.parse(fs.readFileSync(presentPath, 'utf8')),
);
module.exports = {
  parser: '@babel/eslint-parser',
  extends: ['airbnb', 'prettier', 'prettier/react'],
  plugins: ['prettier', 'react', 'jsx-a11y'],
  env: {
    browser: true,
    node: true,
    jest: true,
    es6: true,
  },
  parserOptions: {
    ecmaVersion: 6,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
  },
  rules: {
    'consistent-return': 0,
    'no-restricted-syntax': 0,
    'guard-for-in': 0,
    'jsx-a11y/label-has-associated-control': 0,
    'jsx-a11y/no-static-element-interactions': 0,
    'jsx-a11y/click-events-have-key-events': 0,
    'jsx-a11y/anchor-is-valid': 0,
    'react/prop-types': 0,
    'jsx-a11y/label-has-for': 0,
    'jsx-a11y/no-noninteractive-element-interactions': 0,
    'no-param-reassign': ['error', { props: false }],
    'no-underscore-dangle': 1,
    'global-require': 0,
    'no-plusplus': ['error', { allowForLoopAfterthoughts: true }],
    'prettier/prettier': ['error', prettierOptions],
    'click-events-have-key-events': 0,
    'arrow-body-style': [2, 'as-needed'],
    'class-methods-use-this': 0,
    // 'comma-dangle': [2, 'always-multiline'],
    'import/imports-first': 0,
    'import/newline-after-import': 0,
    'no-debugger': 1,
    'import/no-dynamic-require': 0,
    'import/no-extraneous-dependencies': 0,
    'import/no-named-as-default': 0,
    'import/no-unresolved': 0,
    'import/no-webpack-loader-syntax': 0,
    'import/prefer-default-export': 0,
    'linebreak-style': [0, 'error', 'windows'],
    'jsx-a11y/aria-props': 2,
    'jsx-a11y/heading-has-content': 0,
    'jsx-a11y/mouse-events-have-key-events': 2,
    'jsx-a11y/role-has-required-aria-props': 2,
    'jsx-a11y/role-supports-aria-props': 2,
    'max-len': 0,
    'newline-per-chained-call': 0,
    'no-confusing-arrow': 0,
    'no-console': 1,
    'no-use-before-define': 0,
    'prefer-template': 2,
    'react/jsx-closing-tag-location': 0,
    'react/forbid-prop-types': 0,
    'react/jsx-first-prop-new-line': [2, 'multiline'],
    'react/jsx-filename-extension': 0,
    'react/jsx-no-target-blank': 0,
    'react/require-default-props': 0,
    'react/require-extension': 0,
    'react/self-closing-comp': 0,
    'react/sort-comp': 0,
    'no-nested-ternary': 0,
    'react/no-string-refs': 0,
  },
};

{"name": "@ky/eslint-config-kyfe", "version": "1.9.0", "lockfileVersion": 1, "requires": true, "dependencies": {"@babel/code-frame": {"version": "7.12.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/code-frame/download/@babel/code-frame-7.12.11.tgz", "integrity": "sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8=", "requires": {"@babel/highlight": "^7.10.4"}}, "@babel/eslint-parser": {"version": "7.18.9", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/eslint-parser/-/eslint-parser-7.18.9.tgz", "integrity": "sha1-JVpjeWgZqXt1eHUbsIq58qN1oDE=", "requires": {"eslint-scope": "^5.1.1", "eslint-visitor-keys": "^2.1.0", "semver": "^6.3.0"}, "dependencies": {"eslint-scope": {"version": "5.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eslint-scope/-/eslint-scope-5.1.1.tgz", "integrity": "sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=", "requires": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}}, "eslint-visitor-keys": {"version": "2.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz", "integrity": "sha1-9lMoJZMFknOSyTjtROsKXJsr0wM="}, "semver": {"version": "6.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="}}}, "@babel/helper-validator-identifier": {"version": "7.12.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.12.11.tgz", "integrity": "sha1-yaHwIZF9y1zPDU5FPjmQIpgfye0="}, "@babel/highlight": {"version": "7.10.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/highlight/download/@babel/highlight-7.10.4.tgz", "integrity": "sha1-fRvf1ldTU4+r5sOFls23bZrGAUM=", "requires": {"@babel/helper-validator-identifier": "^7.10.4", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}}, "@babel/runtime": {"version": "7.19.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/runtime/-/runtime-7.19.0.tgz", "integrity": "sha1-IrEcA3sJTSeoolBOpNz/APUOIlk=", "requires": {"regenerator-runtime": "^0.13.4"}}, "@babel/runtime-corejs3": {"version": "7.19.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/runtime-corejs3/-/runtime-corejs3-7.19.0.tgz", "integrity": "sha1-DfdcuOXsujyp5liJhpTlMm1SOX8=", "requires": {"core-js-pure": "^3.20.2", "regenerator-runtime": "^0.13.4"}}, "@types/json5": {"version": "0.0.29", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/json5/-/json5-0.0.29.tgz", "integrity": "sha1-7ihweulOEdK4J7y+UnC86n8+ce4="}, "acorn": {"version": "6.4.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/acorn/download/acorn-6.4.2.tgz", "integrity": "sha1-NYZv1xBSjpLeEM8GAWSY5H454eY="}, "acorn-jsx": {"version": "5.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/acorn-jsx/download/acorn-jsx-5.3.1.tgz", "integrity": "sha1-/IZh4Rt6wVOcR9v+oucrOvNNJns="}, "ajv": {"version": "6.12.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ajv/download/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "requires": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ansi-escapes": {"version": "3.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-escapes/download/ansi-escapes-3.2.0.tgz", "integrity": "sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s="}, "ansi-regex": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg="}, "ansi-styles": {"version": "3.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "requires": {"color-convert": "^1.9.0"}}, "argparse": {"version": "1.0.10", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/argparse/download/argparse-1.0.10.tgz", "integrity": "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=", "requires": {"sprintf-js": "~1.0.2"}}, "aria-query": {"version": "4.2.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/aria-query/-/aria-query-4.2.2.tgz", "integrity": "sha1-DSymyazrVriXfp/tau1+FbvS+Ds=", "requires": {"@babel/runtime": "^7.10.2", "@babel/runtime-corejs3": "^7.10.2"}}, "array-includes": {"version": "3.1.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/array-includes/-/array-includes-3.1.5.tgz", "integrity": "sha1-LDIAENuNMQMf0qX2s7vUsarTG9s=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.19.5", "get-intrinsic": "^1.1.1", "is-string": "^1.0.7"}, "dependencies": {"call-bind": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}, "es-abstract": {"version": "1.20.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/es-abstract/-/es-abstract-1.20.2.tgz", "integrity": "sha1-hJWge8VtNCo7jqOrAb2YZwDCzLM=", "requires": {"call-bind": "^1.0.2", "es-to-primitive": "^1.2.1", "function-bind": "^1.1.1", "function.prototype.name": "^1.1.5", "get-intrinsic": "^1.1.2", "get-symbol-description": "^1.0.0", "has": "^1.0.3", "has-property-descriptors": "^1.0.0", "has-symbols": "^1.0.3", "internal-slot": "^1.0.3", "is-callable": "^1.2.4", "is-negative-zero": "^2.0.2", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "is-string": "^1.0.7", "is-weakref": "^1.0.2", "object-inspect": "^1.12.2", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.4.3", "string.prototype.trimend": "^1.0.5", "string.prototype.trimstart": "^1.0.5", "unbox-primitive": "^1.0.2"}}, "get-intrinsic": {"version": "1.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/get-intrinsic/-/get-intrinsic-1.1.2.tgz", "integrity": "sha1-M2l1Ej4FrQt7pB8VLuSq2+ps9Zg=", "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}}, "has-symbols": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg="}, "is-callable": {"version": "1.2.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-callable/-/is-callable-1.2.4.tgz", "integrity": "sha1-RzAdWN0CWUB4ZVR4U99tYf5HGUU="}, "is-negative-zero": {"version": "2.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-negative-zero/-/is-negative-zero-2.0.2.tgz", "integrity": "sha1-e/bwOigAO4s5Zd46wm9mTXZfMVA="}, "is-regex": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-regex/-/is-regex-1.1.4.tgz", "integrity": "sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=", "requires": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}}, "object-inspect": {"version": "1.12.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object-inspect/-/object-inspect-1.12.2.tgz", "integrity": "sha1-wGQfJjlFMvKKuNeWq5VOQ8AJqOo="}, "object.assign": {"version": "4.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object.assign/-/object.assign-4.1.4.tgz", "integrity": "sha1-lnPHx8NRq4xNC1FvQ0Pr9N+3eZ8=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}}, "string.prototype.trimend": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string.prototype.trimend/-/string.prototype.trimend-1.0.5.tgz", "integrity": "sha1-kUpluqqyX73U7ikcp93lfoacuNA=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.19.5"}}, "string.prototype.trimstart": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string.prototype.trimstart/-/string.prototype.trimstart-1.0.5.tgz", "integrity": "sha1-VGbZO6WM+iE0g5+B1/QkN+jAH+8=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.19.5"}}}}, "array.prototype.flat": {"version": "1.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/array.prototype.flat/-/array.prototype.flat-1.3.0.tgz", "integrity": "sha1-CwwVZ79Xs4tWtMl7iqcqtF5K3Hs=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.3", "es-abstract": "^1.19.2", "es-shim-unscopables": "^1.0.0"}, "dependencies": {"call-bind": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "es-abstract": {"version": "1.20.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/es-abstract/-/es-abstract-1.20.2.tgz", "integrity": "sha1-hJWge8VtNCo7jqOrAb2YZwDCzLM=", "requires": {"call-bind": "^1.0.2", "es-to-primitive": "^1.2.1", "function-bind": "^1.1.1", "function.prototype.name": "^1.1.5", "get-intrinsic": "^1.1.2", "get-symbol-description": "^1.0.0", "has": "^1.0.3", "has-property-descriptors": "^1.0.0", "has-symbols": "^1.0.3", "internal-slot": "^1.0.3", "is-callable": "^1.2.4", "is-negative-zero": "^2.0.2", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "is-string": "^1.0.7", "is-weakref": "^1.0.2", "object-inspect": "^1.12.2", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.4.3", "string.prototype.trimend": "^1.0.5", "string.prototype.trimstart": "^1.0.5", "unbox-primitive": "^1.0.2"}, "dependencies": {"get-intrinsic": {"version": "1.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/get-intrinsic/-/get-intrinsic-1.1.2.tgz", "integrity": "sha1-M2l1Ej4FrQt7pB8VLuSq2+ps9Zg=", "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}}}}, "has-symbols": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg="}, "is-callable": {"version": "1.2.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-callable/-/is-callable-1.2.4.tgz", "integrity": "sha1-RzAdWN0CWUB4ZVR4U99tYf5HGUU="}, "is-negative-zero": {"version": "2.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-negative-zero/-/is-negative-zero-2.0.2.tgz", "integrity": "sha1-e/bwOigAO4s5Zd46wm9mTXZfMVA="}, "is-regex": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-regex/-/is-regex-1.1.4.tgz", "integrity": "sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=", "requires": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}}, "object-inspect": {"version": "1.12.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object-inspect/-/object-inspect-1.12.2.tgz", "integrity": "sha1-wGQfJjlFMvKKuNeWq5VOQ8AJqOo="}, "object.assign": {"version": "4.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object.assign/-/object.assign-4.1.4.tgz", "integrity": "sha1-lnPHx8NRq4xNC1FvQ0Pr9N+3eZ8=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}, "string.prototype.trimend": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string.prototype.trimend/-/string.prototype.trimend-1.0.5.tgz", "integrity": "sha1-kUpluqqyX73U7ikcp93lfoacuNA=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.19.5"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}, "string.prototype.trimstart": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string.prototype.trimstart/-/string.prototype.trimstart-1.0.5.tgz", "integrity": "sha1-VGbZO6WM+iE0g5+B1/QkN+jAH+8=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.19.5"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}}}, "array.prototype.flatmap": {"version": "1.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/array.prototype.flatmap/-/array.prototype.flatmap-1.3.0.tgz", "integrity": "sha1-p+jtQiX0eIpwzZEKvPB5HnalU08=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.3", "es-abstract": "^1.19.2", "es-shim-unscopables": "^1.0.0"}, "dependencies": {"call-bind": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "es-abstract": {"version": "1.20.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/es-abstract/-/es-abstract-1.20.2.tgz", "integrity": "sha1-hJWge8VtNCo7jqOrAb2YZwDCzLM=", "requires": {"call-bind": "^1.0.2", "es-to-primitive": "^1.2.1", "function-bind": "^1.1.1", "function.prototype.name": "^1.1.5", "get-intrinsic": "^1.1.2", "get-symbol-description": "^1.0.0", "has": "^1.0.3", "has-property-descriptors": "^1.0.0", "has-symbols": "^1.0.3", "internal-slot": "^1.0.3", "is-callable": "^1.2.4", "is-negative-zero": "^2.0.2", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "is-string": "^1.0.7", "is-weakref": "^1.0.2", "object-inspect": "^1.12.2", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.4.3", "string.prototype.trimend": "^1.0.5", "string.prototype.trimstart": "^1.0.5", "unbox-primitive": "^1.0.2"}, "dependencies": {"get-intrinsic": {"version": "1.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/get-intrinsic/-/get-intrinsic-1.1.2.tgz", "integrity": "sha1-M2l1Ej4FrQt7pB8VLuSq2+ps9Zg=", "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}}}}, "has-symbols": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg="}, "is-callable": {"version": "1.2.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-callable/-/is-callable-1.2.4.tgz", "integrity": "sha1-RzAdWN0CWUB4ZVR4U99tYf5HGUU="}, "is-negative-zero": {"version": "2.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-negative-zero/-/is-negative-zero-2.0.2.tgz", "integrity": "sha1-e/bwOigAO4s5Zd46wm9mTXZfMVA="}, "is-regex": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-regex/-/is-regex-1.1.4.tgz", "integrity": "sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=", "requires": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}}, "object-inspect": {"version": "1.12.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object-inspect/-/object-inspect-1.12.2.tgz", "integrity": "sha1-wGQfJjlFMvKKuNeWq5VOQ8AJqOo="}, "object.assign": {"version": "4.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object.assign/-/object.assign-4.1.4.tgz", "integrity": "sha1-lnPHx8NRq4xNC1FvQ0Pr9N+3eZ8=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}, "string.prototype.trimend": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string.prototype.trimend/-/string.prototype.trimend-1.0.5.tgz", "integrity": "sha1-kUpluqqyX73U7ikcp93lfoacuNA=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.19.5"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}, "string.prototype.trimstart": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string.prototype.trimstart/-/string.prototype.trimstart-1.0.5.tgz", "integrity": "sha1-VGbZO6WM+iE0g5+B1/QkN+jAH+8=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.19.5"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}}}, "ast-types-flow": {"version": "0.0.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ast-types-flow/-/ast-types-flow-0.0.7.tgz", "integrity": "sha1-9wtzXGvKGlycItmCw+Oef+ujva0="}, "astral-regex": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/astral-regex/download/astral-regex-1.0.0.tgz", "integrity": "sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k="}, "axe-core": {"version": "4.4.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/axe-core/-/axe-core-4.4.3.tgz", "integrity": "sha1-EcdNI9UBPA+l0YN5Zym8NIK9L28="}, "axobject-query": {"version": "2.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/axobject-query/-/axobject-query-2.2.0.tgz", "integrity": "sha1-lD1H4QwLcEqkInXiDt83ImSJib4="}, "balanced-match": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.0.tgz", "integrity": "sha1-ibTRmasr7kneFk6gK4nORi1xt2c="}, "brace-expansion": {"version": "1.1.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/brace-expansion/download/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "call-bind": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/call-bind/download/call-bind-1.0.0.tgz", "integrity": "sha1-JBJwVLs/m9y0sfuCQYGGBy93uM4=", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.0"}}, "callsites": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/callsites/download/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M="}, "chalk": {"version": "2.4.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "chardet": {"version": "0.7.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chardet/download/chardet-0.7.0.tgz", "integrity": "sha1-kAlISfCTfy7twkJdDSip5fDLrZ4="}, "cli-cursor": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/cli-cursor/-/cli-cursor-2.1.0.tgz", "integrity": "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=", "requires": {"restore-cursor": "^2.0.0"}}, "cli-width": {"version": "2.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cli-width/download/cli-width-2.2.1.tgz", "integrity": "sha1-sEM9C06chH7xiGik7xb9X8gnHEg="}, "color-convert": {"version": "1.9.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="}, "concat-map": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="}, "confusing-browser-globals": {"version": "1.0.10", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/confusing-browser-globals/download/confusing-browser-globals-1.0.10.tgz", "integrity": "sha1-MNHn89G4grJexJM9HRraw1PSClk="}, "core-js-pure": {"version": "3.25.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/core-js-pure/-/core-js-pure-3.25.1.tgz", "integrity": "sha1-eVRlGK6HzDYsmR2cLSEfRRB5ke4="}, "cross-spawn": {"version": "6.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cross-spawn/download/cross-spawn-6.0.5.tgz", "integrity": "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=", "requires": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "damerau-levenshtein": {"version": "1.0.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/damerau-levenshtein/-/damerau-levenshtein-1.0.8.tgz", "integrity": "sha1-tD0obMvTa8Wy9+1ByvLQq6H4puc="}, "debug": {"version": "4.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/download/debug-4.3.1.tgz", "integrity": "sha1-8NIpxQXgxtjEmsVT0bE9wYP2su4=", "requires": {"ms": "2.1.2"}}, "deep-is": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/deep-is/-/deep-is-0.1.3.tgz", "integrity": "sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ="}, "define-properties": {"version": "1.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-properties/download/define-properties-1.1.3.tgz", "integrity": "sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=", "requires": {"object-keys": "^1.0.12"}}, "doctrine": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/doctrine/download/doctrine-3.0.0.tgz", "integrity": "sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=", "requires": {"esutils": "^2.0.2"}}, "emoji-regex": {"version": "7.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/emoji-regex/download/emoji-regex-7.0.3.tgz", "integrity": "sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY="}, "es-abstract": {"version": "1.18.0-next.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/es-abstract/download/es-abstract-1.18.0-next.1.tgz", "integrity": "sha1-bjoKS9pxflAjqzuOkL7DYQjSLGg=", "requires": {"es-to-primitive": "^1.2.1", "function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1", "is-callable": "^1.2.2", "is-negative-zero": "^2.0.0", "is-regex": "^1.1.1", "object-inspect": "^1.8.0", "object-keys": "^1.1.1", "object.assign": "^4.1.1", "string.prototype.trimend": "^1.0.1", "string.prototype.trimstart": "^1.0.1"}}, "es-shim-unscopables": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/es-shim-unscopables/-/es-shim-unscopables-1.0.0.tgz", "integrity": "sha1-cC5jIZMgHj7fhxNjXQg9N45RAkE=", "requires": {"has": "^1.0.3"}}, "es-to-primitive": {"version": "1.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/es-to-primitive/download/es-to-primitive-1.2.1.tgz", "integrity": "sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=", "requires": {"is-callable": "^1.1.4", "is-date-object": "^1.0.1", "is-symbol": "^1.0.2"}}, "escape-string-regexp": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="}, "eslint": {"version": "5.16.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/eslint/download/eslint-5.16.0.tgz", "integrity": "sha1-oeOsGq5KP72Clvz496tzFMu2q+o=", "requires": {"@babel/code-frame": "^7.0.0", "ajv": "^6.9.1", "chalk": "^2.1.0", "cross-spawn": "^6.0.5", "debug": "^4.0.1", "doctrine": "^3.0.0", "eslint-scope": "^4.0.3", "eslint-utils": "^1.3.1", "eslint-visitor-keys": "^1.0.0", "espree": "^5.0.1", "esquery": "^1.0.1", "esutils": "^2.0.2", "file-entry-cache": "^5.0.1", "functional-red-black-tree": "^1.0.1", "glob": "^7.1.2", "globals": "^11.7.0", "ignore": "^4.0.6", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "inquirer": "^6.2.2", "js-yaml": "^3.13.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.3.0", "lodash": "^4.17.11", "minimatch": "^3.0.4", "mkdirp": "^0.5.1", "natural-compare": "^1.4.0", "optionator": "^0.8.2", "path-is-inside": "^1.0.2", "progress": "^2.0.0", "regexpp": "^2.0.1", "semver": "^5.5.1", "strip-ansi": "^4.0.0", "strip-json-comments": "^2.0.1", "table": "^5.2.3", "text-table": "^0.2.0"}}, "eslint-config-airbnb": {"version": "17.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/eslint-config-airbnb/download/eslint-config-airbnb-17.1.1.tgz", "integrity": "sha1-InLguGux4rE4zfiNB6O29M2j1iY=", "requires": {"eslint-config-airbnb-base": "^13.2.0", "object.assign": "^4.1.0", "object.entries": "^1.1.0"}}, "eslint-config-airbnb-base": {"version": "13.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/eslint-config-airbnb-base/download/eslint-config-airbnb-base-13.2.0.tgz", "integrity": "sha1-9uqBRZ/03sLdogDDXx2PdBnVeUM=", "requires": {"confusing-browser-globals": "^1.0.5", "object.assign": "^4.1.0", "object.entries": "^1.1.0"}}, "eslint-config-prettier": {"version": "7.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eslint-config-prettier/-/eslint-config-prettier-7.2.0.tgz", "integrity": "sha1-9KS9KDLoEOjMfBQR7IWz6FwMU/k="}, "eslint-import-resolver-node": {"version": "0.3.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.6.tgz", "integrity": "sha1-QEi5WDldqJZoJSAB29nsprg7rL0=", "requires": {"debug": "^3.2.7", "resolve": "^1.20.0"}, "dependencies": {"debug": {"version": "3.2.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/debug/-/debug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "requires": {"ms": "^2.1.1"}}, "is-core-module": {"version": "2.10.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-core-module/-/is-core-module-2.10.0.tgz", "integrity": "sha1-kBLt4KkcaVh+ZHUU4dUncBnnKO0=", "requires": {"has": "^1.0.3"}}, "path-parse": {"version": "1.0.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU="}, "resolve": {"version": "1.22.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/resolve/-/resolve-1.22.1.tgz", "integrity": "sha1-J8suu1P5GrtJRwqSi7p1WAZqwXc=", "requires": {"is-core-module": "^2.9.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}}}}, "eslint-module-utils": {"version": "2.7.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eslint-module-utils/-/eslint-module-utils-2.7.4.tgz", "integrity": "sha1-Tz5BEWqvE6IHkiYeYdOi5+BYOXQ=", "requires": {"debug": "^3.2.7"}, "dependencies": {"debug": {"version": "3.2.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/debug/-/debug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "requires": {"ms": "^2.1.1"}}}}, "eslint-plugin-es": {"version": "1.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/eslint-plugin-es/download/eslint-plugin-es-1.4.1.tgz", "integrity": "sha1-EqyuD0lT52ukRL/RsicQgaxiCZg=", "requires": {"eslint-utils": "^1.4.2", "regexpp": "^2.0.1"}}, "eslint-plugin-import": {"version": "2.26.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eslint-plugin-import/-/eslint-plugin-import-2.26.0.tgz", "integrity": "sha1-+BLcR75PK3K0eKAhYFpZ/G/ouIs=", "requires": {"array-includes": "^3.1.4", "array.prototype.flat": "^1.2.5", "debug": "^2.6.9", "doctrine": "^2.1.0", "eslint-import-resolver-node": "^0.3.6", "eslint-module-utils": "^2.7.3", "has": "^1.0.3", "is-core-module": "^2.8.1", "is-glob": "^4.0.3", "minimatch": "^3.1.2", "object.values": "^1.1.5", "resolve": "^1.22.0", "tsconfig-paths": "^3.14.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "doctrine": {"version": "2.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/doctrine/-/doctrine-2.1.0.tgz", "integrity": "sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=", "requires": {"esutils": "^2.0.2"}}, "is-core-module": {"version": "2.10.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-core-module/-/is-core-module-2.10.0.tgz", "integrity": "sha1-kBLt4KkcaVh+ZHUU4dUncBnnKO0=", "requires": {"has": "^1.0.3"}}, "minimatch": {"version": "3.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "requires": {"brace-expansion": "^1.1.7"}}, "ms": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "path-parse": {"version": "1.0.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU="}, "resolve": {"version": "1.22.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/resolve/-/resolve-1.22.1.tgz", "integrity": "sha1-J8suu1P5GrtJRwqSi7p1WAZqwXc=", "requires": {"is-core-module": "^2.9.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}}}}, "eslint-plugin-jsx-a11y": {"version": "6.6.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eslint-plugin-jsx-a11y/-/eslint-plugin-jsx-a11y-6.6.1.tgz", "integrity": "sha1-k3NvyRuD/cOMyNEV3u38MJGu8f8=", "requires": {"@babel/runtime": "^7.18.9", "aria-query": "^4.2.2", "array-includes": "^3.1.5", "ast-types-flow": "^0.0.7", "axe-core": "^4.4.3", "axobject-query": "^2.2.0", "damerau-levenshtein": "^1.0.8", "emoji-regex": "^9.2.2", "has": "^1.0.3", "jsx-ast-utils": "^3.3.2", "language-tags": "^1.0.5", "minimatch": "^3.1.2", "semver": "^6.3.0"}, "dependencies": {"emoji-regex": {"version": "9.2.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/emoji-regex/-/emoji-regex-9.2.2.tgz", "integrity": "sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI="}, "minimatch": {"version": "3.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "requires": {"brace-expansion": "^1.1.7"}}, "semver": {"version": "6.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="}}}, "eslint-plugin-node": {"version": "7.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/eslint-plugin-node/download/eslint-plugin-node-7.0.1.tgz", "integrity": "sha1-puBU5QGZsu3YVRi4m057MjyfNts=", "requires": {"eslint-plugin-es": "^1.3.1", "eslint-utils": "^1.3.1", "ignore": "^4.0.2", "minimatch": "^3.0.4", "resolve": "^1.8.1", "semver": "^5.5.0"}}, "eslint-plugin-prettier": {"version": "3.4.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eslint-plugin-prettier/-/eslint-plugin-prettier-3.4.1.tgz", "integrity": "sha1-6d2yAO+289Bf/oOxZlpxavSjh+U=", "requires": {"prettier-linter-helpers": "^1.0.0"}}, "eslint-plugin-promise": {"version": "4.3.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eslint-plugin-promise/-/eslint-plugin-promise-4.3.1.tgz", "integrity": "sha1-YUhd8qNZ4DFJ/a/AposOAwrSrEU="}, "eslint-plugin-react": {"version": "7.31.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eslint-plugin-react/-/eslint-plugin-react-7.31.7.tgz", "integrity": "sha1-NvscYRp9tfdX/OCcu8wBaC+LD7s=", "requires": {"array-includes": "^3.1.5", "array.prototype.flatmap": "^1.3.0", "doctrine": "^2.1.0", "estraverse": "^5.3.0", "jsx-ast-utils": "^2.4.1 || ^3.0.0", "minimatch": "^3.1.2", "object.entries": "^1.1.5", "object.fromentries": "^2.0.5", "object.hasown": "^1.1.1", "object.values": "^1.1.5", "prop-types": "^15.8.1", "resolve": "^2.0.0-next.3", "semver": "^6.3.0", "string.prototype.matchall": "^4.0.7"}, "dependencies": {"call-bind": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "doctrine": {"version": "2.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/doctrine/-/doctrine-2.1.0.tgz", "integrity": "sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=", "requires": {"esutils": "^2.0.2"}}, "es-abstract": {"version": "1.20.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/es-abstract/-/es-abstract-1.20.2.tgz", "integrity": "sha1-hJWge8VtNCo7jqOrAb2YZwDCzLM=", "requires": {"call-bind": "^1.0.2", "es-to-primitive": "^1.2.1", "function-bind": "^1.1.1", "function.prototype.name": "^1.1.5", "get-intrinsic": "^1.1.2", "get-symbol-description": "^1.0.0", "has": "^1.0.3", "has-property-descriptors": "^1.0.0", "has-symbols": "^1.0.3", "internal-slot": "^1.0.3", "is-callable": "^1.2.4", "is-negative-zero": "^2.0.2", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "is-string": "^1.0.7", "is-weakref": "^1.0.2", "object-inspect": "^1.12.2", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.4.3", "string.prototype.trimend": "^1.0.5", "string.prototype.trimstart": "^1.0.5", "unbox-primitive": "^1.0.2"}, "dependencies": {"get-intrinsic": {"version": "1.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/get-intrinsic/-/get-intrinsic-1.1.2.tgz", "integrity": "sha1-M2l1Ej4FrQt7pB8VLuSq2+ps9Zg=", "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}}}}, "estraverse": {"version": "5.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/estraverse/-/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM="}, "has-symbols": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg="}, "is-callable": {"version": "1.2.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-callable/-/is-callable-1.2.4.tgz", "integrity": "sha1-RzAdWN0CWUB4ZVR4U99tYf5HGUU="}, "is-core-module": {"version": "2.10.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-core-module/-/is-core-module-2.10.0.tgz", "integrity": "sha1-kBLt4KkcaVh+ZHUU4dUncBnnKO0=", "requires": {"has": "^1.0.3"}}, "is-negative-zero": {"version": "2.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-negative-zero/-/is-negative-zero-2.0.2.tgz", "integrity": "sha1-e/bwOigAO4s5Zd46wm9mTXZfMVA="}, "is-regex": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-regex/-/is-regex-1.1.4.tgz", "integrity": "sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=", "requires": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}}, "minimatch": {"version": "3.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "requires": {"brace-expansion": "^1.1.7"}}, "object-inspect": {"version": "1.12.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object-inspect/-/object-inspect-1.12.2.tgz", "integrity": "sha1-wGQfJjlFMvKKuNeWq5VOQ8AJqOo="}, "object.assign": {"version": "4.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object.assign/-/object.assign-4.1.4.tgz", "integrity": "sha1-lnPHx8NRq4xNC1FvQ0Pr9N+3eZ8=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}, "object.entries": {"version": "1.1.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object.entries/-/object.entries-1.1.5.tgz", "integrity": "sha1-4azdF8TeLNltWghIfPuduE2IGGE=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.3", "es-abstract": "^1.19.1"}}, "path-parse": {"version": "1.0.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU="}, "resolve": {"version": "2.0.0-next.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/resolve/-/resolve-2.0.0-next.4.tgz", "integrity": "sha1-PTehE9ZCn0luxHUtKi5Y77H9RmA=", "requires": {"is-core-module": "^2.9.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}}, "semver": {"version": "6.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="}, "string.prototype.trimend": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string.prototype.trimend/-/string.prototype.trimend-1.0.5.tgz", "integrity": "sha1-kUpluqqyX73U7ikcp93lfoacuNA=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.19.5"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}, "string.prototype.trimstart": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string.prototype.trimstart/-/string.prototype.trimstart-1.0.5.tgz", "integrity": "sha1-VGbZO6WM+iE0g5+B1/QkN+jAH+8=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.19.5"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}}}, "eslint-plugin-standard": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/eslint-plugin-standard/download/eslint-plugin-standard-4.1.0.tgz", "integrity": "sha1-DDvzpn6FP4u7xYD7SUX78W9Bt8U="}, "eslint-scope": {"version": "4.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/eslint-scope/download/eslint-scope-4.0.3.tgz", "integrity": "sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=", "requires": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}}, "eslint-utils": {"version": "1.4.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/eslint-utils/download/eslint-utils-1.4.3.tgz", "integrity": "sha1-dP7HxU0Hdrb2fgJRBAtYBlZOmB8=", "requires": {"eslint-visitor-keys": "^1.1.0"}}, "eslint-visitor-keys": {"version": "1.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz", "integrity": "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4="}, "espree": {"version": "5.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/espree/download/espree-5.0.1.tgz", "integrity": "sha1-XWUm+k/H8HiKXPdbFfMDI+L4H3o=", "requires": {"acorn": "^6.0.7", "acorn-jsx": "^5.0.0", "eslint-visitor-keys": "^1.0.0"}}, "esprima": {"version": "4.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/esprima/download/esprima-4.0.1.tgz", "integrity": "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE="}, "esquery": {"version": "1.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/esquery/download/esquery-1.3.1.tgz", "integrity": "sha1-t4tYKKqOIU4p+3TE1bdS4cAz2lc=", "requires": {"estraverse": "^5.1.0"}, "dependencies": {"estraverse": {"version": "5.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/estraverse/download/estraverse-5.2.0.tgz", "integrity": "sha1-MH30JUfmzHMk088DwVXVzbjFOIA="}}}, "esrecurse": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/esrecurse/download/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "requires": {"estraverse": "^5.2.0"}, "dependencies": {"estraverse": {"version": "5.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/estraverse/download/estraverse-5.2.0.tgz", "integrity": "sha1-MH30JUfmzHMk088DwVXVzbjFOIA="}}}, "estraverse": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/estraverse/download/estraverse-4.3.0.tgz", "integrity": "sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0="}, "esutils": {"version": "2.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/esutils/download/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q="}, "external-editor": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/external-editor/download/external-editor-3.1.0.tgz", "integrity": "sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=", "requires": {"chardet": "^0.7.0", "iconv-lite": "^0.4.24", "tmp": "^0.0.33"}}, "fast-deep-equal": {"version": "3.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU="}, "fast-diff": {"version": "1.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/fast-diff/-/fast-diff-1.2.0.tgz", "integrity": "sha1-c+4RmC2Gyq95WYKNUZz+kn+sXwM="}, "fast-json-stable-stringify": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM="}, "fast-levenshtein": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc="}, "figures": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/figures/-/figures-2.0.0.tgz", "integrity": "sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=", "requires": {"escape-string-regexp": "^1.0.5"}}, "file-entry-cache": {"version": "5.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/file-entry-cache/download/file-entry-cache-5.0.1.tgz", "integrity": "sha1-yg9u+m3T1WEzP7FFFQZcL6/fQ5w=", "requires": {"flat-cache": "^2.0.1"}}, "flat-cache": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/flat-cache/download/flat-cache-2.0.1.tgz", "integrity": "sha1-XSltbwS9pEpGMKMBQTvbwuwIXsA=", "requires": {"flatted": "^2.0.0", "rimraf": "2.6.3", "write": "1.0.3"}}, "flatted": {"version": "2.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/flatted/download/flatted-2.0.2.tgz", "integrity": "sha1-RXWyHivO50NKqb5mL0t7X5wrUTg="}, "fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="}, "function-bind": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/function-bind/download/function-bind-1.1.1.tgz", "integrity": "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0="}, "function.prototype.name": {"version": "1.1.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/function.prototype.name/-/function.prototype.name-1.1.5.tgz", "integrity": "sha1-zOBQX+H/uAUD5vnkbMZORqEqliE=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.3", "es-abstract": "^1.19.0", "functions-have-names": "^1.2.2"}, "dependencies": {"call-bind": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "es-abstract": {"version": "1.20.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/es-abstract/-/es-abstract-1.20.2.tgz", "integrity": "sha1-hJWge8VtNCo7jqOrAb2YZwDCzLM=", "requires": {"call-bind": "^1.0.2", "es-to-primitive": "^1.2.1", "function-bind": "^1.1.1", "function.prototype.name": "^1.1.5", "get-intrinsic": "^1.1.2", "get-symbol-description": "^1.0.0", "has": "^1.0.3", "has-property-descriptors": "^1.0.0", "has-symbols": "^1.0.3", "internal-slot": "^1.0.3", "is-callable": "^1.2.4", "is-negative-zero": "^2.0.2", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "is-string": "^1.0.7", "is-weakref": "^1.0.2", "object-inspect": "^1.12.2", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.4.3", "string.prototype.trimend": "^1.0.5", "string.prototype.trimstart": "^1.0.5", "unbox-primitive": "^1.0.2"}, "dependencies": {"get-intrinsic": {"version": "1.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/get-intrinsic/-/get-intrinsic-1.1.2.tgz", "integrity": "sha1-M2l1Ej4FrQt7pB8VLuSq2+ps9Zg=", "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}}}}, "has-symbols": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg="}, "is-callable": {"version": "1.2.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-callable/-/is-callable-1.2.4.tgz", "integrity": "sha1-RzAdWN0CWUB4ZVR4U99tYf5HGUU="}, "is-negative-zero": {"version": "2.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-negative-zero/-/is-negative-zero-2.0.2.tgz", "integrity": "sha1-e/bwOigAO4s5Zd46wm9mTXZfMVA="}, "is-regex": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-regex/-/is-regex-1.1.4.tgz", "integrity": "sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=", "requires": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}}, "object-inspect": {"version": "1.12.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object-inspect/-/object-inspect-1.12.2.tgz", "integrity": "sha1-wGQfJjlFMvKKuNeWq5VOQ8AJqOo="}, "object.assign": {"version": "4.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object.assign/-/object.assign-4.1.4.tgz", "integrity": "sha1-lnPHx8NRq4xNC1FvQ0Pr9N+3eZ8=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}, "string.prototype.trimend": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string.prototype.trimend/-/string.prototype.trimend-1.0.5.tgz", "integrity": "sha1-kUpluqqyX73U7ikcp93lfoacuNA=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.19.5"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}, "string.prototype.trimstart": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string.prototype.trimstart/-/string.prototype.trimstart-1.0.5.tgz", "integrity": "sha1-VGbZO6WM+iE0g5+B1/QkN+jAH+8=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.19.5"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}}}, "functional-red-black-tree": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz", "integrity": "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc="}, "functions-have-names": {"version": "1.2.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/functions-have-names/-/functions-have-names-1.2.3.tgz", "integrity": "sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ="}, "get-intrinsic": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/get-intrinsic/download/get-intrinsic-1.0.2.tgz", "integrity": "sha1-aCDaIm5QskiU4IhZRp3Gg2FUXUk=", "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1"}}, "get-symbol-description": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/get-symbol-description/-/get-symbol-description-1.0.0.tgz", "integrity": "sha1-f9uByQAQH71WTdXxowr1qtweWNY=", "requires": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.1"}, "dependencies": {"call-bind": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "get-intrinsic": {"version": "1.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/get-intrinsic/-/get-intrinsic-1.1.2.tgz", "integrity": "sha1-M2l1Ej4FrQt7pB8VLuSq2+ps9Zg=", "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}}, "has-symbols": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg="}}}, "glob": {"version": "7.1.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/glob/download/glob-7.1.6.tgz", "integrity": "sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY=", "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "globals": {"version": "11.12.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/globals/download/globals-11.12.0.tgz", "integrity": "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4="}, "has": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has/download/has-1.0.3.tgz", "integrity": "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=", "requires": {"function-bind": "^1.1.1"}}, "has-bigints": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-bigints/-/has-bigints-1.0.2.tgz", "integrity": "sha1-CHG9Pj1RYm9soJZmaLo11WAtbqo="}, "has-flag": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="}, "has-property-descriptors": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-property-descriptors/-/has-property-descriptors-1.0.0.tgz", "integrity": "sha1-YQcIYAYG02lh7QTBlhk7amB/qGE=", "requires": {"get-intrinsic": "^1.1.1"}, "dependencies": {"get-intrinsic": {"version": "1.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/get-intrinsic/-/get-intrinsic-1.1.2.tgz", "integrity": "sha1-M2l1Ej4FrQt7pB8VLuSq2+ps9Zg=", "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}}, "has-symbols": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg="}}}, "has-symbols": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-symbols/download/has-symbols-1.0.1.tgz", "integrity": "sha1-n1IUdYpEGWxAbZvXbOv4HsLdMeg="}, "has-tostringtag": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-tostringtag/-/has-tostringtag-1.0.0.tgz", "integrity": "sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU=", "requires": {"has-symbols": "^1.0.2"}, "dependencies": {"has-symbols": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg="}}}, "iconv-lite": {"version": "0.4.24", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/iconv-lite/download/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "ignore": {"version": "4.0.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ignore/download/ignore-4.0.6.tgz", "integrity": "sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw="}, "import-fresh": {"version": "3.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/import-fresh/download/import-fresh-3.3.0.tgz", "integrity": "sha1-NxYsJfy566oublPVtNiM4X2eDCs=", "requires": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}}, "imurmurhash": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o="}, "inflight": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/inherits/download/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="}, "inquirer": {"version": "6.5.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/inquirer/download/inquirer-6.5.2.tgz", "integrity": "sha1-rVCUI3XQNtMn/1KMCL1fqwiZKMo=", "requires": {"ansi-escapes": "^3.2.0", "chalk": "^2.4.2", "cli-cursor": "^2.1.0", "cli-width": "^2.0.0", "external-editor": "^3.0.3", "figures": "^2.0.0", "lodash": "^4.17.12", "mute-stream": "0.0.7", "run-async": "^2.2.0", "rxjs": "^6.4.0", "string-width": "^2.1.0", "strip-ansi": "^5.1.0", "through": "^2.3.6"}, "dependencies": {"ansi-regex": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-regex/download/ansi-regex-4.1.0.tgz", "integrity": "sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc="}, "strip-ansi": {"version": "5.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/strip-ansi/download/strip-ansi-5.2.0.tgz", "integrity": "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=", "requires": {"ansi-regex": "^4.1.0"}}}}, "internal-slot": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/internal-slot/-/internal-slot-1.0.3.tgz", "integrity": "sha1-c0fjB97uovqsKsYgXUvH00ln9Zw=", "requires": {"get-intrinsic": "^1.1.0", "has": "^1.0.3", "side-channel": "^1.0.4"}, "dependencies": {"get-intrinsic": {"version": "1.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/get-intrinsic/-/get-intrinsic-1.1.2.tgz", "integrity": "sha1-M2l1Ej4FrQt7pB8VLuSq2+ps9Zg=", "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}}, "has-symbols": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg="}}}, "is-bigint": {"version": "1.0.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-bigint/-/is-bigint-1.0.4.tgz", "integrity": "sha1-CBR6GHW8KzIAXUHM2Ckd/8ZpHfM=", "requires": {"has-bigints": "^1.0.1"}}, "is-boolean-object": {"version": "1.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-boolean-object/-/is-boolean-object-1.1.2.tgz", "integrity": "sha1-XG3CACRt2TIa5LiFoRS7H3X2Nxk=", "requires": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "dependencies": {"call-bind": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}}}, "is-callable": {"version": "1.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-callable/download/is-callable-1.2.2.tgz", "integrity": "sha1-x8ZxXNItTdtI0+GZcCI6zquwgNk="}, "is-core-module": {"version": "2.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-core-module/download/is-core-module-2.2.0.tgz", "integrity": "sha1-lwN+89UiJNhRY/VZeytj2a/tmBo=", "requires": {"has": "^1.0.3"}}, "is-date-object": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-date-object/download/is-date-object-1.0.2.tgz", "integrity": "sha1-vac28s2P0G0yhE53Q7+nSUw7/X4="}, "is-extglob": {"version": "2.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="}, "is-fullwidth-code-point": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8="}, "is-glob": {"version": "4.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "requires": {"is-extglob": "^2.1.1"}}, "is-negative-zero": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-negative-zero/download/is-negative-zero-2.0.1.tgz", "integrity": "sha1-PedGwY3aIxkkGlNnWQjY92bxHCQ="}, "is-number-object": {"version": "1.0.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-number-object/-/is-number-object-1.0.7.tgz", "integrity": "sha1-WdUK2kxFJReE6ZBPUkbHQvB6Qvw=", "requires": {"has-tostringtag": "^1.0.0"}}, "is-regex": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-regex/download/is-regex-1.1.1.tgz", "integrity": "sha1-xvmKrMVG9s7FRooHt7FTq1ZKV7k=", "requires": {"has-symbols": "^1.0.1"}}, "is-shared-array-buffer": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-shared-array-buffer/-/is-shared-array-buffer-1.0.2.tgz", "integrity": "sha1-jyWcVztgtqMtQFihoHQwwKc0THk=", "requires": {"call-bind": "^1.0.2"}, "dependencies": {"call-bind": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}}}, "is-string": {"version": "1.0.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-string/-/is-string-1.0.7.tgz", "integrity": "sha1-DdEr8gBvJVu1j2lREO/3SR7rwP0=", "requires": {"has-tostringtag": "^1.0.0"}}, "is-symbol": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-symbol/download/is-symbol-1.0.3.tgz", "integrity": "sha1-OOEBS55jKb4N6dJKQU/XRB7GGTc=", "requires": {"has-symbols": "^1.0.1"}}, "is-weakref": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-weakref/-/is-weakref-1.0.2.tgz", "integrity": "sha1-lSnzg6kzggXol2XgOS78LxAPBvI=", "requires": {"call-bind": "^1.0.2"}, "dependencies": {"call-bind": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}}}, "isexe": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="}, "js-tokens": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/js-tokens/download/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk="}, "js-yaml": {"version": "3.14.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/js-yaml/download/js-yaml-3.14.1.tgz", "integrity": "sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=", "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "json-schema-traverse": {"version": "0.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA="}, "json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE="}, "json5": {"version": "1.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/json5/-/json5-1.0.1.tgz", "integrity": "sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4=", "requires": {"minimist": "^1.2.0"}}, "jsx-ast-utils": {"version": "3.3.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/jsx-ast-utils/-/jsx-ast-utils-3.3.3.tgz", "integrity": "sha1-drPm5s7OXGnUmleSw9Ab0aDNx+o=", "requires": {"array-includes": "^3.1.5", "object.assign": "^4.1.3"}, "dependencies": {"call-bind": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}, "has-symbols": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg="}, "object.assign": {"version": "4.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object.assign/-/object.assign-4.1.4.tgz", "integrity": "sha1-lnPHx8NRq4xNC1FvQ0Pr9N+3eZ8=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}}}}, "language-subtag-registry": {"version": "0.3.22", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/language-subtag-registry/-/language-subtag-registry-0.3.22.tgz", "integrity": "sha1-LhUAhhsuRX66fnroaHfL0I+h/R0="}, "language-tags": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/language-tags/-/language-tags-1.0.5.tgz", "integrity": "sha1-0yHbxNowuovzAk4ED6XBRmH5GTo=", "requires": {"language-subtag-registry": "~0.3.2"}}, "levn": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/levn/-/levn-0.3.0.tgz", "integrity": "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=", "requires": {"prelude-ls": "~1.1.2", "type-check": "~0.3.2"}}, "lodash": {"version": "4.17.20", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/lodash/download/lodash-4.17.20.tgz", "integrity": "sha1-tEqbYpe8tpjxxRo1RaKzs2jVnFI="}, "loose-envify": {"version": "1.4.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/loose-envify/-/loose-envify-1.4.0.tgz", "integrity": "sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=", "requires": {"js-tokens": "^3.0.0 || ^4.0.0"}}, "mimic-fn": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/mimic-fn/download/mimic-fn-1.2.0.tgz", "integrity": "sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI="}, "minimatch": {"version": "3.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/minimatch/download/minimatch-3.0.4.tgz", "integrity": "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=", "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/minimist/download/minimist-1.2.5.tgz", "integrity": "sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI="}, "mkdirp": {"version": "0.5.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/mkdirp/download/mkdirp-0.5.5.tgz", "integrity": "sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=", "requires": {"minimist": "^1.2.5"}}, "ms": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/download/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}, "mute-stream": {"version": "0.0.7", "resolved": "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.7.tgz", "integrity": "sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s="}, "natural-compare": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc="}, "nice-try": {"version": "1.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/nice-try/download/nice-try-1.0.5.tgz", "integrity": "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y="}, "object-assign": {"version": "4.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="}, "object-inspect": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object-inspect/download/object-inspect-1.9.0.tgz", "integrity": "sha1-yQUh104RJ7ZyZt7TOUrWEWmGUzo="}, "object-keys": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object-keys/download/object-keys-1.1.1.tgz", "integrity": "sha1-HEfyct8nfzsdrwYWd9nILiMixg4="}, "object.assign": {"version": "4.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object.assign/download/object.assign-4.1.2.tgz", "integrity": "sha1-DtVKNC7Os3s4/3brgxoOeIy2OUA=", "requires": {"call-bind": "^1.0.0", "define-properties": "^1.1.3", "has-symbols": "^1.0.1", "object-keys": "^1.1.1"}}, "object.entries": {"version": "1.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object.entries/download/object.entries-1.1.3.tgz", "integrity": "sha1-xgHH8Wi2I3RUGgfdvT4tXk93EaY=", "requires": {"call-bind": "^1.0.0", "define-properties": "^1.1.3", "es-abstract": "^1.18.0-next.1", "has": "^1.0.3"}}, "object.fromentries": {"version": "2.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object.fromentries/-/object.fromentries-2.0.5.tgz", "integrity": "sha1-ezeyBRCcIedB5gVyf+iwrV+gglE=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.3", "es-abstract": "^1.19.1"}, "dependencies": {"call-bind": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "es-abstract": {"version": "1.20.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/es-abstract/-/es-abstract-1.20.2.tgz", "integrity": "sha1-hJWge8VtNCo7jqOrAb2YZwDCzLM=", "requires": {"call-bind": "^1.0.2", "es-to-primitive": "^1.2.1", "function-bind": "^1.1.1", "function.prototype.name": "^1.1.5", "get-intrinsic": "^1.1.2", "get-symbol-description": "^1.0.0", "has": "^1.0.3", "has-property-descriptors": "^1.0.0", "has-symbols": "^1.0.3", "internal-slot": "^1.0.3", "is-callable": "^1.2.4", "is-negative-zero": "^2.0.2", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "is-string": "^1.0.7", "is-weakref": "^1.0.2", "object-inspect": "^1.12.2", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.4.3", "string.prototype.trimend": "^1.0.5", "string.prototype.trimstart": "^1.0.5", "unbox-primitive": "^1.0.2"}, "dependencies": {"get-intrinsic": {"version": "1.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/get-intrinsic/-/get-intrinsic-1.1.2.tgz", "integrity": "sha1-M2l1Ej4FrQt7pB8VLuSq2+ps9Zg=", "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}}}}, "has-symbols": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg="}, "is-callable": {"version": "1.2.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-callable/-/is-callable-1.2.4.tgz", "integrity": "sha1-RzAdWN0CWUB4ZVR4U99tYf5HGUU="}, "is-negative-zero": {"version": "2.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-negative-zero/-/is-negative-zero-2.0.2.tgz", "integrity": "sha1-e/bwOigAO4s5Zd46wm9mTXZfMVA="}, "is-regex": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-regex/-/is-regex-1.1.4.tgz", "integrity": "sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=", "requires": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}}, "object-inspect": {"version": "1.12.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object-inspect/-/object-inspect-1.12.2.tgz", "integrity": "sha1-wGQfJjlFMvKKuNeWq5VOQ8AJqOo="}, "object.assign": {"version": "4.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object.assign/-/object.assign-4.1.4.tgz", "integrity": "sha1-lnPHx8NRq4xNC1FvQ0Pr9N+3eZ8=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}, "string.prototype.trimend": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string.prototype.trimend/-/string.prototype.trimend-1.0.5.tgz", "integrity": "sha1-kUpluqqyX73U7ikcp93lfoacuNA=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.19.5"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}, "string.prototype.trimstart": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string.prototype.trimstart/-/string.prototype.trimstart-1.0.5.tgz", "integrity": "sha1-VGbZO6WM+iE0g5+B1/QkN+jAH+8=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.19.5"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}}}, "object.hasown": {"version": "1.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object.hasown/-/object.hasown-1.1.1.tgz", "integrity": "sha1-rR7sxg0D9JRgYAQw2X8jiCz1kqM=", "requires": {"define-properties": "^1.1.4", "es-abstract": "^1.19.5"}, "dependencies": {"call-bind": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}, "es-abstract": {"version": "1.20.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/es-abstract/-/es-abstract-1.20.2.tgz", "integrity": "sha1-hJWge8VtNCo7jqOrAb2YZwDCzLM=", "requires": {"call-bind": "^1.0.2", "es-to-primitive": "^1.2.1", "function-bind": "^1.1.1", "function.prototype.name": "^1.1.5", "get-intrinsic": "^1.1.2", "get-symbol-description": "^1.0.0", "has": "^1.0.3", "has-property-descriptors": "^1.0.0", "has-symbols": "^1.0.3", "internal-slot": "^1.0.3", "is-callable": "^1.2.4", "is-negative-zero": "^2.0.2", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "is-string": "^1.0.7", "is-weakref": "^1.0.2", "object-inspect": "^1.12.2", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.4.3", "string.prototype.trimend": "^1.0.5", "string.prototype.trimstart": "^1.0.5", "unbox-primitive": "^1.0.2"}}, "get-intrinsic": {"version": "1.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/get-intrinsic/-/get-intrinsic-1.1.2.tgz", "integrity": "sha1-M2l1Ej4FrQt7pB8VLuSq2+ps9Zg=", "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}}, "has-symbols": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg="}, "is-callable": {"version": "1.2.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-callable/-/is-callable-1.2.4.tgz", "integrity": "sha1-RzAdWN0CWUB4ZVR4U99tYf5HGUU="}, "is-negative-zero": {"version": "2.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-negative-zero/-/is-negative-zero-2.0.2.tgz", "integrity": "sha1-e/bwOigAO4s5Zd46wm9mTXZfMVA="}, "is-regex": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-regex/-/is-regex-1.1.4.tgz", "integrity": "sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=", "requires": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}}, "object-inspect": {"version": "1.12.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object-inspect/-/object-inspect-1.12.2.tgz", "integrity": "sha1-wGQfJjlFMvKKuNeWq5VOQ8AJqOo="}, "object.assign": {"version": "4.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object.assign/-/object.assign-4.1.4.tgz", "integrity": "sha1-lnPHx8NRq4xNC1FvQ0Pr9N+3eZ8=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}}, "string.prototype.trimend": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string.prototype.trimend/-/string.prototype.trimend-1.0.5.tgz", "integrity": "sha1-kUpluqqyX73U7ikcp93lfoacuNA=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.19.5"}}, "string.prototype.trimstart": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string.prototype.trimstart/-/string.prototype.trimstart-1.0.5.tgz", "integrity": "sha1-VGbZO6WM+iE0g5+B1/QkN+jAH+8=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.19.5"}}}}, "object.values": {"version": "1.1.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object.values/-/object.values-1.1.5.tgz", "integrity": "sha1-lZ9j486e8QhyAzMIITHkpFm3Fqw=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.3", "es-abstract": "^1.19.1"}, "dependencies": {"call-bind": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "es-abstract": {"version": "1.20.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/es-abstract/-/es-abstract-1.20.2.tgz", "integrity": "sha1-hJWge8VtNCo7jqOrAb2YZwDCzLM=", "requires": {"call-bind": "^1.0.2", "es-to-primitive": "^1.2.1", "function-bind": "^1.1.1", "function.prototype.name": "^1.1.5", "get-intrinsic": "^1.1.2", "get-symbol-description": "^1.0.0", "has": "^1.0.3", "has-property-descriptors": "^1.0.0", "has-symbols": "^1.0.3", "internal-slot": "^1.0.3", "is-callable": "^1.2.4", "is-negative-zero": "^2.0.2", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "is-string": "^1.0.7", "is-weakref": "^1.0.2", "object-inspect": "^1.12.2", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.4.3", "string.prototype.trimend": "^1.0.5", "string.prototype.trimstart": "^1.0.5", "unbox-primitive": "^1.0.2"}, "dependencies": {"get-intrinsic": {"version": "1.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/get-intrinsic/-/get-intrinsic-1.1.2.tgz", "integrity": "sha1-M2l1Ej4FrQt7pB8VLuSq2+ps9Zg=", "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}}}}, "has-symbols": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg="}, "is-callable": {"version": "1.2.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-callable/-/is-callable-1.2.4.tgz", "integrity": "sha1-RzAdWN0CWUB4ZVR4U99tYf5HGUU="}, "is-negative-zero": {"version": "2.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-negative-zero/-/is-negative-zero-2.0.2.tgz", "integrity": "sha1-e/bwOigAO4s5Zd46wm9mTXZfMVA="}, "is-regex": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-regex/-/is-regex-1.1.4.tgz", "integrity": "sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=", "requires": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}}, "object-inspect": {"version": "1.12.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object-inspect/-/object-inspect-1.12.2.tgz", "integrity": "sha1-wGQfJjlFMvKKuNeWq5VOQ8AJqOo="}, "object.assign": {"version": "4.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object.assign/-/object.assign-4.1.4.tgz", "integrity": "sha1-lnPHx8NRq4xNC1FvQ0Pr9N+3eZ8=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}, "string.prototype.trimend": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string.prototype.trimend/-/string.prototype.trimend-1.0.5.tgz", "integrity": "sha1-kUpluqqyX73U7ikcp93lfoacuNA=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.19.5"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}, "string.prototype.trimstart": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string.prototype.trimstart/-/string.prototype.trimstart-1.0.5.tgz", "integrity": "sha1-VGbZO6WM+iE0g5+B1/QkN+jAH+8=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.19.5"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}}}, "once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "requires": {"wrappy": "1"}}, "onetime": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/onetime/-/onetime-2.0.1.tgz", "integrity": "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=", "requires": {"mimic-fn": "^1.0.0"}}, "optionator": {"version": "0.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/optionator/download/optionator-0.8.3.tgz", "integrity": "sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=", "requires": {"deep-is": "~0.1.3", "fast-levenshtein": "~2.0.6", "levn": "~0.3.0", "prelude-ls": "~1.1.2", "type-check": "~0.3.2", "word-wrap": "~1.2.3"}}, "os-tmpdir": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "integrity": "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ="}, "parent-module": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/parent-module/download/parent-module-1.0.1.tgz", "integrity": "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=", "requires": {"callsites": "^3.0.0"}}, "path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="}, "path-is-inside": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/path-is-inside/-/path-is-inside-1.0.2.tgz", "integrity": "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM="}, "path-key": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/path-key/-/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A="}, "path-parse": {"version": "1.0.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-parse/download/path-parse-1.0.6.tgz", "integrity": "sha1-1i27VnlAXXLEc37FhgDp3c8G0kw="}, "prelude-ls": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.1.2.tgz", "integrity": "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ="}, "prettier-linter-helpers": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz", "integrity": "sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=", "requires": {"fast-diff": "^1.1.2"}}, "progress": {"version": "2.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/progress/download/progress-2.0.3.tgz", "integrity": "sha1-foz42PW48jnBvGi+tOt4Vn1XLvg="}, "prop-types": {"version": "15.8.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/prop-types/-/prop-types-15.8.1.tgz", "integrity": "sha1-QinMbUvONxg5I/L3Vks+kDRKGBI=", "requires": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}}, "punycode": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/punycode/download/punycode-2.1.1.tgz", "integrity": "sha1-tYsBCsQMIsVldhbI0sLALHv0eew="}, "react-is": {"version": "16.13.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/react-is/-/react-is-16.13.1.tgz", "integrity": "sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ="}, "regenerator-runtime": {"version": "0.13.9", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/regenerator-runtime/-/regenerator-runtime-0.13.9.tgz", "integrity": "sha1-iSV0Kpj/2QgUmI11Zq0wyjsmO1I="}, "regexp.prototype.flags": {"version": "1.4.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/regexp.prototype.flags/-/regexp.prototype.flags-1.4.3.tgz", "integrity": "sha1-h8qzD4D2ZmAYGju3v1mBqHKzZ6w=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.3", "functions-have-names": "^1.2.2"}, "dependencies": {"call-bind": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}}}, "regexpp": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/regexpp/download/regexpp-2.0.1.tgz", "integrity": "sha1-jRnTHPYySCtYkEn4KB+T28uk0H8="}, "resolve": {"version": "1.19.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/resolve/download/resolve-1.19.0.tgz", "integrity": "sha1-GvW/YwQJc0oGfK4pMYqsf6KaJnw=", "requires": {"is-core-module": "^2.1.0", "path-parse": "^1.0.6"}}, "resolve-from": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/resolve-from/download/resolve-from-4.0.0.tgz", "integrity": "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY="}, "restore-cursor": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-2.0.0.tgz", "integrity": "sha1-n37ih/gv0ybU/RYpI9YhKe7g368=", "requires": {"onetime": "^2.0.0", "signal-exit": "^3.0.2"}}, "rimraf": {"version": "2.6.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/rimraf/download/rimraf-2.6.3.tgz", "integrity": "sha1-stEE/g2Psnz54KHNqCYt04M8bKs=", "requires": {"glob": "^7.1.3"}}, "run-async": {"version": "2.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/run-async/download/run-async-2.4.1.tgz", "integrity": "sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU="}, "rxjs": {"version": "6.6.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/rxjs/download/rxjs-6.6.3.tgz", "integrity": "sha1-jKhGNcTaqQDA05Z6buesYCce5VI=", "requires": {"tslib": "^1.9.0"}}, "safer-buffer": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/safer-buffer/download/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="}, "semver": {"version": "5.7.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/download/semver-5.7.1.tgz", "integrity": "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="}, "shebang-command": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM="}, "side-channel": {"version": "1.0.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/side-channel/-/side-channel-1.0.4.tgz", "integrity": "sha1-785cj9wQTudRslxY1CkAEfpeos8=", "requires": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}}, "signal-exit": {"version": "3.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/signal-exit/download/signal-exit-3.0.3.tgz", "integrity": "sha1-oUEMLt2PB3sItOJTyOrPyvBXRhw="}, "slice-ansi": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/slice-ansi/download/slice-ansi-2.1.0.tgz", "integrity": "sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY=", "requires": {"ansi-styles": "^3.2.0", "astral-regex": "^1.0.0", "is-fullwidth-code-point": "^2.0.0"}}, "sprintf-js": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="}, "string-width": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/string-width/download/string-width-2.1.1.tgz", "integrity": "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=", "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}}, "string.prototype.matchall": {"version": "4.0.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string.prototype.matchall/-/string.prototype.matchall-4.0.7.tgz", "integrity": "sha1-jm7LDYofsf2kcNgazsstugV6SB0=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.3", "es-abstract": "^1.19.1", "get-intrinsic": "^1.1.1", "has-symbols": "^1.0.3", "internal-slot": "^1.0.3", "regexp.prototype.flags": "^1.4.1", "side-channel": "^1.0.4"}, "dependencies": {"call-bind": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "es-abstract": {"version": "1.20.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/es-abstract/-/es-abstract-1.20.2.tgz", "integrity": "sha1-hJWge8VtNCo7jqOrAb2YZwDCzLM=", "requires": {"call-bind": "^1.0.2", "es-to-primitive": "^1.2.1", "function-bind": "^1.1.1", "function.prototype.name": "^1.1.5", "get-intrinsic": "^1.1.2", "get-symbol-description": "^1.0.0", "has": "^1.0.3", "has-property-descriptors": "^1.0.0", "has-symbols": "^1.0.3", "internal-slot": "^1.0.3", "is-callable": "^1.2.4", "is-negative-zero": "^2.0.2", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "is-string": "^1.0.7", "is-weakref": "^1.0.2", "object-inspect": "^1.12.2", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.4.3", "string.prototype.trimend": "^1.0.5", "string.prototype.trimstart": "^1.0.5", "unbox-primitive": "^1.0.2"}}, "get-intrinsic": {"version": "1.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/get-intrinsic/-/get-intrinsic-1.1.2.tgz", "integrity": "sha1-M2l1Ej4FrQt7pB8VLuSq2+ps9Zg=", "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}}, "has-symbols": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg="}, "is-callable": {"version": "1.2.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-callable/-/is-callable-1.2.4.tgz", "integrity": "sha1-RzAdWN0CWUB4ZVR4U99tYf5HGUU="}, "is-negative-zero": {"version": "2.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-negative-zero/-/is-negative-zero-2.0.2.tgz", "integrity": "sha1-e/bwOigAO4s5Zd46wm9mTXZfMVA="}, "is-regex": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-regex/-/is-regex-1.1.4.tgz", "integrity": "sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=", "requires": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}}, "object-inspect": {"version": "1.12.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object-inspect/-/object-inspect-1.12.2.tgz", "integrity": "sha1-wGQfJjlFMvKKuNeWq5VOQ8AJqOo="}, "object.assign": {"version": "4.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/object.assign/-/object.assign-4.1.4.tgz", "integrity": "sha1-lnPHx8NRq4xNC1FvQ0Pr9N+3eZ8=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}, "string.prototype.trimend": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string.prototype.trimend/-/string.prototype.trimend-1.0.5.tgz", "integrity": "sha1-kUpluqqyX73U7ikcp93lfoacuNA=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.19.5"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}, "string.prototype.trimstart": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string.prototype.trimstart/-/string.prototype.trimstart-1.0.5.tgz", "integrity": "sha1-VGbZO6WM+iE0g5+B1/QkN+jAH+8=", "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.19.5"}, "dependencies": {"define-properties": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.4.tgz", "integrity": "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE=", "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}}}}}, "string.prototype.trimend": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/string.prototype.trimend/download/string.prototype.trimend-1.0.3.tgz", "integrity": "sha1-oivVPMpcfPRNfJ1ccyEYhz1s0Ys=", "requires": {"call-bind": "^1.0.0", "define-properties": "^1.1.3"}}, "string.prototype.trimstart": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/string.prototype.trimstart/download/string.prototype.trimstart-1.0.3.tgz", "integrity": "sha1-m0y1kOEjuzZWRAHVmCQpjeUP1ao=", "requires": {"call-bind": "^1.0.0", "define-properties": "^1.1.3"}}, "strip-ansi": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "requires": {"ansi-regex": "^3.0.0"}}, "strip-bom": {"version": "3.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/strip-bom/-/strip-bom-3.0.0.tgz", "integrity": "sha1-NG/BXiSPQ1MJZnZSY1Gb01Kpbxk="}, "strip-json-comments": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz", "integrity": "sha1-PFMZQukIwml8DsNEhYwobHygpgo="}, "supports-color": {"version": "5.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "requires": {"has-flag": "^3.0.0"}}, "supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha1-btpL00SjyUrqN21MwxvHcxEDngk="}, "table": {"version": "5.4.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/table/download/table-5.4.6.tgz", "integrity": "sha1-EpLRlQDOP4YFOwXw6Ofko7shB54=", "requires": {"ajv": "^6.10.2", "lodash": "^4.17.14", "slice-ansi": "^2.1.0", "string-width": "^3.0.0"}, "dependencies": {"ansi-regex": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-regex/download/ansi-regex-4.1.0.tgz", "integrity": "sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc="}, "string-width": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/string-width/download/string-width-3.1.0.tgz", "integrity": "sha1-InZ74htirxCBV0MG9prFG2IgOWE=", "requires": {"emoji-regex": "^7.0.1", "is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^5.1.0"}}, "strip-ansi": {"version": "5.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/strip-ansi/download/strip-ansi-5.2.0.tgz", "integrity": "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=", "requires": {"ansi-regex": "^4.1.0"}}}}, "text-table": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz", "integrity": "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ="}, "through": {"version": "2.3.8", "resolved": "https://registry.npmjs.org/through/-/through-2.3.8.tgz", "integrity": "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU="}, "tmp": {"version": "0.0.33", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tmp/download/tmp-0.0.33.tgz", "integrity": "sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=", "requires": {"os-tmpdir": "~1.0.2"}}, "tsconfig-paths": {"version": "3.14.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/tsconfig-paths/-/tsconfig-paths-3.14.1.tgz", "integrity": "sha1-ugc0WZ6Oo2yGJ5jpILzxYyd7E3o=", "requires": {"@types/json5": "^0.0.29", "json5": "^1.0.1", "minimist": "^1.2.6", "strip-bom": "^3.0.0"}, "dependencies": {"minimist": {"version": "1.2.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/minimist/-/minimist-1.2.6.tgz", "integrity": "sha1-hjelt1nqDW6YcCz7OpKDMjyTr0Q="}}}, "tslib": {"version": "1.14.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tslib/download/tslib-1.14.1.tgz", "integrity": "sha1-zy04vcNKE0vK8QkcQfZhni9nLQA="}, "type-check": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/type-check/-/type-check-0.3.2.tgz", "integrity": "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=", "requires": {"prelude-ls": "~1.1.2"}}, "unbox-primitive": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/unbox-primitive/-/unbox-primitive-1.0.2.tgz", "integrity": "sha1-KQMgIQV9Xmzb0IxRKcIm3/jtb54=", "requires": {"call-bind": "^1.0.2", "has-bigints": "^1.0.2", "has-symbols": "^1.0.3", "which-boxed-primitive": "^1.0.2"}, "dependencies": {"call-bind": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "has-symbols": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg="}}}, "uri-js": {"version": "4.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/uri-js/download/uri-js-4.4.0.tgz", "integrity": "sha1-qnFCYd55PoqCNHp7zJznTobyhgI=", "requires": {"punycode": "^2.1.0"}}, "which": {"version": "1.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/which/download/which-1.3.1.tgz", "integrity": "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=", "requires": {"isexe": "^2.0.0"}}, "which-boxed-primitive": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz", "integrity": "sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY=", "requires": {"is-bigint": "^1.0.1", "is-boolean-object": "^1.1.0", "is-number-object": "^1.0.4", "is-string": "^1.0.5", "is-symbol": "^1.0.3"}}, "word-wrap": {"version": "1.2.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/word-wrap/download/word-wrap-1.2.3.tgz", "integrity": "sha1-YQY29rH3A4kb00dxzLF/uTtHB5w="}, "wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="}, "write": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/write/download/write-1.0.3.tgz", "integrity": "sha1-CADhRSO5I6OH5BUSPIZWFqrg9cM=", "requires": {"mkdirp": "^0.5.1"}}}}
const originFetch = window.fetch;
let errorCallback = data => data;

const wrapFetch = (url, options) =>
  originFetch(url, options).then(async res => {
    const response = res.clone();
    try {
      const data = await response.json();
  
      if (response.status === 200 && !data.success && data.errorCode === '401') {
        errorCallback(data);
        return Promise.reject(new Error('401'));
      }
    } catch(err) {
      
    }

    return res;
  });

window.fetch = wrapFetch;

export function setFetch401Error(errorcb) {
  errorCallback = errorcb;
}
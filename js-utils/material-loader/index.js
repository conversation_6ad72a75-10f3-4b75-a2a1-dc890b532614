/** 加载整个组件包 */
function materialLoader(url) {
  const script = document.createElement('script');
  script.src = url;
  document.body.appendChild(script);

  return new Promise((resolve) => {
    script.onload = () => {
      resolve(window.$packageData)
    }
  })
}

/** 加载单个组件 */
export function componentLoader(url, id) {
  const script = document.createElement('script');
  script.src = url;
  document.body.appendChild(script);

  return new Promise((resolve) => {
    script.onload = () => {
      if (__webpack_modules__[id]) {
        const component = __webpack_require__(id); // id
        resolve(component.default)
      } else {
        resolve(null)
      }
    }
  })
}
// TODO: 暂时给一个前缀
const prefixUrl = 'https://freight.sit.sf-express.com/'

export default function loadJson(url) {
  return materialLoader(url).then(async res => {
    for (const com in res.data) {
      const c = res.data[com]
      // TODO: 使用组件名，构建使用时候的组件别名
      const comName = com.slice(0, 1).toUpperCase() + com.slice(1)
      const component = await componentLoader(`${prefixUrl}${c.url}/${com}@${c.version}.js`, `${com}@${c.version}`)

      window.M = window.M || {}
      window.M[comName] = component

      // TODO: 如果没有某个组件，则需要默认加载一个空组件，不让程序出错
    }
  })
}

<!doctype html>
<html lang="en">

<head>
  <title>Code coverage report for scripts/testMock.js</title>
  <meta charset="utf-8" />
  <link rel="stylesheet" href="../prettify.css" />
  <link rel="stylesheet" href="../base.css" />
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style type='text/css'>
    .coverage-summary .sorter {
      background-image: url(../sort-arrow-sprite.png);
    }
  </style>
</head>

<body>
  <div class='wrapper'>
    <div class='pad1'>
      <h1>
        <a href="../index.html">All files</a> / <a href="index.html">scripts</a> testMock.js
      </h1>
      <div class='clearfix'>
        <div class='fl pad1y space-right2'>
          <span class="strong">100% </span>
          <span class="quiet">Statements</span>
          <span class='fraction'>1/1</span>
        </div>
        <div class='fl pad1y space-right2'>
          <span class="strong">100% </span>
          <span class="quiet">Branches</span>
          <span class='fraction'>0/0</span>
        </div>
        <div class='fl pad1y space-right2'>
          <span class="strong">100% </span>
          <span class="quiet">Functions</span>
          <span class='fraction'>0/0</span>
        </div>
        <div class='fl pad1y space-right2'>
          <span class="strong">100% </span>
          <span class="quiet">Lines</span>
          <span class='fraction'>1/1</span>
        </div>
      </div>
      <p class="quiet">
        Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the
        previous block.
      </p>
    </div>
    <div class='status-line high'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><aid='L1'></a><a href='#L1'>1</a></td><td class="line-coverage quiet"><span class="cline-any cline-yes">1x</span></td><td class="text"><pre class="prettyprint lang-js">module.exports = {};</pre>
    </td>
    </tr>
    </table>
    </pre>
    <div class='push'></div><!-- for sticky footer -->
  </div><!-- /wrapper -->
  <div class='footer quiet pad2 space-top1 center small'>
    Code coverage
    generated by <a href="https://istanbul.js.org/" target="_blank">istanbul</a> at Sun Oct 13 2019 03:42:05 GMT+0000
    (Coordinated Universal Time)
  </div>
  </div>
  <script src="../prettify.js"></script>
  <script>
    window.onload = function () {
      if (typeof prettyPrint === 'function') {
        prettyPrint();
      }
    };
  </script>
  <script src="../sorter.js"></script>
  <script src="../block-navigation.js"></script>
</body>

</html>
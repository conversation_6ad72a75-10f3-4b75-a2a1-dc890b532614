<!doctype html>
<html lang="en">

<head>
  <title>Code coverage report for src/index.js</title>
  <meta charset="utf-8" />
  <link rel="stylesheet" href="../prettify.css" />
  <link rel="stylesheet" href="../base.css" />
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style type='text/css'>
    .coverage-summary .sorter {
      background-image: url(../sort-arrow-sprite.png);
    }
  </style>
</head>

<body>
  <div class='wrapper'>
    <div class='pad1'>
      <h1>
        <a href="../index.html">All files</a> / <a href="index.html">src</a> index.js
      </h1>
      <div class='clearfix'>
        <div class='fl pad1y space-right2'>
          <span class="strong">66.67% </span>
          <span class="quiet">Statements</span>
          <span class='fraction'>2/3</span>
        </div>
        <div class='fl pad1y space-right2'>
          <span class="strong">100% </span>
          <span class="quiet">Branches</span>
          <span class='fraction'>0/0</span>
        </div>
        <div class='fl pad1y space-right2'>
          <span class="strong">50% </span>
          <span class="quiet">Functions</span>
          <span class='fraction'>1/2</span>
        </div>
        <div class='fl pad1y space-right2'>
          <span class="strong">66.67% </span>
          <span class="quiet">Lines</span>
          <span class='fraction'>2/3</span>
        </div>
      </div>
      <p class="quiet">
        Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the
        previous block.
      </p>
    </div>
    <div class='status-line medium'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><aid='L1'></a><a href='#L1'>1</a>
<aid='L2'></a><a href='#L2'>2</a>
<aid='L3'></a><a href='#L3'>3</a>
<aid='L4'></a><a href='#L4'>4</a>
<aid='L5'></a><a href='#L5'>5</a>
<aid='L6'></a><a href='#L6'>6</a>
<aid='L7'></a><a href='#L7'>7</a>
<aid='L8'></a><a href='#L8'>8</a>
<aid='L9'></a><a href='#L9'>9</a>
<aid='L10'></a><a href='#L10'>10</a>
<aid='L11'></a><a href='#L11'>11</a>
<aid='L12'></a><a href='#L12'>12</a>
<aid='L13'></a><a href='#L13'>13</a>
<aid='L14'></a><a href='#L14'>14</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import "./index.css";
&nbsp;
class MyLibrary {
  constructor() {
    console.log("Library constructor loaded");
  }
&nbsp;
  myMethod = <span class="fstat-no" title="function not covered" >()</span> =&gt; {
<span class="cstat-no" title="statement not covered" >    console.log("Library method fired");</span>
  };
}
&nbsp;
export default MyLibrary;
&nbsp;</pre>
    </td>
    </tr>
    </table>
    </pre>
    <div class='push'></div><!-- for sticky footer -->
  </div><!-- /wrapper -->
  <div class='footer quiet pad2 space-top1 center small'>
    Code coverage
    generated by <a href="https://istanbul.js.org/" target="_blank">istanbul</a> at Sun Oct 13 2019 03:42:05 GMT+0000
    (Coordinated Universal Time)
  </div>
  </div>
  <script src="../prettify.js"></script>
  <script>
    window.onload = function () {
      if (typeof prettyPrint === 'function') {
        prettyPrint();
      }
    };
  </script>
  <script src="../sorter.js"></script>
  <script src="../block-navigation.js"></script>
</body>

</html>
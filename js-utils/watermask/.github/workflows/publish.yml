name: Publish

on:
  push:
    branches:
      - master

jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v1
      - uses: actions/setup-node@v1
        with:
          node-version: 12
      - run: npm install
      - run: npm run test -- --coverage --watchAll=false
        
      - name: Setup GIT
        run: |
          git config --local --list
          git checkout master
          git config user.email "$GH_EMAIL"
          git config user.name "<PERSON>"
        env:
            GH_EMAIL: ${{secrets.GH_EMAIL}}
          
      - name: Bump version
        run: |
          git reset --hard
          npm version patch
          npm run build
          git add . || true
          git commit -m "Build update" || true
          git push "https://$GITHUB_ACTOR:$<EMAIL>/$GITHUB_REPOSITORY"
        env:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
          
      - name: npm publish
        run: |
          npm config set //registry.npmjs.org/:_authToken=$NODE_AUTH_TOKEN
          npm run trypublish
        env:
            NODE_AUTH_TOKEN: ${{secrets.NODE_AUTH_TOKEN}}

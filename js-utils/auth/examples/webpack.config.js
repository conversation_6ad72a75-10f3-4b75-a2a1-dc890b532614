const path = require("path");
const CopyPlugin = require("copy-webpack-plugin");
const HtmlWebpackPlugin = require("html-webpack-plugin");

module.exports = {
	mode: "development",
	entry: {
		"main": path.resolve(__dirname, "./main.js")
	},
	output: {
		publicPath: "",
		path: path.resolve(__dirname, "dist"),
		filename: "[name]_[contenthash].js",
	},
	plugins: [
		new CopyPlugin({
			patterns: [
				// { from: path.resolve(__dirname, "index.html"), to: "index.html" },
				{ from: path.resolve(__dirname, "redirect.js"), to: "redirect.js" },
				{ from: path.resolve(__dirname, "global.css"), to: "global.css" },
			],
		}),
		new HtmlWebpackPlugin({ inject: true, filename: "index.html", template: "./examples/index.html" })
	],
	devServer: {
		port: 9000,
		proxy: {
			"/nuts/": {
				target: "https://freight.sit.sf-express.com/",
				pathRewrite: { "^/nuts/": "/sf-nuts/" },
				secure: false,
			},
			"/nuts/hb-move-open/": {
				target: "https://freight.sit.sf-express.com/",
				secure: false,
			},
			"/restApi/": {
				target: "https://freight.sit.sf-express.com/",
				pathRewrite: {},
				secure: false,
			}
		},
	}
};
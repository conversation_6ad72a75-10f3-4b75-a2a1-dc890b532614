import auth from '../dist/es/main.js';

function ajax(url, { method = 'POST', headers = {}, data = {} }) {
  const xhr = new XMLHttpRequest();
  xhr.open(method, url, true);
  xhr.setRequestHeader('Content-Type', 'application/json');
  for (let p in headers) {
    if (Object.prototype.hasOwnProperty.call(headers, p)) {
      xhr.setRequestHeader(p, headers[p]);
    }
  }
  xhr.send(JSON.stringify(data));
  return new Promise((resolve, reject) => {
    xhr.onreadystatechange = () => {
      if (xhr.readyState === 4 && xhr.status === 200) {
        resolve(JSON.parse(xhr.responseText));
      }
    };
  });
}

// 外部可以定义的额外环境
auth.addEnvs({
  // 城市配送
  CSPS: () => {
    // 当前环境的判断
    return window.navigator.userAgent.indexOf('App/csps') > -1;
  },
});

auth.watch('CSPS', async (data, context) => {
  const getAuthInfo = new Promise(resolve => {
    if (window.shortTransportApi) {
      window.onGetTokenSuccess = dataStr => {
        const res = { success: false, obj: null, errMsg: '' };
        try {
          res.obj = JSON.parse(dataStr);
          console.log('CSPS userInfo', res.obj);
          res.success = true;
          Object.assign(context, {
            ...res,
          });
        } catch (err) {
          console.error(err.message);
          res.success = false;
          res.errMsg = '获取鉴权信息失败';
          Object.assign(context, {
            ...res,
          });
        }
        resolve();
      };
      window.shortTransportApi.getToken();
    }
  });
  const authInfo = await getAuthInfo;
  if (authInfo.success) {
    const { obj } = authInfo;
    if (obj.cpToken) {
      const { token, userName } = obj.cpToken;
      // 城市配送这里的 token 是已经可以直接请求接口的了
      return ajax(
        '/nuts/hb-move-open/thirdForward/fop-fowd-core-special-line/short-direct-delivery-order/roll',
        {
          method: 'POST',
          headers: {
            Authorization: token,
            empId: userName,
          },
          data: { pageNum: 1, pageSize: 20, waybillNoOrTranSupplierName: '' },
        },
      ).then(data => {
        document.querySelector('#auth1').innerHTML = JSON.stringify(
          data,
          null,
          2,
        );
      });
    }
  }
});

auth.watch('SFXG', async (data, context) => {
  console.log('SFXG', data, context);
  if (context.success) {
    const { obj } = context;
    const { token, userName } = obj;
    // 这里的 token 是已经可以直接请求接口的了
    return ajax(
      'https://out-third-common.sit.sf-express.com:9443/fop-fowd-core-special-line/shortTransportTask/inSearchWarehousingTask',
      {
        method: 'POST',
        headers: {
          token,
          'hg-nickname': userName,
          xVersion: '1.6',
          serviceid: 'shorttransport',
        },
        data: {
          pageNum: 1,
          pageSize: 20,
          nextDeptCode: '755DL',
          inSearchItem: '',
          transportTaskStatus: [5, 6],
        },
      },
    ).then(data => {
      document.querySelector('#auth1').innerHTML = JSON.stringify(
        data,
        null,
        2,
      );
    });
  }
});

auth.watch('SFAPP', { appid: '202108272254221331' }, (data, context) => {
  // app 鉴权信息
  if (context.success) {
    return ajax('/nuts/hb-move-person/customer/auth/login', {
      data: {
        type: 3,
        code: context.obj.code,
      },
    }).then(data => {
      console.log('data', data);
      context.token = '1';
      document.querySelector('#auth1').innerHTML = JSON.stringify(
        data,
        null,
        2,
      );
    });
  }
});

auth.watch('SFWEAPP', (data, context) => {
  console.log('速运小程序了', context);
  return ajax('/nuts/hb-move-person/customer/auth/login', {
    data: {
      type: 5,
      encryptPhone: decodeURIComponent(context.obj.mobile),
      openid: decodeURIComponent(context.obj.openId),
      thirdPaySource: 'syToken',
      encryptOpenId: decodeURIComponent(context.obj.openId),
      channelToken: 'syToken',
      orderSourceInt: 5,
    },
  }).then(data => {
    console.log('data', data, document.querySelector('#auth1'));
    // auth1
    context.token = '1';
    document.querySelector('#auth1').innerHTML = JSON.stringify(data, null, 2);
  });
});

auth.watch('SFDG', (data, context) => {
  console.log('顺丰大哥', context);
  if (context.success) {
    const { obj } = context;
    return ajax(
      `/restApi/apis-auth/login/check_token?timestamp=${new Date().getTime()}`,
      {
        method: 'GET',
        headers: {
          token: obj.token,
          'X-Requested-With': 'com.sf.freight.ca',
        },
      },
    ).then(data => {
      document.querySelector('#auth1').innerHTML = JSON.stringify(
        data,
        null,
        2,
      );
    });
  }
});

auth.watch(
  'FSAPP',
  { baseUrl: 'https://freight.sit.sf-express.com/restApi' },
  (data, context) => {
    if (context.success) {
      const { obj } = context; // 这里已经获取到用户信息，在丰声app中可以直接使用
      document.querySelector('#auth1').innerHTML = JSON.stringify(obj, null, 2);
    }
  },
);

document.querySelector('#init-btn').addEventListener('click', function () {
  console.log('auth', auth);
  auth.init().then(res => {
    console.log('res', res);
    document.querySelector('#env').innerHTML = res.envName;
  });
});

document.querySelector('#auth-btn').addEventListener('click', function () {
  auth.start().then(({ data, err }) => {
    console.log('data', data);
    console.log('err', err);

    if (!err) {
      if (!data) {
        console.log('找不到环境');
      } else {
        console.log('鉴权成功');
      }
    }
  });
});

// document.querySelector("#reload-btn").addEventListener("click", function() {
// 	window.location.reload();
// });

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="./global.css">
  <title>鉴权demo</title>
</head>

<body>
  <h1>鉴权DEMO</h1>
  <h3>鉴权库的引入</h3>
  <pre><code>import auth from "@ky/auth"</code></pre>
  <h3>鉴权库的初始化</h3>
  <pre><code>auth.init()</code></pre>
  <!-- <div><button id="reload">刷新页面</button></div> -->
  <button id="init-btn">初始化</button>
  <div>当前环境为<span id="env"></span></div>

  <button id="auth-btn">鉴权</button>

  <div>当前鉴权信息为
    <pre><code id="auth1"></code></pre>
  </div>
</body>
<script>
  var count = 0;
  var timerId = null;
  var loaded = false;
  function loadVconsole() {
    var el = document.createElement('script');
    el.src = 'https://cdn.bootcss.com/vConsole/3.3.2/vconsole.min.js';
    el.onload = function () {
      var vConsole = new VConsole();
      vConsole.show();
      loaded = true;
      sessionStorage

    };
    document.head.appendChild(el);
  }

  // loadVconsole();
  document.addEventListener('click', function () {
    if (loaded) return false;
    if (timerId) clearTimeout(timerId);
    count += 1;
    console.log(count);
    if (count >= 10) {
      loadVconsole();
      count = 0;
    } else {
      timerId = setTimeout(function () {
        count = 0;
      }, 1000);
    }
  });
</script>
</html>
## 统一鉴权插件

当H5页面以第三方应用接入不同的渠道时，那么就需要接入对应渠道的登录鉴权。如果一个应用接入了多个渠道，那么代码中登录鉴权的逻辑就会变得繁琐，为了让开发者是聚焦获取登录token逻辑本身（不再关心获取token所需要的前置逻辑），则将一些渠道的登录鉴权进行标准化，集成到鉴权插件中。

## 快速开始

安装@ky/auth
```sh
npm install @ky/auth
```

引入文件
```js
import auth from '@ky/auth'
```

鉴权代码
```js
// 接入顺丰速运+ APP
auth.watch("SFAPP", (data, context) => {
    // context将会返回鉴权所需信息
    if (context.success) {
        ajax({
            code: context.obj.code
        }).then(res => {
            // 业务的token
        })
    }
});

auth.init()
auth.start()
```

当使用鉴权库的时候，必须先调用```auth.init```函数，```auth```详情参考api文档，当需要鉴权的时候，则执行```auth.start()```，start函数会判断当前H5所在的环境，然后执行对应注册的钩子里面的回调函数，比如：如果判断当前h5所在的环境在速运APP中，则只会调用```auth.watch('SFAPP', fn)```中的回调函数


## 接入多个 app 使用示例

目前已经支持的渠道
* 顺丰大哥（SFDG）
* 顺丰小哥（SFXG）
* 速运+小程序（SFWEAPP）
* 速运APP（SFAPP）

```js
// 接入顺丰速运+ APP
auth.hook("SFAPP", (data, context) => {
    // context将会返回鉴权所需信息
    if (context.success) {
        ajax({
            code: context.obj.code
        }).then(res => {
            // 业务的token
        })
    }
});

// 速运+小程序
auth.hook("SFWEAPP", (data, context) => {
    // context将会返回鉴权所需信息
    if (context.success) {
        ajax({
            mobile: context.obj.mobile,
            openId: context.obj.openId
        }).then(res => {
            // 业务的token
        })
    }
});

auth.init()
auth.start()
```


## API

### 鉴权实例
```@ky/auth```库引入进来的即为鉴权实例对象，后续所有的鉴权函数的的调用都是通过鉴权对象来调用

```js
import auth from '@ky/auth'
```
auth为鉴权实例，
### init
初始化函数，初始化该环境中的一些全局变量，并且判断环境为什么环境，加载对应环境的js代码，执行鉴权之前一定要手动调用```init```函数

类型：函数

参数：无

返回值：Promise

用例：

一般用法
```js
auth.init()
```

获取当前的环境名
```js
auth.init().then(({ envName }) => {
    // 当没有匹配到环境是，envName为''
    console.log('当前的环境名为', envName)
})
```

### start
开始执行对应环境的鉴权流程，最后触发对应环境```watch```函数中的回调，该函数在调用之前一定要调用```init```方法。

类型：函数

参数：无

返回值：Promise<{data, err}>

当err有值时，则说明鉴权过程出错，当data有值，并且err为空时，鉴权成功，data为全局数据和context的集合，当err和data都为空时，则表示当前并为匹配到任何环境。

用例：

一般用法
```js
auth.start()
```

```js
auth.start().then(({ data, err }) => {
    if (!err) {
        if (data) {
            // 鉴权成功
        } else {
            // 当前没有匹配到任何环境
        }
    }
})
```

### addEnvs
添加额外环境（非标准环境），如果项目中存在需要校验的环境未标准化，那么可以使用```addEnvs```函数

类型：函数

参数：Object

返回值：void

用例：
```js
auth.addEnvs({
  customEnv: () => {
    if (/* 对该环境的判断 */) {
        return true
    }
  }
});
```

比如
```js
auth.addEnvs({
  xxx: () => {
    if (/xxx/.test(navigator.userAgent)) {
        return true
    }
  }
});
// 当匹配到当前浏览器中的userAgent有xxx字符串时，则会匹配该环境
```

该方法也需要结合watch方法使用，当匹配到对应的环境是，也需要添加对应环境的监听
```js
auth.watch('xxx', (data, context) => {
    // 进行对应环境的自定义鉴权处理
})
```

### watch
监听对应环境的鉴权流程，当对应环境获取鉴权对象成功时，能够在回调函数中获取对应的值

类型：函数

参数：
    
envName: 环境名称，比如SFAPP，SFWEAPP

fn: 回调函数

回调函数参数：

data: 全局的变量，比如浏览器变量，URL参数等

context: 鉴权所需要的数据

用例：
```js
auth.watch(envName, (data, context) => {
    // ...
})
```

### getData

类型：函数

参数：无

返回值：Object  返回的是这个鉴权对象的context，全局对象，URL参数的集合

用例：
```js
// 可以用于拿到请求所需要的鉴权参数
auth.getData()
```
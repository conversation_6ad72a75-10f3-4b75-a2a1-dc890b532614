{"name": "@ky/auth", "version": "1.0.34", "lockfileVersion": 1, "requires": true, "dependencies": {"@ampproject/remapping": {"version": "2.2.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@ampproject/remapping/-/remapping-2.2.1.tgz", "integrity": "sha1-mejhGFESi4cCzVfDNoTx0PJgtjA=", "dev": true, "requires": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}}, "@babel/code-frame": {"version": "7.18.6", "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.18.6.tgz", "integrity": "sha512-TDCmlK5eOvH+eH7cdAFlNXeVJqWIQ7gW9tY1GJIpUtFb6CmjVyq2VM3u71bOyR8CRihcCgMUYoDNyLXao3+70Q==", "dev": true, "requires": {"@babel/highlight": "^7.18.6"}}, "@babel/compat-data": {"version": "7.21.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/compat-data/-/compat-data-7.21.4.tgz", "integrity": "sha1-RX/+ZHxIDf9Zwr4JL8Os9xGVyH8=", "dev": true}, "@babel/core": {"version": "7.21.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/core/-/core-7.21.4.tgz", "integrity": "sha1-xtxzJCUHuOKif9E6nBgU+fo0plk=", "dev": true, "requires": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.21.4", "@babel/generator": "^7.21.4", "@babel/helper-compilation-targets": "^7.21.4", "@babel/helper-module-transforms": "^7.21.2", "@babel/helpers": "^7.21.0", "@babel/parser": "^7.21.4", "@babel/template": "^7.20.7", "@babel/traverse": "^7.21.4", "@babel/types": "^7.21.4", "convert-source-map": "^1.7.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.2", "semver": "^6.3.0"}, "dependencies": {"@babel/code-frame": {"version": "7.21.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/code-frame/-/code-frame-7.21.4.tgz", "integrity": "sha1-0PqeRBOsqB8rI7lEJ5e9oYJu2zk=", "dev": true, "requires": {"@babel/highlight": "^7.18.6"}}, "json5": {"version": "2.2.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/json5/-/json5-2.2.3.tgz", "integrity": "sha1-eM1vGhm9wStz21rQxh79ZsHikoM=", "dev": true}, "semver": {"version": "6.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "dev": true}}}, "@babel/generator": {"version": "7.21.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/generator/-/generator-7.21.4.tgz", "integrity": "sha1-ZKlLdEiYn0IfkZ1SOe9VOze7Jrw=", "dev": true, "requires": {"@babel/types": "^7.21.4", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17", "jsesc": "^2.5.1"}, "dependencies": {"jsesc": {"version": "2.5.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/jsesc/-/jsesc-2.5.2.tgz", "integrity": "sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=", "dev": true}}}, "@babel/helper-annotate-as-pure": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.18.6.tgz", "integrity": "sha1-6qSfb4DVoz+aXdInbm1uRRvgprs=", "dev": true, "requires": {"@babel/types": "^7.18.6"}}, "@babel/helper-builder-binary-assignment-operator-visitor": {"version": "7.18.9", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.18.9.tgz", "integrity": "sha1-rNTt/XpWbR1R6pdd/zj9UpBpgbs=", "dev": true, "requires": {"@babel/helper-explode-assignable-expression": "^7.18.6", "@babel/types": "^7.18.9"}}, "@babel/helper-compilation-targets": {"version": "7.21.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-compilation-targets/-/helper-compilation-targets-7.21.4.tgz", "integrity": "sha1-dwzRzgiJCXzqy5lBjuaTTvBXJlY=", "dev": true, "requires": {"@babel/compat-data": "^7.21.4", "@babel/helper-validator-option": "^7.21.0", "browserslist": "^4.21.3", "lru-cache": "^5.1.1", "semver": "^6.3.0"}, "dependencies": {"lru-cache": {"version": "5.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=", "dev": true, "requires": {"yallist": "^3.0.2"}}, "semver": {"version": "6.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "dev": true}, "yallist": {"version": "3.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/yallist/-/yallist-3.1.1.tgz", "integrity": "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=", "dev": true}}}, "@babel/helper-create-class-features-plugin": {"version": "7.21.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.21.4.tgz", "integrity": "sha1-OgFxY9w8K6feuaeVCEmpWG6iTBg=", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-function-name": "^7.21.0", "@babel/helper-member-expression-to-functions": "^7.21.0", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-replace-supers": "^7.20.7", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0", "@babel/helper-split-export-declaration": "^7.18.6"}}, "@babel/helper-create-regexp-features-plugin": {"version": "7.21.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.21.4.tgz", "integrity": "sha1-QEEairE0JYrSzzo9mH7GqgcjzuU=", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.18.6", "regexpu-core": "^5.3.1"}}, "@babel/helper-define-polyfill-provider": {"version": "0.3.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.3.3.tgz", "integrity": "sha1-hhLlW+XVHwzR82tKWoOSTomIS3o=", "dev": true, "requires": {"@babel/helper-compilation-targets": "^7.17.7", "@babel/helper-plugin-utils": "^7.16.7", "debug": "^4.1.1", "lodash.debounce": "^4.0.8", "resolve": "^1.14.2", "semver": "^6.1.2"}, "dependencies": {"semver": {"version": "6.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "dev": true}}}, "@babel/helper-environment-visitor": {"version": "7.18.9", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-environment-visitor/-/helper-environment-visitor-7.18.9.tgz", "integrity": "sha1-DAzumzXSyhkEeHVoZbs1KEIvUb4=", "dev": true}, "@babel/helper-explode-assignable-expression": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.18.6.tgz", "integrity": "sha1-QfgijvCm8aA2uN/f7HzpT5prwJY=", "dev": true, "requires": {"@babel/types": "^7.18.6"}}, "@babel/helper-function-name": {"version": "7.21.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-function-name/-/helper-function-name-7.21.0.tgz", "integrity": "sha1-1VKCmxDqnxIJaTBAI80GRfoAsbQ=", "dev": true, "requires": {"@babel/template": "^7.20.7", "@babel/types": "^7.21.0"}}, "@babel/helper-hoist-variables": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-hoist-variables/-/helper-hoist-variables-7.18.6.tgz", "integrity": "sha1-1NLI+0uuqlxouZzIJFxWVU+SZng=", "dev": true, "requires": {"@babel/types": "^7.18.6"}}, "@babel/helper-member-expression-to-functions": {"version": "7.21.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.21.0.tgz", "integrity": "sha1-MZxqlAQxoTOJcUhRWHfS8yacO6U=", "dev": true, "requires": {"@babel/types": "^7.21.0"}}, "@babel/helper-module-imports": {"version": "7.21.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-module-imports/-/helper-module-imports-7.21.4.tgz", "integrity": "sha1-rIiy92CTY3SJ5xipDOxs+KmwKa8=", "dev": true, "requires": {"@babel/types": "^7.21.4"}}, "@babel/helper-module-transforms": {"version": "7.21.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-module-transforms/-/helper-module-transforms-7.21.2.tgz", "integrity": "sha1-Fgyq+kl4rIwArGZjbLD6N7Ak4tI=", "dev": true, "requires": {"@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-simple-access": "^7.20.2", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-validator-identifier": "^7.19.1", "@babel/template": "^7.20.7", "@babel/traverse": "^7.21.2", "@babel/types": "^7.21.2"}}, "@babel/helper-optimise-call-expression": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.18.6.tgz", "integrity": "sha1-k2mqlD7n2kftqyy06Dis8J0pD/4=", "dev": true, "requires": {"@babel/types": "^7.18.6"}}, "@babel/helper-plugin-utils": {"version": "7.20.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "integrity": "sha1-0bkAB1KxjQh3z/haXDds5cMSFik=", "dev": true}, "@babel/helper-remap-async-to-generator": {"version": "7.18.9", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.18.9.tgz", "integrity": "sha1-mXRYoOM1cIDlTh157DR/iozShRk=", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-wrap-function": "^7.18.9", "@babel/types": "^7.18.9"}}, "@babel/helper-replace-supers": {"version": "7.20.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-replace-supers/-/helper-replace-supers-7.20.7.tgz", "integrity": "sha1-JD7NJyTSBxUyssitLw+fCDvK4zE=", "dev": true, "requires": {"@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-member-expression-to-functions": "^7.20.7", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/template": "^7.20.7", "@babel/traverse": "^7.20.7", "@babel/types": "^7.20.7"}}, "@babel/helper-simple-access": {"version": "7.20.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-simple-access/-/helper-simple-access-7.20.2.tgz", "integrity": "sha1-CrRSaH/gws+x4rngAV3gf8LWLdk=", "dev": true, "requires": {"@babel/types": "^7.20.2"}}, "@babel/helper-skip-transparent-expression-wrappers": {"version": "7.20.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.20.0.tgz", "integrity": "sha1-++TFL2BRjKuBQNdxAfDmOoojBoQ=", "dev": true, "requires": {"@babel/types": "^7.20.0"}}, "@babel/helper-split-export-declaration": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.18.6.tgz", "integrity": "sha1-c2eUm8dbIMbVpdSpe7ooJK6O8HU=", "dev": true, "requires": {"@babel/types": "^7.18.6"}}, "@babel/helper-string-parser": {"version": "7.19.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-string-parser/-/helper-string-parser-7.19.4.tgz", "integrity": "sha1-ONOstlS0cBqbd/sGFalvd1w6nmM=", "dev": true}, "@babel/helper-validator-identifier": {"version": "7.19.1", "resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz", "integrity": "sha512-awrNfaMtnHUr653GgGEs++LlAvW6w+DcPrOliSMXWCKo597CwL5Acf/wWdNkf/tfEQE3mjkeD1YOVZOUV/od1w==", "dev": true}, "@babel/helper-validator-option": {"version": "7.21.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-validator-option/-/helper-validator-option-7.21.0.tgz", "integrity": "sha1-giTH4TrOS6/cQATaLPBk70JnMYA=", "dev": true}, "@babel/helper-wrap-function": {"version": "7.20.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helper-wrap-function/-/helper-wrap-function-7.20.5.tgz", "integrity": "sha1-deLYTUmaCrOzHDO8/lnWuKRfYuM=", "dev": true, "requires": {"@babel/helper-function-name": "^7.19.0", "@babel/template": "^7.18.10", "@babel/traverse": "^7.20.5", "@babel/types": "^7.20.5"}}, "@babel/helpers": {"version": "7.21.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/helpers/-/helpers-7.21.0.tgz", "integrity": "sha1-ndGE+1WZhiA3kXzcnuy4RXfcTn4=", "dev": true, "requires": {"@babel/template": "^7.20.7", "@babel/traverse": "^7.21.0", "@babel/types": "^7.21.0"}}, "@babel/highlight": {"version": "7.18.6", "resolved": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.18.6.tgz", "integrity": "sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g==", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.18.6", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}, "dependencies": {"ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "color-convert": {"version": "1.9.3", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "dev": true, "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "integrity": "sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==", "dev": true}, "escape-string-regexp": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==", "dev": true}, "has-flag": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==", "dev": true}, "supports-color": {"version": "5.5.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "@babel/parser": {"version": "7.21.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/parser/-/parser-7.21.4.tgz", "integrity": "sha1-lAA/38Ugu+KHXUrlV7Q9222IDxc=", "dev": true}, "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.18.6.tgz", "integrity": "sha1-2luPmlgKzfvlNJTbpF6jifsJpNI=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": {"version": "7.20.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.20.7.tgz", "integrity": "sha1-2chViSWFOaIqkBAzhTEBphmNTvE=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0", "@babel/plugin-proposal-optional-chaining": "^7.20.7"}}, "@babel/plugin-proposal-async-generator-functions": {"version": "7.20.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.20.7.tgz", "integrity": "sha1-v7cnbS1XPLZ7o3mYSiM04mK6UyY=", "dev": true, "requires": {"@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-remap-async-to-generator": "^7.18.9", "@babel/plugin-syntax-async-generators": "^7.8.4"}}, "@babel/plugin-proposal-class-properties": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz", "integrity": "sha1-sRD1l0GJX37CGm//aW7EYmXERqM=", "dev": true, "requires": {"@babel/helper-create-class-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-proposal-class-static-block": {"version": "7.21.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-class-static-block/-/plugin-proposal-class-static-block-7.21.0.tgz", "integrity": "sha1-d73Wb7e2BfOmEwLSJL36z1VHl30=", "dev": true, "requires": {"@babel/helper-create-class-features-plugin": "^7.21.0", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-class-static-block": "^7.14.5"}}, "@babel/plugin-proposal-dynamic-import": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-dynamic-import/-/plugin-proposal-dynamic-import-7.18.6.tgz", "integrity": "sha1-crz41Ah5n1R9dZKYw8J8fn+qTZQ=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}}, "@babel/plugin-proposal-export-namespace-from": {"version": "7.18.9", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-export-namespace-from/-/plugin-proposal-export-namespace-from-7.18.9.tgz", "integrity": "sha1-X3MTqzSM2xnVkBRfkkdUDpR2EgM=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.9", "@babel/plugin-syntax-export-namespace-from": "^7.8.3"}}, "@babel/plugin-proposal-json-strings": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.18.6.tgz", "integrity": "sha1-foeIwYEcOTr/digX59vx69DAXws=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-json-strings": "^7.8.3"}}, "@babel/plugin-proposal-logical-assignment-operators": {"version": "7.20.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-logical-assignment-operators/-/plugin-proposal-logical-assignment-operators-7.20.7.tgz", "integrity": "sha1-37yqj3tNN7Uei/tG2Upa6iu4nYM=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}}, "@babel/plugin-proposal-nullish-coalescing-operator": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz", "integrity": "sha1-/dlAqZp0Dld9bHU6tvu0P9uUZ+E=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}}, "@babel/plugin-proposal-numeric-separator": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.18.6.tgz", "integrity": "sha1-iZsU+6/ofwU9LF/wWzYCnGLhPHU=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}}, "@babel/plugin-proposal-object-rest-spread": {"version": "7.20.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.20.7.tgz", "integrity": "sha1-qmYpQO9CV3nHVTSlxB6dk27cOQo=", "dev": true, "requires": {"@babel/compat-data": "^7.20.5", "@babel/helper-compilation-targets": "^7.20.7", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-transform-parameters": "^7.20.7"}}, "@babel/plugin-proposal-optional-catch-binding": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.18.6.tgz", "integrity": "sha1-+UANDmo+qTup73CwnnLdbaY4oss=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3"}}, "@babel/plugin-proposal-optional-chaining": {"version": "7.21.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.21.0.tgz", "integrity": "sha1-iG9ciXjet9MPZ4suJDRrKHI00+o=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0", "@babel/plugin-syntax-optional-chaining": "^7.8.3"}}, "@babel/plugin-proposal-private-methods": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-private-methods/-/plugin-proposal-private-methods-7.18.6.tgz", "integrity": "sha1-UgnefSE0V1SKmENvoogvUvS+a+o=", "dev": true, "requires": {"@babel/helper-create-class-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-proposal-private-property-in-object": {"version": "7.21.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.0.tgz", "integrity": "sha1-GUlr2Yg92Dwjx9f8RdzZrQLfodw=", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.21.0", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}}, "@babel/plugin-proposal-unicode-property-regex": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.18.6.tgz", "integrity": "sha1-r2E9LNXmQ2Q7Zc3tZCB7Fchct44=", "dev": true, "requires": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-syntax-async-generators": {"version": "7.8.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz", "integrity": "sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-class-properties": {"version": "7.12.13", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz", "integrity": "sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.12.13"}}, "@babel/plugin-syntax-class-static-block": {"version": "7.14.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz", "integrity": "sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-syntax-dynamic-import": {"version": "7.8.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz", "integrity": "sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-export-namespace-from": {"version": "7.8.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz", "integrity": "sha1-AolkqbqA28CUyRXEh618TnpmRlo=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.3"}}, "@babel/plugin-syntax-import-assertions": {"version": "7.20.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.20.0.tgz", "integrity": "sha1-u1Dg1L6glXI1OQZBIJOU6HvbnMQ=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.19.0"}}, "@babel/plugin-syntax-json-strings": {"version": "7.8.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz", "integrity": "sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-logical-assignment-operators": {"version": "7.10.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "integrity": "sha1-ypHvRjA1MESLkGZSusLp/plB9pk=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "integrity": "sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz", "integrity": "sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz", "integrity": "sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz", "integrity": "sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz", "integrity": "sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-private-property-in-object": {"version": "7.14.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz", "integrity": "sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-syntax-top-level-await": {"version": "7.14.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz", "integrity": "sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-arrow-functions": {"version": "7.20.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.20.7.tgz", "integrity": "sha1-vqMysOiy2rPa/lWhY9gidTGrBVE=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2"}}, "@babel/plugin-transform-async-to-generator": {"version": "7.20.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.20.7.tgz", "integrity": "sha1-3+4YYjyMsx3reWqjyoTdqc6pQ1Q=", "dev": true, "requires": {"@babel/helper-module-imports": "^7.18.6", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-remap-async-to-generator": "^7.18.9"}}, "@babel/plugin-transform-block-scoped-functions": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.18.6.tgz", "integrity": "sha1-kYe/S6MCY1udcNmGrXDwOHJiFqg=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-block-scoping": {"version": "7.21.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.21.0.tgz", "integrity": "sha1-5ze5EDflGG7ha3bnrgkzWKVjTwI=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2"}}, "@babel/plugin-transform-classes": {"version": "7.21.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-classes/-/plugin-transform-classes-7.21.0.tgz", "integrity": "sha1-9GnQsHpMWn27Ia+tnifle0cDFmU=", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-compilation-targets": "^7.20.7", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-function-name": "^7.21.0", "@babel/helper-optimise-call-expression": "^7.18.6", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-replace-supers": "^7.20.7", "@babel/helper-split-export-declaration": "^7.18.6", "globals": "^11.1.0"}, "dependencies": {"globals": {"version": "11.12.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/globals/-/globals-11.12.0.tgz", "integrity": "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=", "dev": true}}}, "@babel/plugin-transform-computed-properties": {"version": "7.20.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.20.7.tgz", "integrity": "sha1-cEzC/RVdHJllUduCdtVbnUbk0Ko=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/template": "^7.20.7"}}, "@babel/plugin-transform-destructuring": {"version": "7.21.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.21.3.tgz", "integrity": "sha1-c7RtD9Ec1u9X3qijgbEhX0lZ1AE=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2"}}, "@babel/plugin-transform-dotall-regex": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.18.6.tgz", "integrity": "sha1-soaz56rmx7hh5FvtCi+v1rGk/vg=", "dev": true, "requires": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-duplicate-keys": {"version": "7.18.9", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.18.9.tgz", "integrity": "sha1-aH8V7jza1thRkesqNyxFKOqgrg4=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.9"}}, "@babel/plugin-transform-exponentiation-operator": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.18.6.tgz", "integrity": "sha1-QhxwX0UhiIxl6R/dGvlRv+/U2s0=", "dev": true, "requires": {"@babel/helper-builder-binary-assignment-operator-visitor": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-for-of": {"version": "7.21.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.21.0.tgz", "integrity": "sha1-lkEIyZiN4aYLS+I1Sn1+JF826G4=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2"}}, "@babel/plugin-transform-function-name": {"version": "7.18.9", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.18.9.tgz", "integrity": "sha1-zDVPgjTmKWiUbGGkbWNlRA/HZOA=", "dev": true, "requires": {"@babel/helper-compilation-targets": "^7.18.9", "@babel/helper-function-name": "^7.18.9", "@babel/helper-plugin-utils": "^7.18.9"}}, "@babel/plugin-transform-literals": {"version": "7.18.9", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-literals/-/plugin-transform-literals-7.18.9.tgz", "integrity": "sha1-cnlv2++A5W+6PGppnVTw3lV0RLw=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.9"}}, "@babel/plugin-transform-member-expression-literals": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.18.6.tgz", "integrity": "sha1-rJ/cGhGGIKxJt+el0twXehv+6I4=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-modules-amd": {"version": "7.20.11", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.20.11.tgz", "integrity": "sha1-PazMqOTMMJ8Dw6DEtB3Esm9VIUo=", "dev": true, "requires": {"@babel/helper-module-transforms": "^7.20.11", "@babel/helper-plugin-utils": "^7.20.2"}}, "@babel/plugin-transform-modules-commonjs": {"version": "7.21.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.21.2.tgz", "integrity": "sha1-b/UHDnHjGS7yt+OYIKBvt44wWOc=", "dev": true, "requires": {"@babel/helper-module-transforms": "^7.21.2", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-simple-access": "^7.20.2"}}, "@babel/plugin-transform-modules-systemjs": {"version": "7.20.11", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.20.11.tgz", "integrity": "sha1-Rn7Gu6a2pQY07qYcnCMmVNikaW4=", "dev": true, "requires": {"@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-module-transforms": "^7.20.11", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-validator-identifier": "^7.19.1"}}, "@babel/plugin-transform-modules-umd": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.18.6.tgz", "integrity": "sha1-gdODLWA0t1tU5ighuljyjtCqtLk=", "dev": true, "requires": {"@babel/helper-module-transforms": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-named-capturing-groups-regex": {"version": "7.20.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.20.5.tgz", "integrity": "sha1-YmKY3WLqUdRSw75YsoXSMZW6aag=", "dev": true, "requires": {"@babel/helper-create-regexp-features-plugin": "^7.20.5", "@babel/helper-plugin-utils": "^7.20.2"}}, "@babel/plugin-transform-new-target": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.18.6.tgz", "integrity": "sha1-0Sjzdq4gBHfzfE3fzHIqihsyRqg=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-object-super": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.18.6.tgz", "integrity": "sha1-+zxszdFZObb/eTmUS1GXHdw1kSw=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/helper-replace-supers": "^7.18.6"}}, "@babel/plugin-transform-parameters": {"version": "7.21.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.21.3.tgz", "integrity": "sha1-GPxOeXz21tlyy4xBHb6KgJ+hV9s=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2"}}, "@babel/plugin-transform-property-literals": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.18.6.tgz", "integrity": "sha1-4iSYkDpINEjpTgMum7ucXMv8k6M=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-regenerator": {"version": "7.20.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.20.5.tgz", "integrity": "sha1-V82liMf/t/T4SDzIO9zqAqkH8E0=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2", "regenerator-transform": "^0.15.1"}}, "@babel/plugin-transform-reserved-words": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.18.6.tgz", "integrity": "sha1-savY6/jtql9/5ru40hM9I7am92o=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-runtime": {"version": "7.21.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.21.4.tgz", "integrity": "sha1-Lh2iHKWXp9AfyWtpmyHY0gIxkao=", "dev": true, "requires": {"@babel/helper-module-imports": "^7.21.4", "@babel/helper-plugin-utils": "^7.20.2", "babel-plugin-polyfill-corejs2": "^0.3.3", "babel-plugin-polyfill-corejs3": "^0.6.0", "babel-plugin-polyfill-regenerator": "^0.4.1", "semver": "^6.3.0"}, "dependencies": {"semver": {"version": "6.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "dev": true}}}, "@babel/plugin-transform-shorthand-properties": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.18.6.tgz", "integrity": "sha1-bW33mD1nsZUom+JJCePxKo9mTck=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-spread": {"version": "7.20.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-spread/-/plugin-transform-spread-7.20.7.tgz", "integrity": "sha1-wtg+C5nTv4PgexGZXuJL98oJQB4=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0"}}, "@babel/plugin-transform-sticky-regex": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.18.6.tgz", "integrity": "sha1-xnBusrFSQCjjF3IDOVg60PRErcw=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/plugin-transform-template-literals": {"version": "7.18.9", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.18.9.tgz", "integrity": "sha1-BOxvEKzaqBhGaJ1j+uEX3ZwkOl4=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.9"}}, "@babel/plugin-transform-typeof-symbol": {"version": "7.18.9", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.18.9.tgz", "integrity": "sha1-yM6mgmPkWt3NavyQkUKfgJJXYsA=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.9"}}, "@babel/plugin-transform-unicode-escapes": {"version": "7.18.10", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.18.10.tgz", "integrity": "sha1-Hs+w7ag9CbvLd8CZcMLdVYMqokY=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.18.9"}}, "@babel/plugin-transform-unicode-regex": {"version": "7.18.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.18.6.tgz", "integrity": "sha1-GUMXIl2MIBu64QM2T/6eLOo2zco=", "dev": true, "requires": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}}, "@babel/preset-env": {"version": "7.21.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/preset-env/-/preset-env-7.21.4.tgz", "integrity": "sha1-qVJILmNKjdgnGj/lRZoW6xBznFg=", "dev": true, "requires": {"@babel/compat-data": "^7.21.4", "@babel/helper-compilation-targets": "^7.21.4", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-validator-option": "^7.21.0", "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": "^7.18.6", "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "^7.20.7", "@babel/plugin-proposal-async-generator-functions": "^7.20.7", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-class-static-block": "^7.21.0", "@babel/plugin-proposal-dynamic-import": "^7.18.6", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-json-strings": "^7.18.6", "@babel/plugin-proposal-logical-assignment-operators": "^7.20.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-numeric-separator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-proposal-optional-catch-binding": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.21.0", "@babel/plugin-proposal-unicode-property-regex": "^7.18.6", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-export-namespace-from": "^7.8.3", "@babel/plugin-syntax-import-assertions": "^7.20.0", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-top-level-await": "^7.14.5", "@babel/plugin-transform-arrow-functions": "^7.20.7", "@babel/plugin-transform-async-to-generator": "^7.20.7", "@babel/plugin-transform-block-scoped-functions": "^7.18.6", "@babel/plugin-transform-block-scoping": "^7.21.0", "@babel/plugin-transform-classes": "^7.21.0", "@babel/plugin-transform-computed-properties": "^7.20.7", "@babel/plugin-transform-destructuring": "^7.21.3", "@babel/plugin-transform-dotall-regex": "^7.18.6", "@babel/plugin-transform-duplicate-keys": "^7.18.9", "@babel/plugin-transform-exponentiation-operator": "^7.18.6", "@babel/plugin-transform-for-of": "^7.21.0", "@babel/plugin-transform-function-name": "^7.18.9", "@babel/plugin-transform-literals": "^7.18.9", "@babel/plugin-transform-member-expression-literals": "^7.18.6", "@babel/plugin-transform-modules-amd": "^7.20.11", "@babel/plugin-transform-modules-commonjs": "^7.21.2", "@babel/plugin-transform-modules-systemjs": "^7.20.11", "@babel/plugin-transform-modules-umd": "^7.18.6", "@babel/plugin-transform-named-capturing-groups-regex": "^7.20.5", "@babel/plugin-transform-new-target": "^7.18.6", "@babel/plugin-transform-object-super": "^7.18.6", "@babel/plugin-transform-parameters": "^7.21.3", "@babel/plugin-transform-property-literals": "^7.18.6", "@babel/plugin-transform-regenerator": "^7.20.5", "@babel/plugin-transform-reserved-words": "^7.18.6", "@babel/plugin-transform-shorthand-properties": "^7.18.6", "@babel/plugin-transform-spread": "^7.20.7", "@babel/plugin-transform-sticky-regex": "^7.18.6", "@babel/plugin-transform-template-literals": "^7.18.9", "@babel/plugin-transform-typeof-symbol": "^7.18.9", "@babel/plugin-transform-unicode-escapes": "^7.18.10", "@babel/plugin-transform-unicode-regex": "^7.18.6", "@babel/preset-modules": "^0.1.5", "@babel/types": "^7.21.4", "babel-plugin-polyfill-corejs2": "^0.3.3", "babel-plugin-polyfill-corejs3": "^0.6.0", "babel-plugin-polyfill-regenerator": "^0.4.1", "core-js-compat": "^3.25.1", "semver": "^6.3.0"}, "dependencies": {"semver": {"version": "6.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "dev": true}}}, "@babel/preset-modules": {"version": "0.1.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/preset-modules/-/preset-modules-0.1.5.tgz", "integrity": "sha1-75Odbn8miCfhhBY43G/5VRXhFdk=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4", "@babel/plugin-transform-dotall-regex": "^7.4.4", "@babel/types": "^7.4.4", "esutils": "^2.0.2"}}, "@babel/regjsgen": {"version": "0.8.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/regjsgen/-/regjsgen-0.8.0.tgz", "integrity": "sha1-8LppsHXh8F+yglt/rZkeetuxgxA=", "dev": true}, "@babel/runtime": {"version": "7.21.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/runtime/-/runtime-7.21.0.tgz", "integrity": "sha1-W1XJ05Tl/PMEkJqLAMB9whe1ZnM=", "dev": true, "requires": {"regenerator-runtime": "^0.13.11"}, "dependencies": {"regenerator-runtime": {"version": "0.13.11", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz", "integrity": "sha1-9tyj587sIFkNB62nhWNqkM3KF/k=", "dev": true}}}, "@babel/template": {"version": "7.20.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/template/-/template-7.20.7.tgz", "integrity": "sha1-oVCQwoOag7AqqZbAtJlABYQf1ag=", "dev": true, "requires": {"@babel/code-frame": "^7.18.6", "@babel/parser": "^7.20.7", "@babel/types": "^7.20.7"}}, "@babel/traverse": {"version": "7.21.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/traverse/-/traverse-7.21.4.tgz", "integrity": "sha1-qDasp7EWY06Xpu2Zl2I2sygsnTY=", "dev": true, "requires": {"@babel/code-frame": "^7.21.4", "@babel/generator": "^7.21.4", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-function-name": "^7.21.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/parser": "^7.21.4", "@babel/types": "^7.21.4", "debug": "^4.1.0", "globals": "^11.1.0"}, "dependencies": {"@babel/code-frame": {"version": "7.21.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/code-frame/-/code-frame-7.21.4.tgz", "integrity": "sha1-0PqeRBOsqB8rI7lEJ5e9oYJu2zk=", "dev": true, "requires": {"@babel/highlight": "^7.18.6"}}, "globals": {"version": "11.12.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/globals/-/globals-11.12.0.tgz", "integrity": "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=", "dev": true}}}, "@babel/types": {"version": "7.21.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@babel/types/-/types-7.21.4.tgz", "integrity": "sha1-LV1rt5CGmbO0FkCf/TtdqiWwMNQ=", "dev": true, "requires": {"@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1", "to-fast-properties": "^2.0.0"}}, "@discoveryjs/json-ext": {"version": "0.5.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@discoveryjs/json-ext/-/json-ext-0.5.7.tgz", "integrity": "sha1-HVcr+74Ut3BOC6Dzm3SBW4SHDXA=", "dev": true}, "@eslint/eslintrc": {"version": "1.3.3", "resolved": "https://registry.npmmirror.com/@eslint/eslintrc/-/eslintrc-1.3.3.tgz", "integrity": "sha512-uj3pT6Mg+3t39fvLrj8iuCIJ38zKO9FpGtJ4BBJebJhEwjoT+KLVNCcHT5QC9NGRIEi7fZ0ZR8YRb884auB4Lg==", "dev": true, "requires": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.4.0", "globals": "^13.15.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "dependencies": {"brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "minimatch": {"version": "3.1.2", "resolved": "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dev": true, "requires": {"brace-expansion": "^1.1.7"}}}}, "@humanwhocodes/config-array": {"version": "0.11.7", "resolved": "https://registry.npmmirror.com/@humanwhocodes/config-array/-/config-array-0.11.7.tgz", "integrity": "sha512-kBbPWzN8oVMLb0hOUYXhmxggL/1cJE6ydvjDIGi9EnAGUyA7cLVKQg+d/Dsm+KZwx2czGHrCmMVLiyg8s5JPKw==", "dev": true, "requires": {"@humanwhocodes/object-schema": "^1.2.1", "debug": "^4.1.1", "minimatch": "^3.0.5"}, "dependencies": {"brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "minimatch": {"version": "3.1.2", "resolved": "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dev": true, "requires": {"brace-expansion": "^1.1.7"}}}}, "@humanwhocodes/module-importer": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz", "integrity": "sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==", "dev": true}, "@humanwhocodes/object-schema": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz", "integrity": "sha512-ZnQMnLV4e7hDlUvw8H+U8ASL02SS2Gn6+9Ac3wGGLIe7+je2AeAOxPY+izIPJDfFDb7eDjev0Us8MO1iFRN8hA==", "dev": true}, "@jridgewell/gen-mapping": {"version": "0.3.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@jridgewell/gen-mapping/-/gen-mapping-0.3.3.tgz", "integrity": "sha1-fgLm6135AartsIUUIDsJZhQCQJg=", "dev": true, "requires": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.9"}}, "@jridgewell/resolve-uri": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz", "integrity": "sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==", "dev": true}, "@jridgewell/set-array": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.2.tgz", "integrity": "sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==", "dev": true}, "@jridgewell/source-map": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.2.tgz", "integrity": "sha512-m7O9o2uR8k2ObDysZYzdfhb08VuEml5oWGiosa1VdaPZ/A6QyPkAJuwN0Q1lhULOf6B7MtQmHENS743hWtCrgw==", "dev": true, "requires": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}, "dependencies": {"@jridgewell/gen-mapping": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz", "integrity": "sha512-mh65xKQAzI6iBcFzwv28KVWSmCkdRBWoOh+bYQGW3+6OZvbbN3TqMGo5hqYxQniRcH9F2VZIoJCm4pa3BPDK/A==", "dev": true, "requires": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.9"}}}}, "@jridgewell/sourcemap-codec": {"version": "1.4.14", "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz", "integrity": "sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==", "dev": true}, "@jridgewell/trace-mapping": {"version": "0.3.17", "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.17.tgz", "integrity": "sha512-MCNzAp77qzKca9+W/+I0+sEpaUnZoeasnghNeVc41VZCEKaCH73Vq3BZZ/SzWIgrqE4H4ceI+p+b6C0mHf9T4g==", "dev": true, "requires": {"@jridgewell/resolve-uri": "3.1.0", "@jridgewell/sourcemap-codec": "1.4.14"}}, "@leichtgewicht/ip-codec": {"version": "2.0.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@leichtgewicht/ip-codec/-/ip-codec-2.0.4.tgz", "integrity": "sha1-sqxibWy5yHGKtFkWbUu0Bbj/p4s=", "dev": true}, "@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "https://registry.npmmirror.com/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "integrity": "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==", "dev": true, "requires": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}}, "@nodelib/fs.stat": {"version": "2.0.5", "resolved": "https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "integrity": "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==", "dev": true}, "@nodelib/fs.walk": {"version": "1.2.8", "resolved": "https://registry.npmmirror.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "integrity": "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==", "dev": true, "requires": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}}, "@rollup/plugin-babel": {"version": "6.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@rollup/plugin-babel/-/plugin-babel-6.0.3.tgz", "integrity": "sha1-B8zeFd4njFgWcwNK1qzNtKFT3+s=", "dev": true, "requires": {"@babel/helper-module-imports": "^7.18.6", "@rollup/pluginutils": "^5.0.1"}}, "@rollup/plugin-commonjs": {"version": "23.0.4", "resolved": "https://registry.npmjs.org/@rollup/plugin-commonjs/-/plugin-commonjs-23.0.4.tgz", "integrity": "sha512-bOPJeTZg56D2MCm+TT4psP8e8Jmf1Jsi7pFUMl8BN5kOADNzofNHe47+84WVCt7D095xPghC235/YKuNDEhczg==", "dev": true, "requires": {"@rollup/pluginutils": "^5.0.1", "commondir": "^1.0.1", "estree-walker": "^2.0.2", "glob": "^8.0.3", "is-reference": "1.2.1", "magic-string": "^0.26.4"}}, "@rollup/plugin-node-resolve": {"version": "15.0.1", "resolved": "https://registry.npmmirror.com/@rollup/plugin-node-resolve/-/plugin-node-resolve-15.0.1.tgz", "integrity": "sha512-ReY88T7JhJjeRVbfCyNj+NXAG3IIsVMsX9b5/9jC98dRP8/yxlZdz7mHZbHk5zHr24wZZICS5AcXsFZAXYUQEg==", "dev": true, "requires": {"@rollup/pluginutils": "^5.0.1", "@types/resolve": "1.20.2", "deepmerge": "^4.2.2", "is-builtin-module": "^3.2.0", "is-module": "^1.0.0", "resolve": "^1.22.1"}}, "@rollup/plugin-typescript": {"version": "9.0.2", "resolved": "https://registry.npmmirror.com/@rollup/plugin-typescript/-/plugin-typescript-9.0.2.tgz", "integrity": "sha512-/sS93vmHUMjzDUsl5scNQr1mUlNE1QjBBvOhmRwJCH8k2RRhDIm3c977B3wdu3t3Ap17W6dDeXP3hj1P1Un1bA==", "dev": true, "requires": {"@rollup/pluginutils": "^5.0.1", "resolve": "^1.22.1"}}, "@rollup/pluginutils": {"version": "5.0.2", "resolved": "https://registry.npmmirror.com/@rollup/pluginutils/-/pluginutils-5.0.2.tgz", "integrity": "sha512-pTd9rIsP92h+B6wWwFbW8RkZv4hiR/xKsqre4SIuAOaOEQRxi0lqLke9k2/7WegC85GgUs9pjmOjCUi3In4vwA==", "dev": true, "requires": {"@types/estree": "^1.0.0", "estree-walker": "^2.0.2", "picomatch": "^2.3.1"}}, "@types/body-parser": {"version": "1.19.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/body-parser/-/body-parser-1.19.2.tgz", "integrity": "sha1-rqIFnii3ZYY5CBNHrE+rPeFm5vA=", "dev": true, "requires": {"@types/connect": "*", "@types/node": "*"}}, "@types/bonjour": {"version": "3.5.10", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/bonjour/-/bonjour-3.5.10.tgz", "integrity": "sha1-D2qt/gDqQU7chvXRBjV82pcB4nU=", "dev": true, "requires": {"@types/node": "*"}}, "@types/connect": {"version": "3.4.35", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/connect/-/connect-3.4.35.tgz", "integrity": "sha1-X89q5EXkAh0fwiGaSHPMc6O7KtE=", "dev": true, "requires": {"@types/node": "*"}}, "@types/connect-history-api-fallback": {"version": "1.3.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/connect-history-api-fallback/-/connect-history-api-fallback-1.3.5.tgz", "integrity": "sha1-0feooJ0O1aV67lrpwYq5uAMgXa4=", "dev": true, "requires": {"@types/express-serve-static-core": "*", "@types/node": "*"}}, "@types/eslint": {"version": "8.4.10", "resolved": "https://registry.npmmirror.com/@types/eslint/-/eslint-8.4.10.tgz", "integrity": "sha512-Sl/HOqN8NKPmhWo2VBEPm0nvHnu2LL3v9vKo8MEq0EtbJ4eVzGPl41VNPvn5E1i5poMk4/XD8UriLHpJvEP/Nw==", "dev": true, "requires": {"@types/estree": "*", "@types/json-schema": "*"}}, "@types/eslint-scope": {"version": "3.7.4", "resolved": "https://registry.npmmirror.com/@types/eslint-scope/-/eslint-scope-3.7.4.tgz", "integrity": "sha512-9K<PERSON>zoImiZc3HlIp6AVUDE4CWYx22a+lhSZMYNpbjW04+YF0KWj4pJXnEMjdnFTiQibFFmElcsasJXDbdI/EPhA==", "dev": true, "requires": {"@types/eslint": "*", "@types/estree": "*"}}, "@types/estree": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/@types/estree/-/estree-1.0.0.tgz", "integrity": "sha512-WulqXMDUTYAXCjZnk6JtIHPigp55cVtDgDrO2gHRwhyJto21+1zbVCtOYB2L1F9w4qCQ0rOGWBnBe0FNTiEJIQ==", "dev": true}, "@types/express": {"version": "4.17.17", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/express/-/express-4.17.17.tgz", "integrity": "sha1-AdVDf275z6hmjmFuE8LyrJpJGuQ=", "dev": true, "requires": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}}, "@types/express-serve-static-core": {"version": "4.17.33", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/express-serve-static-core/-/express-serve-static-core-4.17.33.tgz", "integrity": "sha1-3jXTCp1jfcFFCtGN1YPXXVcz1UM=", "dev": true, "requires": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*"}}, "@types/html-minifier-terser": {"version": "6.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz", "integrity": "sha1-T8M6AMHQwWmHsaIM+S0gYUxVrDU=", "dev": true}, "@types/http-proxy": {"version": "1.17.10", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/http-proxy/-/http-proxy-1.17.10.tgz", "integrity": "sha1-5XbI5KDMXGoTiBkCWojhZ+uzjWw=", "dev": true, "requires": {"@types/node": "*"}}, "@types/json-schema": {"version": "7.0.11", "resolved": "https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.11.tgz", "integrity": "sha512-wOuvG1SN4Us4rez+tylwwwCV1psiNVOkJeM3AUWUNWg/jDQY2+HE/444y5gc+jBmRqASOm2Oeh5c1axHobwRKQ==", "dev": true}, "@types/mime": {"version": "3.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/mime/-/mime-3.0.1.tgz", "integrity": "sha1-X48rygpYY8tpvAsKzYjJbLHUrhA=", "dev": true}, "@types/node": {"version": "18.11.11", "resolved": "https://registry.npmjs.org/@types/node/-/node-18.11.11.tgz", "integrity": "sha512-KJ021B1nlQUBLopzZmPBVuGU9un7WJd/W4ya7Ih02B4Uwky5Nja0yGYav2EfYIk0RR2Q9oVhf60S2XR1BCWJ2g==", "dev": true}, "@types/qs": {"version": "6.9.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/qs/-/qs-6.9.7.tgz", "integrity": "sha1-Y7t9Bn2xB8weRXwwO8JdUR/r9ss=", "dev": true}, "@types/range-parser": {"version": "1.2.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/range-parser/-/range-parser-1.2.4.tgz", "integrity": "sha1-zWZ7z90CUhOq+3ylkVqTJZCs3Nw=", "dev": true}, "@types/resolve": {"version": "1.20.2", "resolved": "https://registry.npmmirror.com/@types/resolve/-/resolve-1.20.2.tgz", "integrity": "sha512-60BCwRFOZCQhDncwQdxxeOEEkbc5dIMccYLwbxsS4TUNeVECQ/pBJ0j09mrHOl/JJvpRPGwO9SvE4nR2Nb/a4Q==", "dev": true}, "@types/retry": {"version": "0.12.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/retry/-/retry-0.12.0.tgz", "integrity": "sha1-KzXsz87n04zXKtmSMvvVi/+zyE0=", "dev": true}, "@types/semver": {"version": "7.3.13", "resolved": "https://registry.npmmirror.com/@types/semver/-/semver-7.3.13.tgz", "integrity": "sha512-21cFJr9z3g5dW8B0CVI9g2O9beqaThGQ6ZFBqHfwhzLDKUxaqTIy3vnfah/UPkfOiF2pLq+tGz+W8RyCskuslw==", "dev": true}, "@types/serve-index": {"version": "1.9.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/serve-index/-/serve-index-1.9.1.tgz", "integrity": "sha1-G16FNwoZLAHsbOxHNc8pFzN6Yng=", "dev": true, "requires": {"@types/express": "*"}}, "@types/serve-static": {"version": "1.15.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/serve-static/-/serve-static-1.15.1.tgz", "integrity": "sha1-hrF1Pwvk+aG+5o1Fn82lvk6lK10=", "dev": true, "requires": {"@types/mime": "*", "@types/node": "*"}}, "@types/sockjs": {"version": "0.3.33", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/sockjs/-/sockjs-0.3.33.tgz", "integrity": "sha1-Vw06C5msmVNg4xNv1gRRE7G9I28=", "dev": true, "requires": {"@types/node": "*"}}, "@types/ws": {"version": "8.5.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@types/ws/-/ws-8.5.4.tgz", "integrity": "sha1-uxDjYRbW5XDdlDc1+GyTPBWHuKU=", "dev": true, "requires": {"@types/node": "*"}}, "@typescript-eslint/eslint-plugin": {"version": "5.42.1", "resolved": "https://registry.npmmirror.com/@typescript-eslint/eslint-plugin/-/eslint-plugin-5.42.1.tgz", "integrity": "sha512-LyR6x784JCiJ1j6sH5Y0K6cdExqCCm8DJUTcwG5ThNXJj/G8o5E56u5EdG4SLy+bZAwZBswC+GYn3eGdttBVCg==", "dev": true, "requires": {"@typescript-eslint/scope-manager": "5.42.1", "@typescript-eslint/type-utils": "5.42.1", "@typescript-eslint/utils": "5.42.1", "debug": "^4.3.4", "ignore": "^5.2.0", "natural-compare-lite": "^1.4.0", "regexpp": "^3.2.0", "semver": "^7.3.7", "tsutils": "^3.21.0"}}, "@typescript-eslint/parser": {"version": "5.42.1", "resolved": "https://registry.npmmirror.com/@typescript-eslint/parser/-/parser-5.42.1.tgz", "integrity": "sha512-kAV+NiNBWVQDY9gDJDToTE/NO8BHi4f6b7zTsVAJoTkmB/zlfOpiEVBzHOKtlgTndCKe8vj9F/PuolemZSh50Q==", "dev": true, "requires": {"@typescript-eslint/scope-manager": "5.42.1", "@typescript-eslint/types": "5.42.1", "@typescript-eslint/typescript-estree": "5.42.1", "debug": "^4.3.4"}}, "@typescript-eslint/scope-manager": {"version": "5.42.1", "resolved": "https://registry.npmmirror.com/@typescript-eslint/scope-manager/-/scope-manager-5.42.1.tgz", "integrity": "sha512-QAZY/CBP1Emx4rzxurgqj3rUinfsh/6mvuKbLNMfJMMKYLRBfweus8brgXF8f64ABkIZ3zdj2/rYYtF8eiuksQ==", "dev": true, "requires": {"@typescript-eslint/types": "5.42.1", "@typescript-eslint/visitor-keys": "5.42.1"}}, "@typescript-eslint/type-utils": {"version": "5.42.1", "resolved": "https://registry.npmmirror.com/@typescript-eslint/type-utils/-/type-utils-5.42.1.tgz", "integrity": "sha512-WWiMChneex5w4xPIX56SSnQQo0tEOy5ZV2dqmj8Z371LJ0E+aymWD25JQ/l4FOuuX+Q49A7pzh/CGIQflxMVXg==", "dev": true, "requires": {"@typescript-eslint/typescript-estree": "5.42.1", "@typescript-eslint/utils": "5.42.1", "debug": "^4.3.4", "tsutils": "^3.21.0"}}, "@typescript-eslint/types": {"version": "5.42.1", "resolved": "https://registry.npmmirror.com/@typescript-eslint/types/-/types-5.42.1.tgz", "integrity": "sha512-Qrco9dsFF5lhalz+lLFtxs3ui1/YfC6NdXu+RAGBa8uSfn01cjO7ssCsjIsUs484vny9Xm699FSKwpkCcqwWwA==", "dev": true}, "@typescript-eslint/typescript-estree": {"version": "5.42.1", "resolved": "https://registry.npmmirror.com/@typescript-eslint/typescript-estree/-/typescript-estree-5.42.1.tgz", "integrity": "sha512-qElc0bDOuO0B8wDhhW4mYVgi/LZL+igPwXtV87n69/kYC/7NG3MES0jHxJNCr4EP7kY1XVsRy8C/u3DYeTKQmw==", "dev": true, "requires": {"@typescript-eslint/types": "5.42.1", "@typescript-eslint/visitor-keys": "5.42.1", "debug": "^4.3.4", "globby": "^11.1.0", "is-glob": "^4.0.3", "semver": "^7.3.7", "tsutils": "^3.21.0"}}, "@typescript-eslint/utils": {"version": "5.42.1", "resolved": "https://registry.npmmirror.com/@typescript-eslint/utils/-/utils-5.42.1.tgz", "integrity": "sha512-Gxvf12xSp3iYZd/fLqiQRD4uKZjDNR01bQ+j8zvhPjpsZ4HmvEFL/tC4amGNyxN9Rq+iqvpHLhlqx6KTxz9ZyQ==", "dev": true, "requires": {"@types/json-schema": "^7.0.9", "@types/semver": "^7.3.12", "@typescript-eslint/scope-manager": "5.42.1", "@typescript-eslint/types": "5.42.1", "@typescript-eslint/typescript-estree": "5.42.1", "eslint-scope": "^5.1.1", "eslint-utils": "^3.0.0", "semver": "^7.3.7"}, "dependencies": {"eslint-scope": {"version": "5.1.1", "resolved": "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-5.1.1.tgz", "integrity": "sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==", "dev": true, "requires": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}}, "estraverse": {"version": "4.3.0", "resolved": "https://registry.npmmirror.com/estraverse/-/estraverse-4.3.0.tgz", "integrity": "sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==", "dev": true}}}, "@typescript-eslint/visitor-keys": {"version": "5.42.1", "resolved": "https://registry.npmmirror.com/@typescript-eslint/visitor-keys/-/visitor-keys-5.42.1.tgz", "integrity": "sha512-LOQtSF4z+hejmpUvitPlc4hA7ERGoj2BVkesOcG91HCn8edLGUXbTrErmutmPbl8Bo9HjAvOO/zBKQHExXNA2A==", "dev": true, "requires": {"@typescript-eslint/types": "5.42.1", "eslint-visitor-keys": "^3.3.0"}}, "@webassemblyjs/ast": {"version": "1.11.1", "resolved": "https://registry.npmmirror.com/@webassemblyjs/ast/-/ast-1.11.1.tgz", "integrity": "sha512-ukBh14qFLjxTQNTXocdyksN5QdM28S1CxHt2rdskFyL+xFV7VremuBLVbmCePj+URalXBENx/9Lm7lnhihtCSw==", "dev": true, "requires": {"@webassemblyjs/helper-numbers": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1"}}, "@webassemblyjs/floating-point-hex-parser": {"version": "1.11.1", "resolved": "https://registry.npmmirror.com/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.11.1.tgz", "integrity": "sha512-iGRfyc5Bq+NnNuX8b5hwBrRjzf0ocrJPI6GWFodBFzmFnyvrQ83SHKhmilCU/8Jv67i4GJZBMhEzltxzcNagtQ==", "dev": true}, "@webassemblyjs/helper-api-error": {"version": "1.11.1", "resolved": "https://registry.npmmirror.com/@webassemblyjs/helper-api-error/-/helper-api-error-1.11.1.tgz", "integrity": "sha512-RlhS8CBCXfRUR/cwo2ho9bkheSXG0+NwooXcc3PAILALf2QLdFyj7KGsKRbVc95hZnhnERon4kW/D3SZpp6Tcg==", "dev": true}, "@webassemblyjs/helper-buffer": {"version": "1.11.1", "resolved": "https://registry.npmmirror.com/@webassemblyjs/helper-buffer/-/helper-buffer-1.11.1.tgz", "integrity": "sha512-gwikF65aDNeeXa8JxXa2BAk+REjSyhrNC9ZwdT0f8jc4dQQeDQ7G4m0f2QCLPJiMTTO6wfDmRmj/pW0PsUvIcA==", "dev": true}, "@webassemblyjs/helper-numbers": {"version": "1.11.1", "resolved": "https://registry.npmmirror.com/@webassemblyjs/helper-numbers/-/helper-numbers-1.11.1.tgz", "integrity": "sha512-vDkbxiB8zfnPdNK9Rajcey5C0w+QJugEglN0of+kmO8l7lDb77AnlKYQF7aarZuCrv+l0UvqL+68gSDr3k9LPQ==", "dev": true, "requires": {"@webassemblyjs/floating-point-hex-parser": "1.11.1", "@webassemblyjs/helper-api-error": "1.11.1", "@xtuc/long": "4.2.2"}}, "@webassemblyjs/helper-wasm-bytecode": {"version": "1.11.1", "resolved": "https://registry.npmmirror.com/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.11.1.tgz", "integrity": "sha512-PvpoOGiJwXeTrSf/qfudJhwlvDQxFgelbMqtq52WWiXC6Xgg1IREdngmPN3bs4RoO83PnL/nFrxucXj1+BX62Q==", "dev": true}, "@webassemblyjs/helper-wasm-section": {"version": "1.11.1", "resolved": "https://registry.npmmirror.com/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.11.1.tgz", "integrity": "sha512-10P9No29rYX1j7F3EVPX3JvGPQPae+AomuSTPiF9eBQeChHI6iqjMIwR9JmOJXwpnn/oVGDk7I5IlskuMwU/pg==", "dev": true, "requires": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-buffer": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/wasm-gen": "1.11.1"}}, "@webassemblyjs/ieee754": {"version": "1.11.1", "resolved": "https://registry.npmmirror.com/@webassemblyjs/ieee754/-/ieee754-1.11.1.tgz", "integrity": "sha512-hJ87QIPtAMKbFq6CGTkZYJivEwZDbQUgYd3qKSadTNOhVY7p+gfP6Sr0lLRVTaG1JjFj+r3YchoqRYxNH3M0GQ==", "dev": true, "requires": {"@xtuc/ieee754": "^1.2.0"}}, "@webassemblyjs/leb128": {"version": "1.11.1", "resolved": "https://registry.npmmirror.com/@webassemblyjs/leb128/-/leb128-1.11.1.tgz", "integrity": "sha512-BJ2P0hNZ0u+Th1YZXJpzW6miwqQUGcIHT1G/sf72gLVD9DZ5AdYTqPNbHZh6K1M5VmKvFXwGSWZADz+qBWxeRw==", "dev": true, "requires": {"@xtuc/long": "4.2.2"}}, "@webassemblyjs/utf8": {"version": "1.11.1", "resolved": "https://registry.npmmirror.com/@webassemblyjs/utf8/-/utf8-1.11.1.tgz", "integrity": "sha512-9kqcxAEdMhiwQkHpkNiorZzqpGrodQQ2IGrHHxCy+Ozng0ofyMA0lTqiLkVs1uzTRejX+/O0EOT7KxqVPuXosQ==", "dev": true}, "@webassemblyjs/wasm-edit": {"version": "1.11.1", "resolved": "https://registry.npmmirror.com/@webassemblyjs/wasm-edit/-/wasm-edit-1.11.1.tgz", "integrity": "sha512-g+RsupUC1aTHfR8CDgnsVRVZFJqdkFHpsHMfJuWQzWU3tvnLC07UqHICfP+4XyL2tnr1amvl1Sdp06TnYCmVkA==", "dev": true, "requires": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-buffer": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/helper-wasm-section": "1.11.1", "@webassemblyjs/wasm-gen": "1.11.1", "@webassemblyjs/wasm-opt": "1.11.1", "@webassemblyjs/wasm-parser": "1.11.1", "@webassemblyjs/wast-printer": "1.11.1"}}, "@webassemblyjs/wasm-gen": {"version": "1.11.1", "resolved": "https://registry.npmmirror.com/@webassemblyjs/wasm-gen/-/wasm-gen-1.11.1.tgz", "integrity": "sha512-F7QqKXwwNlMmsulj6+O7r4mmtAlCWfO/0HdgOxSklZfQcDu0TpLiD1mRt/zF25Bk59FIjEuGAIyn5ei4yMfLhA==", "dev": true, "requires": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/ieee754": "1.11.1", "@webassemblyjs/leb128": "1.11.1", "@webassemblyjs/utf8": "1.11.1"}}, "@webassemblyjs/wasm-opt": {"version": "1.11.1", "resolved": "https://registry.npmmirror.com/@webassemblyjs/wasm-opt/-/wasm-opt-1.11.1.tgz", "integrity": "sha512-VqnkNqnZlU5EB64pp1l7hdm3hmQw7Vgqa0KF/KCNO9sIpI6Fk6brDEiX+iCOYrvMuBWDws0NkTOxYEb85XQHHw==", "dev": true, "requires": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-buffer": "1.11.1", "@webassemblyjs/wasm-gen": "1.11.1", "@webassemblyjs/wasm-parser": "1.11.1"}}, "@webassemblyjs/wasm-parser": {"version": "1.11.1", "resolved": "https://registry.npmmirror.com/@webassemblyjs/wasm-parser/-/wasm-parser-1.11.1.tgz", "integrity": "sha512-rrBujw+dJu32gYB7/Lup6UhdkPx9S9SnobZzRVL7VcBH9Bt9bCBLEuX/YXOOtBsOZ4NQrRykKhffRWHvigQvOA==", "dev": true, "requires": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-api-error": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/ieee754": "1.11.1", "@webassemblyjs/leb128": "1.11.1", "@webassemblyjs/utf8": "1.11.1"}}, "@webassemblyjs/wast-printer": {"version": "1.11.1", "resolved": "https://registry.npmmirror.com/@webassemblyjs/wast-printer/-/wast-printer-1.11.1.tgz", "integrity": "sha512-IQboUWM4eKzWW+N/jij2sRatKMh99QEelo3Eb2q0qXkvPRISAj8Qxtmw5itwqK+TTkBuUIE45AxYPToqPtL5gg==", "dev": true, "requires": {"@webassemblyjs/ast": "1.11.1", "@xtuc/long": "4.2.2"}}, "@webpack-cli/configtest": {"version": "2.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@webpack-cli/configtest/-/configtest-2.0.1.tgz", "integrity": "sha1-ppcg9sm61q71So+mupw1M+fvTH8=", "dev": true}, "@webpack-cli/info": {"version": "2.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@webpack-cli/info/-/info-2.0.1.tgz", "integrity": "sha1-7tdFeZyRDSAIHgblF3wrJWnxZsA=", "dev": true}, "@webpack-cli/serve": {"version": "2.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/@webpack-cli/serve/-/serve-2.0.1.tgz", "integrity": "sha1-NL3DFyehiJGYhVkT2y8nCs5te/g=", "dev": true}, "@xtuc/ieee754": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/@xtuc/ieee754/-/ieee754-1.2.0.tgz", "integrity": "sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==", "dev": true}, "@xtuc/long": {"version": "4.2.2", "resolved": "https://registry.npmmirror.com/@xtuc/long/-/long-4.2.2.tgz", "integrity": "sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==", "dev": true}, "accepts": {"version": "1.3.8", "resolved": "https://registry.npmmirror.com/accepts/-/accepts-1.3.8.tgz", "integrity": "sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==", "dev": true, "requires": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}}, "acorn": {"version": "8.8.1", "resolved": "https://registry.npmmirror.com/acorn/-/acorn-8.8.1.tgz", "integrity": "sha512-7zFpHzhnqYKrkYdUjF1HI1bzd0VygEGX8lFk4k5zVMqHEoES+P+7TKI+EvLO9WVMJ8eekdO0aDEK044xTXwPPA==", "dev": true}, "acorn-import-assertions": {"version": "1.8.0", "resolved": "https://registry.npmmirror.com/acorn-import-assertions/-/acorn-import-assertions-1.8.0.tgz", "integrity": "sha512-m7VZ3jwz4eK6A4Vtt8Ew1/mNbP24u0FhdyfA7fSvnJR6LMdfOYnmuIrrJAgrYfYJ10F/otaHTtrtrtmHdMNzEw==", "dev": true}, "acorn-jsx": {"version": "5.3.2", "resolved": "https://registry.npmmirror.com/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "integrity": "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==", "dev": true}, "ajv": {"version": "6.12.6", "resolved": "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz", "integrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==", "dev": true, "requires": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ajv-formats": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/ajv-formats/-/ajv-formats-2.1.1.tgz", "integrity": "sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==", "dev": true, "requires": {"ajv": "^8.0.0"}, "dependencies": {"ajv": {"version": "8.11.2", "resolved": "https://registry.npmmirror.com/ajv/-/ajv-8.11.2.tgz", "integrity": "sha512-E4bfmKAhGiSTvMfL1Myyycaub+cUEU2/IvpylXkUu7CHBkBj1f/ikdzbD7YQ6FKUbixDxeYvB/xY4fvyroDlQg==", "dev": true, "requires": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}}, "json-schema-traverse": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "integrity": "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==", "dev": true}}}, "ajv-keywords": {"version": "3.5.2", "resolved": "https://registry.npmmirror.com/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "integrity": "sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==", "dev": true}, "ansi-html-community": {"version": "0.0.8", "resolved": "https://registry.npmmirror.com/ansi-html-community/-/ansi-html-community-0.0.8.tgz", "integrity": "sha512-1APHAyr3+PCamwNw3bXCPp4HFLONZt/yIH0sZp0/469KWNTEy+qN5jQ3GVX6DMZ1UXAi34yVwtTeaG/HpBuuzw==", "dev": true}, "ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "dev": true}, "ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "anymatch": {"version": "3.1.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/anymatch/-/anymatch-3.1.3.tgz", "integrity": "sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=", "dev": true, "requires": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}}, "argparse": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/argparse/-/argparse-2.0.1.tgz", "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==", "dev": true}, "array-flatten": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==", "dev": true}, "array-union": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/array-union/-/array-union-2.1.0.tgz", "integrity": "sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==", "dev": true}, "babel-code-frame": {"version": "6.26.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/babel-code-frame/-/babel-code-frame-6.26.0.tgz", "integrity": "sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=", "dev": true, "requires": {"chalk": "^1.1.3", "esutils": "^2.0.2", "js-tokens": "^3.0.2"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true}, "ansi-styles": {"version": "2.2.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ansi-styles/-/ansi-styles-2.2.1.tgz", "integrity": "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=", "dev": true}, "chalk": {"version": "1.1.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/chalk/-/chalk-1.1.3.tgz", "integrity": "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=", "dev": true, "requires": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}}, "escape-string-regexp": {"version": "1.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=", "dev": true}, "js-tokens": {"version": "3.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/js-tokens/-/js-tokens-3.0.2.tgz", "integrity": "sha1-mGbfOVECEw449/mWvOtlRDIJwls=", "dev": true}, "strip-ansi": {"version": "3.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/strip-ansi/-/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "requires": {"ansi-regex": "^2.0.0"}}, "supports-color": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/supports-color/-/supports-color-2.0.0.tgz", "integrity": "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=", "dev": true}}}, "babel-core": {"version": "6.26.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/babel-core/-/babel-core-6.26.3.tgz", "integrity": "sha1-suLwnjQtDwyI4vAuBneUEl51wgc=", "dev": true, "requires": {"babel-code-frame": "^6.26.0", "babel-generator": "^6.26.0", "babel-helpers": "^6.24.1", "babel-messages": "^6.23.0", "babel-register": "^6.26.0", "babel-runtime": "^6.26.0", "babel-template": "^6.26.0", "babel-traverse": "^6.26.0", "babel-types": "^6.26.0", "babylon": "^6.18.0", "convert-source-map": "^1.5.1", "debug": "^2.6.9", "json5": "^0.5.1", "lodash": "^4.17.4", "minimatch": "^3.0.4", "path-is-absolute": "^1.0.1", "private": "^0.1.8", "slash": "^1.0.0", "source-map": "^0.5.7"}, "dependencies": {"brace-expansion": {"version": "1.1.11", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "debug": {"version": "2.6.9", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "minimatch": {"version": "3.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "requires": {"brace-expansion": "^1.1.7"}}, "ms": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "slash": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/slash/-/slash-1.0.0.tgz", "integrity": "sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=", "dev": true}, "source-map": {"version": "0.5.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true}}}, "babel-generator": {"version": "6.26.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/babel-generator/-/babel-generator-6.26.1.tgz", "integrity": "sha1-GERAjTuPDTWkBOp6wYDwh6YBvZA=", "dev": true, "requires": {"babel-messages": "^6.23.0", "babel-runtime": "^6.26.0", "babel-types": "^6.26.0", "detect-indent": "^4.0.0", "jsesc": "^1.3.0", "lodash": "^4.17.4", "source-map": "^0.5.7", "trim-right": "^1.0.1"}, "dependencies": {"source-map": {"version": "0.5.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true}}}, "babel-helpers": {"version": "6.24.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/babel-helpers/-/babel-helpers-6.24.1.tgz", "integrity": "sha1-NHHenK7DiOXIUOWX5Yom3fN2ArI=", "dev": true, "requires": {"babel-runtime": "^6.22.0", "babel-template": "^6.24.1"}}, "babel-messages": {"version": "6.23.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/babel-messages/-/babel-messages-6.23.0.tgz", "integrity": "sha1-8830cDhYA1sqKVHG7F7fbGLyYw4=", "dev": true, "requires": {"babel-runtime": "^6.22.0"}}, "babel-plugin-polyfill-corejs2": {"version": "0.3.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.3.3.tgz", "integrity": "sha1-XRvTg20KGeG4S78tlkDMtvlRwSI=", "dev": true, "requires": {"@babel/compat-data": "^7.17.7", "@babel/helper-define-polyfill-provider": "^0.3.3", "semver": "^6.1.1"}, "dependencies": {"semver": {"version": "6.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "dev": true}}}, "babel-plugin-polyfill-corejs3": {"version": "0.6.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.6.0.tgz", "integrity": "sha1-Vq2II3E36t5IWnG1L3Lb7VfGIwo=", "dev": true, "requires": {"@babel/helper-define-polyfill-provider": "^0.3.3", "core-js-compat": "^3.25.1"}}, "babel-plugin-polyfill-regenerator": {"version": "0.4.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.4.1.tgz", "integrity": "sha1-OQ+Rw42QRzWS7UM1HoAanT4P10c=", "dev": true, "requires": {"@babel/helper-define-polyfill-provider": "^0.3.3"}}, "babel-register": {"version": "6.26.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/babel-register/-/babel-register-6.26.0.tgz", "integrity": "sha1-btAhFz4vy0htestFxgCahW9kcHE=", "dev": true, "requires": {"babel-core": "^6.26.0", "babel-runtime": "^6.26.0", "core-js": "^2.5.0", "home-or-tmp": "^2.0.0", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "source-map-support": "^0.4.15"}, "dependencies": {"source-map": {"version": "0.5.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true}, "source-map-support": {"version": "0.4.18", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/source-map-support/-/source-map-support-0.4.18.tgz", "integrity": "sha1-Aoam3ovkJkEzhZTpfM6nXwosWF8=", "dev": true, "requires": {"source-map": "^0.5.6"}}}}, "babel-runtime": {"version": "6.26.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/babel-runtime/-/babel-runtime-6.26.0.tgz", "integrity": "sha1-llxwWGaOgrVde/4E/yM3vItWR/4=", "dev": true, "requires": {"core-js": "^2.4.0", "regenerator-runtime": "^0.11.0"}}, "babel-template": {"version": "6.26.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/babel-template/-/babel-template-6.26.0.tgz", "integrity": "sha1-3gPi0WOWsGn0bdn/+FIfsaDjXgI=", "dev": true, "requires": {"babel-runtime": "^6.26.0", "babel-traverse": "^6.26.0", "babel-types": "^6.26.0", "babylon": "^6.18.0", "lodash": "^4.17.4"}}, "babel-traverse": {"version": "6.26.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/babel-traverse/-/babel-traverse-6.26.0.tgz", "integrity": "sha1-RqnL1+3MYsjlwGTi0tjQ9ANXZu4=", "dev": true, "requires": {"babel-code-frame": "^6.26.0", "babel-messages": "^6.23.0", "babel-runtime": "^6.26.0", "babel-types": "^6.26.0", "babylon": "^6.18.0", "debug": "^2.6.8", "globals": "^9.18.0", "invariant": "^2.2.2", "lodash": "^4.17.4"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "globals": {"version": "9.18.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/globals/-/globals-9.18.0.tgz", "integrity": "sha1-qjiWs+abSH8X4x7SFD1pqOMMLYo=", "dev": true}, "ms": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "babel-types": {"version": "6.26.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/babel-types/-/babel-types-6.26.0.tgz", "integrity": "sha1-o7Bz+Uq0nrb6Vc1lInozQ4BjJJc=", "dev": true, "requires": {"babel-runtime": "^6.26.0", "esutils": "^2.0.2", "lodash": "^4.17.4", "to-fast-properties": "^1.0.3"}, "dependencies": {"to-fast-properties": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/to-fast-properties/-/to-fast-properties-1.0.3.tgz", "integrity": "sha1-uDVx+k2MJbguIxsG46MFXeTKGkc=", "dev": true}}}, "babylon": {"version": "6.18.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/babylon/-/babylon-6.18.0.tgz", "integrity": "sha1-ry87iPpvXB5MY00aD46sT1WzleM=", "dev": true}, "balanced-match": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==", "dev": true}, "batch": {"version": "0.6.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/batch/-/batch-0.6.1.tgz", "integrity": "sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=", "dev": true}, "binary-extensions": {"version": "2.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/binary-extensions/-/binary-extensions-2.2.0.tgz", "integrity": "sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0=", "dev": true}, "body-parser": {"version": "1.20.1", "resolved": "https://registry.npmmirror.com/body-parser/-/body-parser-1.20.1.tgz", "integrity": "sha512-jWi7abTbYwajOytWCQc37VulmWiRae5RyTpaCyDcS5/lMdtwSz5lOpDE67srw/HYe35f1z3fDQw+3txg7gNtWw==", "dev": true, "requires": {"bytes": "3.1.2", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.11.0", "raw-body": "2.5.1", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==", "dev": true}}}, "bonjour-service": {"version": "1.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/bonjour-service/-/bonjour-service-1.1.0.tgz", "integrity": "sha1-QkFwJo1oryb/g6XGQLld7wGAOhM=", "dev": true, "requires": {"array-flatten": "^2.1.2", "dns-equal": "^1.0.0", "fast-deep-equal": "^3.1.3", "multicast-dns": "^7.2.5"}, "dependencies": {"array-flatten": {"version": "2.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/array-flatten/-/array-flatten-2.1.2.tgz", "integrity": "sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk=", "dev": true}}}, "boolbase": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/boolbase/-/boolbase-1.0.0.tgz", "integrity": "sha1-aN/1++YMUes3cl6p4+0xDcwed24=", "dev": true}, "brace-expansion": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-2.0.1.tgz", "integrity": "sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==", "dev": true, "requires": {"balanced-match": "^1.0.0"}}, "braces": {"version": "3.0.2", "resolved": "https://registry.npmmirror.com/braces/-/braces-3.0.2.tgz", "integrity": "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==", "dev": true, "requires": {"fill-range": "^7.0.1"}}, "browserslist": {"version": "4.21.4", "resolved": "https://registry.npmmirror.com/browserslist/-/browserslist-4.21.4.tgz", "integrity": "sha512-CBHJJdDmgjl3daYjN5Cp5kbTf1mUhZoS+beLklHIvkOWscs83YAhLlF3Wsh/lciQYAcbBJgTOD44VtG31ZM4Hw==", "dev": true, "requires": {"caniuse-lite": "^1.0.30001400", "electron-to-chromium": "^1.4.251", "node-releases": "^2.0.6", "update-browserslist-db": "^1.0.9"}}, "buffer-from": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "integrity": "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==", "dev": true}, "builtin-modules": {"version": "3.3.0", "resolved": "https://registry.npmmirror.com/builtin-modules/-/builtin-modules-3.3.0.tgz", "integrity": "sha512-zhaCDicdLuWN5UbN5IMnFqNMhNfo919sH85y2/ea+5Yg9TsTkeZxpL+JLbp6cgYFS4sRLp3YV4S6yDuqVWHYOw==", "dev": true}, "bytes": {"version": "3.1.2", "resolved": "https://registry.npmmirror.com/bytes/-/bytes-3.1.2.tgz", "integrity": "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==", "dev": true}, "call-bind": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==", "dev": true, "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "callsites": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/callsites/-/callsites-3.1.0.tgz", "integrity": "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==", "dev": true}, "camel-case": {"version": "4.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/camel-case/-/camel-case-4.1.2.tgz", "integrity": "sha1-lygHKpVPgFIoIlpt7qazhGHhvVo=", "dev": true, "requires": {"pascal-case": "^3.1.2", "tslib": "^2.0.3"}, "dependencies": {"tslib": {"version": "2.5.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/tslib/-/tslib-2.5.0.tgz", "integrity": "sha1-Qr/thvV4eutB0DGGbI9AJCng/d8=", "dev": true}}}, "caniuse-lite": {"version": "1.0.30001441", "resolved": "https://registry.npmmirror.com/caniuse-lite/-/caniuse-lite-1.0.30001441.tgz", "integrity": "sha512-OyxRR4Vof59I3yGWXws6i908EtGbMzVUi3ganaZQHmydk1iwDhRnvaPG2WaR0KcqrDFKrxVZHULT396LEPhXfg==", "dev": true}, "chalk": {"version": "4.1.2", "resolved": "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz", "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "chokidar": {"version": "3.5.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/chokidar/-/chokidar-3.5.3.tgz", "integrity": "sha1-HPN8hwe5Mr0a8a4iwEMuKs0ZA70=", "dev": true, "requires": {"anymatch": "~3.1.2", "braces": "~3.0.2", "fsevents": "~2.3.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "dependencies": {"glob-parent": {"version": "5.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "dev": true, "requires": {"is-glob": "^4.0.1"}}}}, "chrome-trace-event": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz", "integrity": "sha512-p3KULyQg4S7NIHixdwbGX+nFHkoBiA4YQmyWtjb8XngSKV124nJmRysgAeujbUVb15vh+RvFUfCPqU7rXk+hZg==", "dev": true}, "clean-css": {"version": "5.3.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/clean-css/-/clean-css-5.3.2.tgz", "integrity": "sha1-cOzH1NQRSSH10pg0n/hqMamXUiQ=", "dev": true, "requires": {"source-map": "~0.6.0"}}, "clone-deep": {"version": "4.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/clone-deep/-/clone-deep-4.0.1.tgz", "integrity": "sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c=", "dev": true, "requires": {"is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^3.0.0"}}, "color-convert": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "dev": true}, "colorette": {"version": "2.0.19", "resolved": "https://registry.npmmirror.com/colorette/-/colorette-2.0.19.tgz", "integrity": "sha512-3tlv/dIP7FWvj3BsbHrGLJ6l/oKh1O3TcgBqMn+yyCagOxc23fyzDS6HypQbgxWbkpDnf52p1LuR4eWDQ/K9WQ==", "dev": true}, "commander": {"version": "2.20.3", "resolved": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "integrity": "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==", "dev": true}, "commondir": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/commondir/-/commondir-1.0.1.tgz", "integrity": "sha512-W9pAhw0ja1Edb5GVdIF1mjZw/ASI0AlShXM83UUGe2DVr5TdAPEA1OA8m/g8zWp9x6On7gqufY+FatDbC3MDQg==", "dev": true}, "compressible": {"version": "2.0.18", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/compressible/-/compressible-2.0.18.tgz", "integrity": "sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=", "dev": true, "requires": {"mime-db": ">= 1.43.0 < 2"}}, "compression": {"version": "1.7.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/compression/-/compression-1.7.4.tgz", "integrity": "sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48=", "dev": true, "requires": {"accepts": "~1.3.5", "bytes": "3.0.0", "compressible": "~2.0.16", "debug": "2.6.9", "on-headers": "~1.0.2", "safe-buffer": "5.1.2", "vary": "~1.1.2"}, "dependencies": {"bytes": {"version": "3.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/bytes/-/bytes-3.0.0.tgz", "integrity": "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=", "dev": true}, "debug": {"version": "2.6.9", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "safe-buffer": {"version": "5.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "dev": true}}}, "concat-map": {"version": "0.0.1", "resolved": "https://registry.npmmirror.com/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==", "dev": true}, "connect-history-api-fallback": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/connect-history-api-fallback/-/connect-history-api-fallback-2.0.0.tgz", "integrity": "sha1-ZHJkhFJRoNryW5fOh4NMrOD18cg=", "dev": true}, "content-disposition": {"version": "0.5.4", "resolved": "https://registry.npmmirror.com/content-disposition/-/content-disposition-0.5.4.tgz", "integrity": "sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==", "dev": true, "requires": {"safe-buffer": "5.2.1"}}, "content-type": {"version": "1.0.4", "resolved": "https://registry.npmmirror.com/content-type/-/content-type-1.0.4.tgz", "integrity": "sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA==", "dev": true}, "convert-source-map": {"version": "1.9.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/convert-source-map/-/convert-source-map-1.9.0.tgz", "integrity": "sha1-f6rmI1P7QhM2bQypg1jSLoNosF8=", "dev": true}, "cookie": {"version": "0.5.0", "resolved": "https://registry.npmmirror.com/cookie/-/cookie-0.5.0.tgz", "integrity": "sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==", "dev": true}, "cookie-signature": {"version": "1.0.6", "resolved": "https://registry.npmmirror.com/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==", "dev": true}, "copy-webpack-plugin": {"version": "11.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/copy-webpack-plugin/-/copy-webpack-plugin-11.0.0.tgz", "integrity": "sha1-ltTb219z0C3XLQUo0ZWHIaty4Eo=", "dev": true, "requires": {"fast-glob": "^3.2.11", "glob-parent": "^6.0.1", "globby": "^13.1.1", "normalize-path": "^3.0.0", "schema-utils": "^4.0.0", "serialize-javascript": "^6.0.0"}, "dependencies": {"ajv": {"version": "8.12.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ajv/-/ajv-8.12.0.tgz", "integrity": "sha1-0aBScyPiL1NWLFZ8AJkVd9++GdE=", "dev": true, "requires": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}}, "ajv-keywords": {"version": "5.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ajv-keywords/-/ajv-keywords-5.1.0.tgz", "integrity": "sha1-adTThaRzPNvqtElkoRcKiPh/DhY=", "dev": true, "requires": {"fast-deep-equal": "^3.1.3"}}, "globby": {"version": "13.1.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/globby/-/globby-13.1.3.tgz", "integrity": "sha1-9iuvVyC8ssEzDI1O8iLuEjGFY/8=", "dev": true, "requires": {"dir-glob": "^3.0.1", "fast-glob": "^3.2.11", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^4.0.0"}}, "json-schema-traverse": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "integrity": "sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=", "dev": true}, "schema-utils": {"version": "4.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/schema-utils/-/schema-utils-4.0.0.tgz", "integrity": "sha1-YDMenjrnjsXRY1PEZ8NLOgodPfc=", "dev": true, "requires": {"@types/json-schema": "^7.0.9", "ajv": "^8.8.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.0.0"}}, "serialize-javascript": {"version": "6.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/serialize-javascript/-/serialize-javascript-6.0.1.tgz", "integrity": "sha1-sgbvsnw9oLCra1L0jRcLeZZFjlw=", "dev": true, "requires": {"randombytes": "^2.1.0"}}, "slash": {"version": "4.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/slash/-/slash-4.0.0.tgz", "integrity": "sha1-JCI3IXbExsWt214q2oha+YSzlqc=", "dev": true}}}, "core-js": {"version": "2.6.11", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/core-js/-/core-js-2.6.11.tgz", "integrity": "sha1-9/dfwSYgNHgYNIefRNVcrM+QKcc=", "dev": true}, "core-js-compat": {"version": "3.30.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/core-js-compat/-/core-js-compat-3.30.0.tgz", "integrity": "sha1-maonifbtLev6HfMjJ4QSbul/TYA=", "dev": true, "requires": {"browserslist": "^4.21.5"}, "dependencies": {"browserslist": {"version": "4.21.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/browserslist/-/browserslist-4.21.5.tgz", "integrity": "sha1-dcXa5gBj7mQfl34A7dPPsvt69qc=", "dev": true, "requires": {"caniuse-lite": "^1.0.30001449", "electron-to-chromium": "^1.4.284", "node-releases": "^2.0.8", "update-browserslist-db": "^1.0.10"}}, "caniuse-lite": {"version": "1.0.30001474", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/caniuse-lite/-/caniuse-lite-1.0.30001474.tgz", "integrity": "sha1-E7b+MBqDH+ZmzOjKTviTUjNBM9U=", "dev": true}}}, "core-util-is": {"version": "1.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/core-util-is/-/core-util-is-1.0.3.tgz", "integrity": "sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=", "dev": true}, "cross-spawn": {"version": "7.0.3", "resolved": "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz", "integrity": "sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==", "dev": true, "requires": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}}, "css-select": {"version": "4.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/css-select/-/css-select-4.3.0.tgz", "integrity": "sha1-23EpsoRmYv2GKM/ElquytZ5BUps=", "dev": true, "requires": {"boolbase": "^1.0.0", "css-what": "^6.0.1", "domhandler": "^4.3.1", "domutils": "^2.8.0", "nth-check": "^2.0.1"}}, "css-what": {"version": "6.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/css-what/-/css-what-6.1.0.tgz", "integrity": "sha1-+17/z3bx3eosgb36pN5E55uscPQ=", "dev": true}, "debug": {"version": "4.3.4", "resolved": "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "dev": true, "requires": {"ms": "2.1.2"}}, "deep-is": {"version": "0.1.4", "resolved": "https://registry.npmmirror.com/deep-is/-/deep-is-0.1.4.tgz", "integrity": "sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==", "dev": true}, "deepmerge": {"version": "4.2.2", "resolved": "https://registry.npmmirror.com/deepmerge/-/deepmerge-4.2.2.tgz", "integrity": "sha512-FJ3UgI4gIl+PHZm53knsuSFpE+nESMr7M4v9QcgB7S63Kj/6WqMiFQJpBBYz1Pt+66bZpP3Q7Lye0Oo9MPKEdg==", "dev": true}, "default-gateway": {"version": "6.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/default-gateway/-/default-gateway-6.0.3.tgz", "integrity": "sha1-gZSUyIgFO9t0PtvzQ9bN9/KUOnE=", "dev": true, "requires": {"execa": "^5.0.0"}}, "define-lazy-prop": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz", "integrity": "sha1-P3rkIRKbyqrJvHSQXJigAJ7J7n8=", "dev": true}, "depd": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/depd/-/depd-2.0.0.tgz", "integrity": "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==", "dev": true}, "destroy": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/destroy/-/destroy-1.2.0.tgz", "integrity": "sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==", "dev": true}, "detect-indent": {"version": "4.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/detect-indent/-/detect-indent-4.0.0.tgz", "integrity": "sha1-920GQ1LN9Docts5hnE7jqUdd4gg=", "dev": true, "requires": {"repeating": "^2.0.0"}}, "detect-node": {"version": "2.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/detect-node/-/detect-node-2.1.0.tgz", "integrity": "sha1-yccHdaScPQO8LAbZpzvlUPl4+LE=", "dev": true}, "dir-glob": {"version": "3.0.1", "resolved": "https://registry.npmmirror.com/dir-glob/-/dir-glob-3.0.1.tgz", "integrity": "sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==", "dev": true, "requires": {"path-type": "^4.0.0"}}, "dns-equal": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/dns-equal/-/dns-equal-1.0.0.tgz", "integrity": "sha1-s55/HabrCnW6nBcySzR1PEfgZU0=", "dev": true}, "dns-packet": {"version": "5.4.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/dns-packet/-/dns-packet-5.4.0.tgz", "integrity": "sha1-H4hHfPnyfniiE/ttEYrjjnWah5s=", "dev": true, "requires": {"@leichtgewicht/ip-codec": "^2.0.1"}}, "doctrine": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/doctrine/-/doctrine-3.0.0.tgz", "integrity": "sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==", "dev": true, "requires": {"esutils": "^2.0.2"}}, "dom-converter": {"version": "0.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/dom-converter/-/dom-converter-0.2.0.tgz", "integrity": "sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=", "dev": true, "requires": {"utila": "~0.4"}}, "dom-serializer": {"version": "1.4.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/dom-serializer/-/dom-serializer-1.4.1.tgz", "integrity": "sha1-3l1Bsa6ikCFdxFptrorc8dMuLTA=", "dev": true, "requires": {"domelementtype": "^2.0.1", "domhandler": "^4.2.0", "entities": "^2.0.0"}}, "domelementtype": {"version": "2.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/domelementtype/-/domelementtype-2.3.0.tgz", "integrity": "sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=", "dev": true}, "domhandler": {"version": "4.3.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/domhandler/-/domhandler-4.3.1.tgz", "integrity": "sha1-jXkgM0FvWdaLwDpap7AYwcqJJ5w=", "dev": true, "requires": {"domelementtype": "^2.2.0"}}, "domutils": {"version": "2.8.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/domutils/-/domutils-2.8.0.tgz", "integrity": "sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=", "dev": true, "requires": {"dom-serializer": "^1.0.1", "domelementtype": "^2.2.0", "domhandler": "^4.2.0"}}, "dot-case": {"version": "3.0.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/dot-case/-/dot-case-3.0.4.tgz", "integrity": "sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=", "dev": true, "requires": {"no-case": "^3.0.4", "tslib": "^2.0.3"}, "dependencies": {"tslib": {"version": "2.5.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/tslib/-/tslib-2.5.0.tgz", "integrity": "sha1-Qr/thvV4eutB0DGGbI9AJCng/d8=", "dev": true}}}, "ee-first": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==", "dev": true}, "electron-to-chromium": {"version": "1.4.284", "resolved": "https://registry.npmmirror.com/electron-to-chromium/-/electron-to-chromium-1.4.284.tgz", "integrity": "sha512-M8WEXFuKXMYMVr45fo8mq0wUrrJHheiKZf6BArTKk9ZBYCKJEOU5H8cdWgDT+qCVZf7Na4lVUaZsA+h6uA9+PA==", "dev": true}, "encodeurl": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==", "dev": true}, "enhanced-resolve": {"version": "5.12.0", "resolved": "https://registry.npmmirror.com/enhanced-resolve/-/enhanced-resolve-5.12.0.tgz", "integrity": "sha512-QHTXI/sZQmko1cbDoNAa3mJ5qhWUUNAq3vR0/YiD379fWQrcfuoX1+HW2S0MTt7XmoPLapdaDKUtelUSPic7hQ==", "dev": true, "requires": {"graceful-fs": "^4.2.4", "tapable": "^2.2.0"}}, "entities": {"version": "2.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/entities/-/entities-2.2.0.tgz", "integrity": "sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=", "dev": true}, "envinfo": {"version": "7.8.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/envinfo/-/envinfo-7.8.1.tgz", "integrity": "sha1-Bjd+Pl9NN5/qesWS1a2JJ+DE1HU=", "dev": true}, "es-module-lexer": {"version": "0.9.3", "resolved": "https://registry.npmmirror.com/es-module-lexer/-/es-module-lexer-0.9.3.tgz", "integrity": "sha512-1HQ2M2sPtxwnvOvT1ZClHyQDiggdNjURWpY2we6aMKCQiUVxTmVs2UYPLIrD84sS+kMdUwfBSylbJPwNnBrnHQ==", "dev": true}, "escalade": {"version": "3.1.1", "resolved": "https://registry.npmmirror.com/escalade/-/escalade-3.1.1.tgz", "integrity": "sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==", "dev": true}, "escape-html": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==", "dev": true}, "escape-string-regexp": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "integrity": "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==", "dev": true}, "eslint": {"version": "8.27.0", "resolved": "https://registry.npmmirror.com/eslint/-/eslint-8.27.0.tgz", "integrity": "sha512-0y1bfG2ho7mty+SiILVf9PfuRA49ek4Nc60Wmmu62QlobNR+CeXa4xXIJgcuwSQgZiWaPH+5BDsctpIW0PR/wQ==", "dev": true, "requires": {"@eslint/eslintrc": "^1.3.3", "@humanwhocodes/config-array": "^0.11.6", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "ajv": "^6.10.0", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.1.1", "eslint-utils": "^3.0.0", "eslint-visitor-keys": "^3.3.0", "espree": "^9.4.0", "esquery": "^1.4.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.15.0", "grapheme-splitter": "^1.0.4", "ignore": "^5.2.0", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-sdsl": "^4.1.4", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.1", "regexpp": "^3.2.0", "strip-ansi": "^6.0.1", "strip-json-comments": "^3.1.0", "text-table": "^0.2.0"}, "dependencies": {"brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "minimatch": {"version": "3.1.2", "resolved": "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dev": true, "requires": {"brace-expansion": "^1.1.7"}}}}, "eslint-scope": {"version": "7.1.1", "resolved": "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-7.1.1.tgz", "integrity": "sha512-QKQM/UXpIiHcLqJ5AOyIW7XZmzjkzQXYE54n1++wb0u9V/abW3l9uQnxX8Z5Xd18xyKIMTUAyQ0k1e8pz6LUrw==", "dev": true, "requires": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}}, "eslint-utils": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/eslint-utils/-/eslint-utils-3.0.0.tgz", "integrity": "sha512-uuQC43IGctw68pJA1RgbQS8/NP7rch6Cwd4j3ZBtgo4/8Flj4eGE7ZYSZRN3iq5pVUv6GPdW5Z1RFleo84uLDA==", "dev": true, "requires": {"eslint-visitor-keys": "^2.0.0"}, "dependencies": {"eslint-visitor-keys": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz", "integrity": "sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==", "dev": true}}}, "eslint-visitor-keys": {"version": "3.3.0", "resolved": "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.3.0.tgz", "integrity": "sha512-mQ+suqKJVyeuwGYHAdjMFqjCyfl8+Ldnxuyp3ldiMBFKkvytrXUZWaiPCEav8qDHKty44bD+qV1IP4T+w+xXRA==", "dev": true}, "espree": {"version": "9.4.1", "resolved": "https://registry.npmmirror.com/espree/-/espree-9.4.1.tgz", "integrity": "sha512-XwctdmTO6SIvCzd9810yyNzIrOrqNYV9Koizx4C/mRhf9uq0o4yHoCEU/670pOxOL/MSraektvSAji79kX90Vg==", "dev": true, "requires": {"acorn": "^8.8.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.3.0"}}, "esquery": {"version": "1.4.0", "resolved": "https://registry.npmmirror.com/esquery/-/esquery-1.4.0.tgz", "integrity": "sha512-cCDispWt5vHHtwMY2YrAQ4ibFkAL8RbH5YGBnZBc90MolvvfkkQcJro/aZiAQUlQ3qgrYS6D6v8Gc5G5CQsc9w==", "dev": true, "requires": {"estraverse": "^5.1.0"}}, "esrecurse": {"version": "4.3.0", "resolved": "https://registry.npmmirror.com/esrecurse/-/esrecurse-4.3.0.tgz", "integrity": "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==", "dev": true, "requires": {"estraverse": "^5.2.0"}}, "estraverse": {"version": "5.3.0", "resolved": "https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz", "integrity": "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==", "dev": true}, "estree-walker": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz", "integrity": "sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==", "dev": true}, "esutils": {"version": "2.0.3", "resolved": "https://registry.npmmirror.com/esutils/-/esutils-2.0.3.tgz", "integrity": "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==", "dev": true}, "etag": {"version": "1.8.1", "resolved": "https://registry.npmmirror.com/etag/-/etag-1.8.1.tgz", "integrity": "sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==", "dev": true}, "eventemitter3": {"version": "4.0.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/eventemitter3/-/eventemitter3-4.0.7.tgz", "integrity": "sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=", "dev": true}, "events": {"version": "3.3.0", "resolved": "https://registry.npmmirror.com/events/-/events-3.3.0.tgz", "integrity": "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==", "dev": true}, "execa": {"version": "5.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/execa/-/execa-5.1.1.tgz", "integrity": "sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=", "dev": true, "requires": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}}, "express": {"version": "4.18.2", "resolved": "https://registry.npmmirror.com/express/-/express-4.18.2.tgz", "integrity": "sha512-5/PsL6iGPdfQ/lKM1UuielYgv3BUoJfz1aUwU9vHZ+J7gyvwdQXFEBIEIaxeGf0GIcreATNyBExtalisDbuMqQ==", "dev": true, "requires": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.1", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.5.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.2.0", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.7", "qs": "6.11.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.18.0", "serve-static": "1.15.0", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==", "dev": true}}}, "fast-deep-equal": {"version": "3.1.3", "resolved": "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==", "dev": true}, "fast-glob": {"version": "3.2.12", "resolved": "https://registry.npmmirror.com/fast-glob/-/fast-glob-3.2.12.tgz", "integrity": "sha512-DVj4CQIYYow0BlaelwK1pHl5n5cRSJfM60UA0zK891sVInoPri2Ekj7+e1CT3/3qxXenpI+nBBmQAcJPJgaj4w==", "dev": true, "requires": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "dependencies": {"glob-parent": {"version": "5.1.2", "resolved": "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==", "dev": true, "requires": {"is-glob": "^4.0.1"}}}}, "fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==", "dev": true}, "fast-levenshtein": {"version": "2.0.6", "resolved": "https://registry.npmmirror.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==", "dev": true}, "fastest-levenshtein": {"version": "1.0.16", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/fastest-levenshtein/-/fastest-levenshtein-1.0.16.tgz", "integrity": "sha1-IQ5htv8YHekeqbPRuE/e3UfgNOU=", "dev": true}, "fastq": {"version": "1.13.0", "resolved": "https://registry.npmmirror.com/fastq/-/fastq-1.13.0.tgz", "integrity": "sha512-YpkpUnK8od0o1hmeSc7UUs/eB/vIPWJYjKck2QKIzAf71Vm1AAQ3EbuZB3g2JIy+pg+ERD0vqI79KyZiB2e2Nw==", "dev": true, "requires": {"reusify": "^1.0.4"}}, "faye-websocket": {"version": "0.11.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/faye-websocket/-/faye-websocket-0.11.4.tgz", "integrity": "sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo=", "dev": true, "requires": {"websocket-driver": ">=0.5.1"}}, "file-entry-cache": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/file-entry-cache/-/file-entry-cache-6.0.1.tgz", "integrity": "sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==", "dev": true, "requires": {"flat-cache": "^3.0.4"}}, "fill-range": {"version": "7.0.1", "resolved": "https://registry.npmmirror.com/fill-range/-/fill-range-7.0.1.tgz", "integrity": "sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==", "dev": true, "requires": {"to-regex-range": "^5.0.1"}}, "finalhandler": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/finalhandler/-/finalhandler-1.2.0.tgz", "integrity": "sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==", "dev": true, "requires": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==", "dev": true}}}, "find-up": {"version": "5.0.0", "resolved": "https://registry.npmmirror.com/find-up/-/find-up-5.0.0.tgz", "integrity": "sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==", "dev": true, "requires": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}}, "flat-cache": {"version": "3.0.4", "resolved": "https://registry.npmmirror.com/flat-cache/-/flat-cache-3.0.4.tgz", "integrity": "sha512-dm9s5Pw7Jc0GvMYbshN6zchCA9RgQlzzEZX3vylR9IqFfS8XciblUXOKfW6SiuJ0e13eDYZoZV5wdrev7P3Nwg==", "dev": true, "requires": {"flatted": "^3.1.0", "rimraf": "^3.0.2"}, "dependencies": {"brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "glob": {"version": "7.2.3", "resolved": "https://registry.npmmirror.com/glob/-/glob-7.2.3.tgz", "integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "minimatch": {"version": "3.1.2", "resolved": "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dev": true, "requires": {"brace-expansion": "^1.1.7"}}, "rimraf": {"version": "3.0.2", "resolved": "https://registry.npmmirror.com/rimraf/-/rimraf-3.0.2.tgz", "integrity": "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==", "dev": true, "requires": {"glob": "^7.1.3"}}}}, "flatted": {"version": "3.2.7", "resolved": "https://registry.npmmirror.com/flatted/-/flatted-3.2.7.tgz", "integrity": "sha512-5nqDSxl8nn5BSNxyR3n4I6eDmbolI6WT+QqR547RwxQapgjQBmtktdP+HTBb/a/zLsbzERTONyUB5pefh5TtjQ==", "dev": true}, "follow-redirects": {"version": "1.15.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/follow-redirects/-/follow-redirects-1.15.2.tgz", "integrity": "sha1-tGCGQUS6Y/JoEJbydMTlcCbaLBM=", "dev": true}, "forwarded": {"version": "0.2.0", "resolved": "https://registry.npmmirror.com/forwarded/-/forwarded-0.2.0.tgz", "integrity": "sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==", "dev": true}, "fresh": {"version": "0.5.2", "resolved": "https://registry.npmmirror.com/fresh/-/fresh-0.5.2.tgz", "integrity": "sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==", "dev": true}, "fs-monkey": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/fs-monkey/-/fs-monkey-1.0.3.tgz", "integrity": "sha512-cybjIfiiE+pTWicSCLFHSrXZ6EilF30oh91FDP9S2B051prEa7QWfrVTQm10/dDpswBDXZugPa1Ogu8Yh+HV0Q==", "dev": true}, "fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==", "dev": true}, "fsevents": {"version": "2.3.2", "resolved": "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.2.tgz", "integrity": "sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==", "dev": true, "optional": true}, "function-bind": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.1.tgz", "integrity": "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==", "dev": true}, "gensync": {"version": "1.0.0-beta.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=", "dev": true}, "get-intrinsic": {"version": "1.1.3", "resolved": "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.1.3.tgz", "integrity": "sha512-QJVz1Tj7MS099PevUG5jvnt9tSkXN8K14dxQlikJuPt4uD9hHAHjLyLBiLR5zELelBdD9QNRAXZzsJx0WaDL9A==", "dev": true, "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.3"}}, "get-stream": {"version": "6.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/get-stream/-/get-stream-6.0.1.tgz", "integrity": "sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=", "dev": true}, "glob": {"version": "8.0.3", "resolved": "https://registry.npmmirror.com/glob/-/glob-8.0.3.tgz", "integrity": "sha512-ull455NHSHI/Y1FqGaaYFaLGkNMMJbavMrEGFXG/PGrg6y7sutWHUHrz6gy6WEBH6akM1M414dWKCNs+IhKdiQ==", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^5.0.1", "once": "^1.3.0"}}, "glob-parent": {"version": "6.0.2", "resolved": "https://registry.npmmirror.com/glob-parent/-/glob-parent-6.0.2.tgz", "integrity": "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==", "dev": true, "requires": {"is-glob": "^4.0.3"}}, "glob-to-regexp": {"version": "0.4.1", "resolved": "https://registry.npmmirror.com/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz", "integrity": "sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==", "dev": true}, "globals": {"version": "13.17.0", "resolved": "https://registry.npmmirror.com/globals/-/globals-13.17.0.tgz", "integrity": "sha512-1C+6nQRb1GwGMKm2dH/E7enFAMxGTmGI7/dEdhy/DNelv85w9B72t3uc5frtMNXIbzrarJJ/lTCjcaZwbLJmyw==", "dev": true, "requires": {"type-fest": "^0.20.2"}}, "globby": {"version": "11.1.0", "resolved": "https://registry.npmmirror.com/globby/-/globby-11.1.0.tgz", "integrity": "sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==", "dev": true, "requires": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}}, "graceful-fs": {"version": "4.2.10", "resolved": "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.10.tgz", "integrity": "sha512-9ByhssR2fPVsNZj478qUUbKfmL0+t5BDVyjShtyZZLiK7ZDAArFFfopyOTj0M05wE2tJPisA4iTnnXl2YoPvOA==", "dev": true}, "grapheme-splitter": {"version": "1.0.4", "resolved": "https://registry.npmmirror.com/grapheme-splitter/-/grapheme-splitter-1.0.4.tgz", "integrity": "sha512-bzh50DW9kTPM00T8y4o8vQg89Di9oLJVLW/KaOGIXJWP/iqCN6WKYkbNOF04vFLJhwcpYUh9ydh/+5vpOqV4YQ==", "dev": true}, "handle-thing": {"version": "2.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/handle-thing/-/handle-thing-2.0.1.tgz", "integrity": "sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=", "dev": true}, "has": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/has/-/has-1.0.3.tgz", "integrity": "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==", "dev": true, "requires": {"function-bind": "^1.1.1"}}, "has-ansi": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/has-ansi/-/has-ansi-2.0.0.tgz", "integrity": "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=", "dev": true, "requires": {"ansi-regex": "^2.0.0"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true}}}, "has-flag": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "dev": true}, "has-symbols": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==", "dev": true}, "he": {"version": "1.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/he/-/he-1.2.0.tgz", "integrity": "sha1-hK5l+n6vsWX922FWauFLrwVmTw8=", "dev": true}, "home-or-tmp": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/home-or-tmp/-/home-or-tmp-2.0.0.tgz", "integrity": "sha1-42w/LSyufXRqhX440Y1fMqeILbg=", "dev": true, "requires": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.1"}}, "hpack.js": {"version": "2.1.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/hpack.js/-/hpack.js-2.1.6.tgz", "integrity": "sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=", "dev": true, "requires": {"inherits": "^2.0.1", "obuf": "^1.0.0", "readable-stream": "^2.0.1", "wbuf": "^1.1.0"}, "dependencies": {"readable-stream": {"version": "2.3.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/readable-stream/-/readable-stream-2.3.8.tgz", "integrity": "sha1-kRJegEK7obmIf0k0X2J3Anzovps=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "safe-buffer": {"version": "5.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "dev": true}}}, "html-entities": {"version": "2.3.3", "resolved": "https://registry.npmmirror.com/html-entities/-/html-entities-2.3.3.tgz", "integrity": "sha512-DV5Ln36z34NNTDgnz0EWGBLZENelNAtkiFA4kyNOG2tDI6Mz1uSWiq1wAKdyjnJwyDiDO7Fa2SO1CTxPXL8VxA==", "dev": true}, "html-minifier-terser": {"version": "6.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz", "integrity": "sha1-v8gYk0zAeRj2s2afV3Ts39SPMqs=", "dev": true, "requires": {"camel-case": "^4.1.2", "clean-css": "^5.2.2", "commander": "^8.3.0", "he": "^1.2.0", "param-case": "^3.0.4", "relateurl": "^0.2.7", "terser": "^5.10.0"}, "dependencies": {"commander": {"version": "8.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/commander/-/commander-8.3.0.tgz", "integrity": "sha1-SDfqGy2me5xhamevuw+v7lZ7ymY=", "dev": true}}}, "html-webpack-plugin": {"version": "5.5.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/html-webpack-plugin/-/html-webpack-plugin-5.5.0.tgz", "integrity": "sha1-w5EZNvV2gcH59Ni2jBWM2d/lL1A=", "dev": true, "requires": {"@types/html-minifier-terser": "^6.0.0", "html-minifier-terser": "^6.0.2", "lodash": "^4.17.21", "pretty-error": "^4.0.0", "tapable": "^2.0.0"}}, "htmlparser2": {"version": "6.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/htmlparser2/-/htmlparser2-6.1.0.tgz", "integrity": "sha1-xNditsM3GgXb5l6UrkOp+EX7j7c=", "dev": true, "requires": {"domelementtype": "^2.0.1", "domhandler": "^4.0.0", "domutils": "^2.5.2", "entities": "^2.0.0"}}, "http-deceiver": {"version": "1.2.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/http-deceiver/-/http-deceiver-1.2.7.tgz", "integrity": "sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=", "dev": true}, "http-errors": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/http-errors/-/http-errors-2.0.0.tgz", "integrity": "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==", "dev": true, "requires": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}}, "http-parser-js": {"version": "0.5.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/http-parser-js/-/http-parser-js-0.5.8.tgz", "integrity": "sha1-ryMJDZrE4kVz3m9q7MnYSki/IOM=", "dev": true}, "http-proxy": {"version": "1.18.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/http-proxy/-/http-proxy-1.18.1.tgz", "integrity": "sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=", "dev": true, "requires": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}}, "http-proxy-middleware": {"version": "2.0.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/http-proxy-middleware/-/http-proxy-middleware-2.0.6.tgz", "integrity": "sha1-4aTdaXlXLHq1pOS1UJXR8yp0lj8=", "dev": true, "requires": {"@types/http-proxy": "^1.17.8", "http-proxy": "^1.18.1", "is-glob": "^4.0.1", "is-plain-obj": "^3.0.0", "micromatch": "^4.0.2"}}, "human-signals": {"version": "2.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/human-signals/-/human-signals-2.1.0.tgz", "integrity": "sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=", "dev": true}, "iconv-lite": {"version": "0.4.24", "resolved": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "dev": true, "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "ignore": {"version": "5.2.0", "resolved": "https://registry.npmmirror.com/ignore/-/ignore-5.2.0.tgz", "integrity": "sha512-CmxgYGiEPCLhfLnpPp1MoRmifwEIOgjcHXxOBjv7mY96c+eWScsOP9c112ZyLdWHi0FxHjI+4uVhKYp/gcdRmQ==", "dev": true}, "import-fresh": {"version": "3.3.0", "resolved": "https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.0.tgz", "integrity": "sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==", "dev": true, "requires": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}}, "import-local": {"version": "3.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/import-local/-/import-local-3.1.0.tgz", "integrity": "sha1-tEed+KX9RPbNziQHBnVnYGPJXLQ=", "dev": true, "requires": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}}, "imurmurhash": {"version": "0.1.4", "resolved": "https://registry.npmmirror.com/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==", "dev": true}, "inflight": {"version": "1.0.6", "resolved": "https://registry.npmmirror.com/inflight/-/inflight-1.0.6.tgz", "integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==", "dev": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "resolved": "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==", "dev": true}, "interpret": {"version": "3.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/interpret/-/interpret-3.1.1.tgz", "integrity": "sha1-W+DO7WfKecbEvFzw1+6EPc6hEMQ=", "dev": true}, "invariant": {"version": "2.2.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/invariant/-/invariant-2.2.4.tgz", "integrity": "sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=", "dev": true, "requires": {"loose-envify": "^1.0.0"}}, "ipaddr.js": {"version": "1.9.1", "resolved": "https://registry.npmmirror.com/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==", "dev": true}, "is-binary-path": {"version": "2.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-binary-path/-/is-binary-path-2.1.0.tgz", "integrity": "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=", "dev": true, "requires": {"binary-extensions": "^2.0.0"}}, "is-builtin-module": {"version": "3.2.0", "resolved": "https://registry.npmmirror.com/is-builtin-module/-/is-builtin-module-3.2.0.tgz", "integrity": "sha512-phDA4oSGt7vl1n5tJvTWooWWAsXLY+2xCnxNqvKhGEzujg+A43wPlPOyDg3C8XQHN+6k/JTQWJ/j0dQh/qr+Hw==", "dev": true, "requires": {"builtin-modules": "^3.3.0"}}, "is-core-module": {"version": "2.11.0", "resolved": "https://registry.npmmirror.com/is-core-module/-/is-core-module-2.11.0.tgz", "integrity": "sha512-RRjxlvLDkD1YJwDbroBHMb+cukurkDWNyHx7D3oNB5x9rb5ogcksMC5wHCadcXoo67gVr/+3GFySh3134zi6rw==", "dev": true, "requires": {"has": "^1.0.3"}}, "is-docker": {"version": "2.2.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-docker/-/is-docker-2.2.1.tgz", "integrity": "sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=", "dev": true}, "is-extglob": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==", "dev": true}, "is-finite": {"version": "1.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-finite/-/is-finite-1.1.0.tgz", "integrity": "sha1-kEE1x3+0LAZB1qobzbxNqo2ggvM=", "dev": true}, "is-glob": {"version": "4.0.3", "resolved": "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "dev": true, "requires": {"is-extglob": "^2.1.1"}}, "is-module": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/is-module/-/is-module-1.0.0.tgz", "integrity": "sha512-51ypPSPCoTEIN9dy5Oy+h4pShgJmPCygKfyRCISBI+JoWT/2oJvK8QPxmwv7b/p239jXrm9M1mlQbyKJ5A152g==", "dev": true}, "is-number": {"version": "7.0.0", "resolved": "https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz", "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==", "dev": true}, "is-path-inside": {"version": "3.0.3", "resolved": "https://registry.npmmirror.com/is-path-inside/-/is-path-inside-3.0.3.tgz", "integrity": "sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==", "dev": true}, "is-plain-obj": {"version": "3.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-plain-obj/-/is-plain-obj-3.0.0.tgz", "integrity": "sha1-r28uoUrFpkYYOlu9tbqrvBVq2dc=", "dev": true}, "is-plain-object": {"version": "2.0.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-plain-object/-/is-plain-object-2.0.4.tgz", "integrity": "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=", "dev": true, "requires": {"isobject": "^3.0.1"}}, "is-reference": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/is-reference/-/is-reference-1.2.1.tgz", "integrity": "sha512-U82MsXXiFIrjCK4otLT+o2NA2Cd2g5MLoOVXUZjIOhLurrRxpEXzI8O0KZHr3IjLvlAH1kTPYSuqer5T9ZVBKQ==", "dev": true, "requires": {"@types/estree": "*"}}, "is-stream": {"version": "2.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-stream/-/is-stream-2.0.1.tgz", "integrity": "sha1-+sHj1TuXrVqdCunO8jifWBClwHc=", "dev": true}, "is-wsl": {"version": "2.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-wsl/-/is-wsl-2.2.0.tgz", "integrity": "sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=", "dev": true, "requires": {"is-docker": "^2.0.0"}}, "isarray": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "isexe": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/isexe/-/isexe-2.0.0.tgz", "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==", "dev": true}, "isobject": {"version": "3.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/isobject/-/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8=", "dev": true}, "jest-worker": {"version": "26.6.2", "resolved": "https://registry.npmjs.org/jest-worker/-/jest-worker-26.6.2.tgz", "integrity": "sha512-KWYVV1c4i+jbMpaBC+U++4Va0cp8OisU185o73T1vo99hqi7w8tSJfUXYswwqqrjzwxa6KpRK54WhPvwf5w6PQ==", "dev": true, "requires": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^7.0.0"}}, "js-sdsl": {"version": "4.1.5", "resolved": "https://registry.npmmirror.com/js-sdsl/-/js-sdsl-4.1.5.tgz", "integrity": "sha512-08bOAKweV2NUC1wqTtf3qZlnpOX/R2DU9ikpjOHs0H+ibQv3zpncVQg6um4uYtRtrwIX8M4Nh3ytK4HGlYAq7Q==", "dev": true}, "js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==", "dev": true}, "js-yaml": {"version": "4.1.0", "resolved": "https://registry.npmmirror.com/js-yaml/-/js-yaml-4.1.0.tgz", "integrity": "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==", "dev": true, "requires": {"argparse": "^2.0.1"}}, "jsesc": {"version": "1.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/jsesc/-/jsesc-1.3.0.tgz", "integrity": "sha1-RsP+yMGJKxKwgz25vHYiF226s0s=", "dev": true}, "json-parse-even-better-errors": {"version": "2.3.1", "resolved": "https://registry.npmmirror.com/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==", "dev": true}, "json-schema-traverse": {"version": "0.4.1", "resolved": "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==", "dev": true}, "json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==", "dev": true}, "json5": {"version": "0.5.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/json5/-/json5-0.5.1.tgz", "integrity": "sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=", "dev": true}, "kind-of": {"version": "6.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/kind-of/-/kind-of-6.0.3.tgz", "integrity": "sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=", "dev": true}, "levn": {"version": "0.4.1", "resolved": "https://registry.npmmirror.com/levn/-/levn-0.4.1.tgz", "integrity": "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==", "dev": true, "requires": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}}, "loader-runner": {"version": "4.3.0", "resolved": "https://registry.npmmirror.com/loader-runner/-/loader-runner-4.3.0.tgz", "integrity": "sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==", "dev": true}, "locate-path": {"version": "6.0.0", "resolved": "https://registry.npmmirror.com/locate-path/-/locate-path-6.0.0.tgz", "integrity": "sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==", "dev": true, "requires": {"p-locate": "^5.0.0"}}, "lodash": {"version": "4.17.21", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/lodash/-/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=", "dev": true}, "lodash.debounce": {"version": "4.0.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/lodash.debounce/-/lodash.debounce-4.0.8.tgz", "integrity": "sha1-gteb/zCmfEAF/9XiUVMArZyk168=", "dev": true}, "lodash.merge": {"version": "4.6.2", "resolved": "https://registry.npmmirror.com/lodash.merge/-/lodash.merge-4.6.2.tgz", "integrity": "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==", "dev": true}, "loose-envify": {"version": "1.4.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/loose-envify/-/loose-envify-1.4.0.tgz", "integrity": "sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=", "dev": true, "requires": {"js-tokens": "^3.0.0 || ^4.0.0"}}, "lower-case": {"version": "2.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/lower-case/-/lower-case-2.0.2.tgz", "integrity": "sha1-b6I3xj29xKgsoP2ILkci3F5jTig=", "dev": true, "requires": {"tslib": "^2.0.3"}, "dependencies": {"tslib": {"version": "2.5.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/tslib/-/tslib-2.5.0.tgz", "integrity": "sha1-Qr/thvV4eutB0DGGbI9AJCng/d8=", "dev": true}}}, "lru-cache": {"version": "6.0.0", "resolved": "https://registry.npmmirror.com/lru-cache/-/lru-cache-6.0.0.tgz", "integrity": "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==", "dev": true, "requires": {"yallist": "^4.0.0"}}, "magic-string": {"version": "0.26.7", "resolved": "https://registry.npmmirror.com/magic-string/-/magic-string-0.26.7.tgz", "integrity": "sha512-hX9XH3ziStPoPhJxLq1syWuZMxbDvGNbVchfrdCtanC7D13888bMFow61x8axrx+GfHLtVeAx2kxL7tTGRl+Ow==", "dev": true, "requires": {"sourcemap-codec": "^1.4.8"}}, "media-typer": {"version": "0.3.0", "resolved": "https://registry.npmmirror.com/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==", "dev": true}, "memfs": {"version": "3.4.12", "resolved": "https://registry.npmmirror.com/memfs/-/memfs-3.4.12.tgz", "integrity": "sha512-BcjuQn6vfqP+k100e0E9m61Hyqa//Brp+I3f0OBmN0ATHlFA8vx3Lt8z57R3u2bPqe3WGDBC+nF72fTH7isyEw==", "dev": true, "requires": {"fs-monkey": "^1.0.3"}}, "merge-descriptors": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "integrity": "sha512-cCi6g3/Zr1iqQi6ySbseM1Xvooa98N0w31jzUYrXPX2xqObmFGHJ0tQ5u74H3mVh7wLouTseZyYIq39g8cNp1w==", "dev": true}, "merge-stream": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz", "integrity": "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==", "dev": true}, "merge2": {"version": "1.4.1", "resolved": "https://registry.npmmirror.com/merge2/-/merge2-1.4.1.tgz", "integrity": "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==", "dev": true}, "methods": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/methods/-/methods-1.1.2.tgz", "integrity": "sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==", "dev": true}, "micromatch": {"version": "4.0.5", "resolved": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.5.tgz", "integrity": "sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==", "dev": true, "requires": {"braces": "^3.0.2", "picomatch": "^2.3.1"}}, "mime": {"version": "1.6.0", "resolved": "https://registry.npmmirror.com/mime/-/mime-1.6.0.tgz", "integrity": "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==", "dev": true}, "mime-db": {"version": "1.52.0", "resolved": "https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "dev": true}, "mime-types": {"version": "2.1.35", "resolved": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "dev": true, "requires": {"mime-db": "1.52.0"}}, "mimic-fn": {"version": "2.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/mimic-fn/-/mimic-fn-2.1.0.tgz", "integrity": "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=", "dev": true}, "minimalistic-assert": {"version": "1.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz", "integrity": "sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=", "dev": true}, "minimatch": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-5.1.1.tgz", "integrity": "sha512-362NP+zlprccbEt/SkxKfRMHnNY85V74mVnpUpNyr3F35covl09Kec7/sEFLt3RA4oXmewtoaanoIf67SE5Y5g==", "dev": true, "requires": {"brace-expansion": "^2.0.1"}}, "minimist": {"version": "1.2.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/minimist/-/minimist-1.2.8.tgz", "integrity": "sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=", "dev": true}, "mkdirp": {"version": "0.5.6", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/mkdirp/-/mkdirp-0.5.6.tgz", "integrity": "sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=", "dev": true, "requires": {"minimist": "^1.2.6"}}, "ms": {"version": "2.1.2", "resolved": "https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==", "dev": true}, "multicast-dns": {"version": "7.2.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/multicast-dns/-/multicast-dns-7.2.5.tgz", "integrity": "sha1-d+tGBX9NetvRbZKQ+nKZ9vpkzO0=", "dev": true, "requires": {"dns-packet": "^5.2.2", "thunky": "^1.0.2"}}, "natural-compare": {"version": "1.4.0", "resolved": "https://registry.npmmirror.com/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==", "dev": true}, "natural-compare-lite": {"version": "1.4.0", "resolved": "https://registry.npmmirror.com/natural-compare-lite/-/natural-compare-lite-1.4.0.tgz", "integrity": "sha512-Tj+HTDSJJKaZnfiuw+iaF9skdPpTo2GtEly5JHnWV/hfv2Qj/9RKsGISQtLh2ox3l5EAGw487hnBee0sIJ6v2g==", "dev": true}, "negotiator": {"version": "0.6.3", "resolved": "https://registry.npmmirror.com/negotiator/-/negotiator-0.6.3.tgz", "integrity": "sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==", "dev": true}, "neo-async": {"version": "2.6.2", "resolved": "https://registry.npmmirror.com/neo-async/-/neo-async-2.6.2.tgz", "integrity": "sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==", "dev": true}, "no-case": {"version": "3.0.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/no-case/-/no-case-3.0.4.tgz", "integrity": "sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=", "dev": true, "requires": {"lower-case": "^2.0.2", "tslib": "^2.0.3"}, "dependencies": {"tslib": {"version": "2.5.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/tslib/-/tslib-2.5.0.tgz", "integrity": "sha1-Qr/thvV4eutB0DGGbI9AJCng/d8=", "dev": true}}}, "node-forge": {"version": "1.3.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/node-forge/-/node-forge-1.3.1.tgz", "integrity": "sha1-vo2iryQ7JBfV9kancGY6krfp3tM=", "dev": true}, "node-releases": {"version": "2.0.8", "resolved": "https://registry.npmmirror.com/node-releases/-/node-releases-2.0.8.tgz", "integrity": "sha512-dFSmB8fFHEH/s81Xi+Y/15DQY6VHW81nXRj86EMSL3lmuTmK1e+aT4wrFCkTbm+gSwkw4KpX+rT/pMM2c1mF+A==", "dev": true}, "normalize-path": {"version": "3.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/normalize-path/-/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "dev": true}, "npm-run-path": {"version": "4.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/npm-run-path/-/npm-run-path-4.0.1.tgz", "integrity": "sha1-t+zR5e1T2o43pV4cImnguX7XSOo=", "dev": true, "requires": {"path-key": "^3.0.0"}}, "nth-check": {"version": "2.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/nth-check/-/nth-check-2.1.1.tgz", "integrity": "sha1-yeq0KO/842zWuSySS9sADvHx7R0=", "dev": true, "requires": {"boolbase": "^1.0.0"}}, "object-inspect": {"version": "1.12.2", "resolved": "https://registry.npmmirror.com/object-inspect/-/object-inspect-1.12.2.tgz", "integrity": "sha512-z+cPxW0QGUp0mcqcsgQyLVRDoXFQbXOwBaqyF7VIgI4TWNQsDHrBpUQslRmIfAoYWdYzs6UlKJtB2XJpTaNSpQ==", "dev": true}, "obuf": {"version": "1.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/obuf/-/obuf-1.1.2.tgz", "integrity": "sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=", "dev": true}, "on-finished": {"version": "2.4.1", "resolved": "https://registry.npmmirror.com/on-finished/-/on-finished-2.4.1.tgz", "integrity": "sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==", "dev": true, "requires": {"ee-first": "1.1.1"}}, "on-headers": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/on-headers/-/on-headers-1.0.2.tgz", "integrity": "sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=", "dev": true}, "once": {"version": "1.4.0", "resolved": "https://registry.npmmirror.com/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "dev": true, "requires": {"wrappy": "1"}}, "onetime": {"version": "5.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/onetime/-/onetime-5.1.2.tgz", "integrity": "sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=", "dev": true, "requires": {"mimic-fn": "^2.1.0"}}, "open": {"version": "8.4.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/open/-/open-8.4.2.tgz", "integrity": "sha1-W1/+Ko95Pc0qrXPlUMuHtZywhPk=", "dev": true, "requires": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}}, "optionator": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/optionator/-/optionator-0.9.1.tgz", "integrity": "sha512-74RlY5FCnhq4jRxVUPKDaRwrVNXMqsGsiW6AJw4XK8hmtm10wC0ypZBLw5IIp85NZMr91+qd1RvvENwg7jjRFw==", "dev": true, "requires": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.3"}}, "os-homedir": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/os-homedir/-/os-homedir-1.0.2.tgz", "integrity": "sha1-/7xJiDNuDoM94MFox+8VISGqf7M=", "dev": true}, "os-tmpdir": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "integrity": "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=", "dev": true}, "p-limit": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/p-limit/-/p-limit-3.1.0.tgz", "integrity": "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==", "dev": true, "requires": {"yocto-queue": "^0.1.0"}}, "p-locate": {"version": "5.0.0", "resolved": "https://registry.npmmirror.com/p-locate/-/p-locate-5.0.0.tgz", "integrity": "sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==", "dev": true, "requires": {"p-limit": "^3.0.2"}}, "p-retry": {"version": "4.6.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/p-retry/-/p-retry-4.6.2.tgz", "integrity": "sha1-m6rnGEBX7dThcjHO4EJkEG4JKhY=", "dev": true, "requires": {"@types/retry": "0.12.0", "retry": "^0.13.1"}}, "p-try": {"version": "2.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/p-try/-/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "dev": true}, "param-case": {"version": "3.0.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/param-case/-/param-case-3.0.4.tgz", "integrity": "sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU=", "dev": true, "requires": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}, "dependencies": {"tslib": {"version": "2.5.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/tslib/-/tslib-2.5.0.tgz", "integrity": "sha1-Qr/thvV4eutB0DGGbI9AJCng/d8=", "dev": true}}}, "parent-module": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/parent-module/-/parent-module-1.0.1.tgz", "integrity": "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==", "dev": true, "requires": {"callsites": "^3.0.0"}}, "parseurl": {"version": "1.3.3", "resolved": "https://registry.npmmirror.com/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==", "dev": true}, "pascal-case": {"version": "3.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/pascal-case/-/pascal-case-3.1.2.tgz", "integrity": "sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs=", "dev": true, "requires": {"no-case": "^3.0.4", "tslib": "^2.0.3"}, "dependencies": {"tslib": {"version": "2.5.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/tslib/-/tslib-2.5.0.tgz", "integrity": "sha1-Qr/thvV4eutB0DGGbI9AJCng/d8=", "dev": true}}}, "path-exists": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==", "dev": true}, "path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==", "dev": true}, "path-key": {"version": "3.1.1", "resolved": "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz", "integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==", "dev": true}, "path-parse": {"version": "1.0.7", "resolved": "https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==", "dev": true}, "path-to-regexp": {"version": "0.1.7", "resolved": "https://registry.npmmirror.com/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "integrity": "sha512-5DFkuoqlv1uYQKxy8omFBeJPQcdoE07Kv2sferDCrAq1ohOU+MSDswDIbnx3YAM60qIOnYa53wBhXW0EbMonrQ==", "dev": true}, "path-type": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/path-type/-/path-type-4.0.0.tgz", "integrity": "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==", "dev": true}, "picocolors": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/picocolors/-/picocolors-1.0.0.tgz", "integrity": "sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==", "dev": true}, "picomatch": {"version": "2.3.1", "resolved": "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "dev": true}, "pkg-dir": {"version": "4.2.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/pkg-dir/-/pkg-dir-4.2.0.tgz", "integrity": "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=", "dev": true, "requires": {"find-up": "^4.0.0"}, "dependencies": {"find-up": {"version": "4.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/find-up/-/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "dev": true, "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}, "locate-path": {"version": "5.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "dev": true, "requires": {"p-locate": "^4.1.0"}}, "p-limit": {"version": "2.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "4.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "dev": true, "requires": {"p-limit": "^2.2.0"}}}}, "prelude-ls": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/prelude-ls/-/prelude-ls-1.2.1.tgz", "integrity": "sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==", "dev": true}, "pretty-error": {"version": "4.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/pretty-error/-/pretty-error-4.0.0.tgz", "integrity": "sha1-kKcD9G3XI0rbRtD4SCPp0cuPENY=", "dev": true, "requires": {"lodash": "^4.17.20", "renderkid": "^3.0.0"}}, "private": {"version": "0.1.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/private/-/private-0.1.8.tgz", "integrity": "sha1-I4Hts2ifelPWUxkAYPz4ItLzaP8=", "dev": true}, "process-nextick-args": {"version": "2.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha1-eCDZsWEgzFXKmud5JoCufbptf+I=", "dev": true}, "proxy-addr": {"version": "2.0.7", "resolved": "https://registry.npmmirror.com/proxy-addr/-/proxy-addr-2.0.7.tgz", "integrity": "sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==", "dev": true, "requires": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}}, "punycode": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/punycode/-/punycode-2.1.1.tgz", "integrity": "sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A==", "dev": true}, "qs": {"version": "6.11.0", "resolved": "https://registry.npmmirror.com/qs/-/qs-6.11.0.tgz", "integrity": "sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==", "dev": true, "requires": {"side-channel": "^1.0.4"}}, "queue-microtask": {"version": "1.2.3", "resolved": "https://registry.npmmirror.com/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==", "dev": true}, "randombytes": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz", "integrity": "sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==", "dev": true, "requires": {"safe-buffer": "^5.1.0"}}, "range-parser": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==", "dev": true}, "raw-body": {"version": "2.5.1", "resolved": "https://registry.npmmirror.com/raw-body/-/raw-body-2.5.1.tgz", "integrity": "sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==", "dev": true, "requires": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}}, "readable-stream": {"version": "3.6.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/readable-stream/-/readable-stream-3.6.1.tgz", "integrity": "sha1-+fm19TaSAlOz0m52YOfaTM/5u2I=", "dev": true, "requires": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}}, "readdirp": {"version": "3.6.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/readdirp/-/readdirp-3.6.0.tgz", "integrity": "sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=", "dev": true, "requires": {"picomatch": "^2.2.1"}}, "rechoir": {"version": "0.8.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/rechoir/-/rechoir-0.8.0.tgz", "integrity": "sha1-Sfhm4NMhRhQto62PDv81KzIV/yI=", "dev": true, "requires": {"resolve": "^1.20.0"}}, "regenerate": {"version": "1.4.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/regenerate/-/regenerate-1.4.2.tgz", "integrity": "sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=", "dev": true}, "regenerate-unicode-properties": {"version": "10.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/regenerate-unicode-properties/-/regenerate-unicode-properties-10.1.0.tgz", "integrity": "sha1-fDGSyrbdJOIctEYeXd190k+oN0w=", "dev": true, "requires": {"regenerate": "^1.4.2"}}, "regenerator-runtime": {"version": "0.11.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz", "integrity": "sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk=", "dev": true}, "regenerator-transform": {"version": "0.15.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/regenerator-transform/-/regenerator-transform-0.15.1.tgz", "integrity": "sha1-9sTpn8G0WR94DbJYYyjk2anY3FY=", "dev": true, "requires": {"@babel/runtime": "^7.8.4"}}, "regexpp": {"version": "3.2.0", "resolved": "https://registry.npmmirror.com/regexpp/-/regexpp-3.2.0.tgz", "integrity": "sha512-pq2bWo9mVD43nbts2wGv17XLiNLya+GklZ8kaDLV2Z08gDCsGpnKn9BFMepvWuHCbyVvY7J5o5+BVvoQbmlJLg==", "dev": true}, "regexpu-core": {"version": "5.3.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/regexpu-core/-/regexpu-core-5.3.2.tgz", "integrity": "sha1-EaKwaITzUnrsPpPbv0o7lYqVVGs=", "dev": true, "requires": {"@babel/regjsgen": "^0.8.0", "regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.1.0", "regjsparser": "^0.9.1", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}}, "regjsparser": {"version": "0.9.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/regjsparser/-/regjsparser-0.9.1.tgz", "integrity": "sha1-Jy0FqhDHwfZwlbH/Ct2uhEL8Vwk=", "dev": true, "requires": {"jsesc": "~0.5.0"}, "dependencies": {"jsesc": {"version": "0.5.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/jsesc/-/jsesc-0.5.0.tgz", "integrity": "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=", "dev": true}}}, "relateurl": {"version": "0.2.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/relateurl/-/relateurl-0.2.7.tgz", "integrity": "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=", "dev": true}, "renderkid": {"version": "3.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/renderkid/-/renderkid-3.0.0.tgz", "integrity": "sha1-X9gj5NaVHTc1jsyaWLHwaDa2Joo=", "dev": true, "requires": {"css-select": "^4.1.3", "dom-converter": "^0.2.0", "htmlparser2": "^6.1.0", "lodash": "^4.17.21", "strip-ansi": "^6.0.1"}}, "repeating": {"version": "2.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/repeating/-/repeating-2.0.1.tgz", "integrity": "sha1-UhTFOpJtNVJwdSf7q0FdvAjQbdo=", "dev": true, "requires": {"is-finite": "^1.0.0"}}, "require-from-string": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/require-from-string/-/require-from-string-2.0.2.tgz", "integrity": "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==", "dev": true}, "requires-port": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/requires-port/-/requires-port-1.0.0.tgz", "integrity": "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=", "dev": true}, "resolve": {"version": "1.22.1", "resolved": "https://registry.npmmirror.com/resolve/-/resolve-1.22.1.tgz", "integrity": "sha512-nBpuuYuY5jFsli/JIs1oldw6fOQCBioohqWZg/2hiaOybXOft4lonv85uDOKXdf8rhyK159cxU5cDcK/NKk8zw==", "dev": true, "requires": {"is-core-module": "^2.9.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}}, "resolve-cwd": {"version": "3.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/resolve-cwd/-/resolve-cwd-3.0.0.tgz", "integrity": "sha1-DwB18bslRHZs9zumpuKt/ryxPy0=", "dev": true, "requires": {"resolve-from": "^5.0.0"}, "dependencies": {"resolve-from": {"version": "5.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/resolve-from/-/resolve-from-5.0.0.tgz", "integrity": "sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=", "dev": true}}}, "resolve-from": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/resolve-from/-/resolve-from-4.0.0.tgz", "integrity": "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==", "dev": true}, "retry": {"version": "0.13.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/retry/-/retry-0.13.1.tgz", "integrity": "sha1-GFsVh6z2eRnWOzVzSeA1N7JIRlg=", "dev": true}, "reusify": {"version": "1.0.4", "resolved": "https://registry.npmmirror.com/reusify/-/reusify-1.0.4.tgz", "integrity": "sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==", "dev": true}, "rimraf": {"version": "4.1.2", "resolved": "https://registry.npmmirror.com/rimraf/-/rimraf-4.1.2.tgz", "integrity": "sha512-BlIbgFryTbw3Dz6hyoWFhKk+unCcHMSkZGrTFVAx2WmttdBSonsdtRlwiuTbDqTKr+UlXIUqJVS4QT5tUzGENQ==", "dev": true}, "rollup": {"version": "3.7.0", "resolved": "https://registry.npmjs.org/rollup/-/rollup-3.7.0.tgz", "integrity": "sha512-FIJe0msW9P7L9BTfvaJyvn1U1BVCNTL3w8O+PKIrCyiMLg+rIUGb4MbcgVZ10Lnm1uWXOTOWRNARjfXC1+M12Q==", "dev": true, "requires": {"fsevents": "~2.3.2"}}, "rollup-plugin-terser": {"version": "7.0.2", "resolved": "https://registry.npmjs.org/rollup-plugin-terser/-/rollup-plugin-terser-7.0.2.tgz", "integrity": "sha512-w3iIaU4OxcF52UUXiZNsNeuXIMDvFrr+ZXK6bFZ0Q60qyVfq4uLptoS4bbq3paG3x216eQllFZX7zt6TIImguQ==", "dev": true, "requires": {"@babel/code-frame": "^7.10.4", "jest-worker": "^26.2.1", "serialize-javascript": "^4.0.0", "terser": "^5.0.0"}}, "run-parallel": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/run-parallel/-/run-parallel-1.2.0.tgz", "integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==", "dev": true, "requires": {"queue-microtask": "^1.2.2"}}, "safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "dev": true}, "safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmmirror.com/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "dev": true}, "schema-utils": {"version": "3.1.1", "resolved": "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.1.1.tgz", "integrity": "sha512-Y5PQxS4ITlC+EahLuXaY86TXfR7Dc5lw294alXOq86JAHCihAIZfqv8nNCWvaEJvaC51uN9hbLGeV0cFBdH+Fw==", "dev": true, "requires": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}}, "select-hose": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/select-hose/-/select-hose-2.0.0.tgz", "integrity": "sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=", "dev": true}, "selfsigned": {"version": "2.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/selfsigned/-/selfsigned-2.1.1.tgz", "integrity": "sha1-GKdhPXFMDNM4XEivAHWr8/Jmr2E=", "dev": true, "requires": {"node-forge": "^1"}}, "semver": {"version": "7.3.8", "resolved": "https://registry.npmmirror.com/semver/-/semver-7.3.8.tgz", "integrity": "sha512-NB1ctGL5rlHrPJtFDVIVzTyQylMLu9N9VICA6HSFJo8MCGVTMW6gfpicwKmmK/dAjTOrqu5l63JJOpDSrAis3A==", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "send": {"version": "0.18.0", "resolved": "https://registry.npmmirror.com/send/-/send-0.18.0.tgz", "integrity": "sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==", "dev": true, "requires": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "requires": {"ms": "2.0.0"}, "dependencies": {"ms": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==", "dev": true}}}, "ms": {"version": "2.1.3", "resolved": "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "dev": true}}}, "serialize-javascript": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-4.0.0.tgz", "integrity": "sha512-GaNA54380uFefWghODBWEGisLZFj00nS5ACs6yHa9nLqlLpVLO8ChDGeKRjZnV4Nh4n0Qi7nhYZD/9fCPzEqkw==", "dev": true, "requires": {"randombytes": "^2.1.0"}}, "serve-index": {"version": "1.9.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/serve-index/-/serve-index-1.9.1.tgz", "integrity": "sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=", "dev": true, "requires": {"accepts": "~1.3.4", "batch": "0.6.1", "debug": "2.6.9", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "mime-types": "~2.1.17", "parseurl": "~1.3.2"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "depd": {"version": "1.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/depd/-/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=", "dev": true}, "http-errors": {"version": "1.6.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/http-errors/-/http-errors-1.6.3.tgz", "integrity": "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=", "dev": true, "requires": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}}, "inherits": {"version": "2.0.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=", "dev": true}, "ms": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "setprototypeof": {"version": "1.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/setprototypeof/-/setprototypeof-1.1.0.tgz", "integrity": "sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=", "dev": true}, "statuses": {"version": "1.5.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/statuses/-/statuses-1.5.0.tgz", "integrity": "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=", "dev": true}}}, "serve-static": {"version": "1.15.0", "resolved": "https://registry.npmmirror.com/serve-static/-/serve-static-1.15.0.tgz", "integrity": "sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==", "dev": true, "requires": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.18.0"}}, "setprototypeof": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/setprototypeof/-/setprototypeof-1.2.0.tgz", "integrity": "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==", "dev": true}, "shallow-clone": {"version": "3.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/shallow-clone/-/shallow-clone-3.0.1.tgz", "integrity": "sha1-jymBrZJTH1UDWwH7IwdppA4C76M=", "dev": true, "requires": {"kind-of": "^6.0.2"}}, "shebang-command": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==", "dev": true, "requires": {"shebang-regex": "^3.0.0"}}, "shebang-regex": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==", "dev": true}, "side-channel": {"version": "1.0.4", "resolved": "https://registry.npmmirror.com/side-channel/-/side-channel-1.0.4.tgz", "integrity": "sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==", "dev": true, "requires": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}}, "signal-exit": {"version": "3.0.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/signal-exit/-/signal-exit-3.0.7.tgz", "integrity": "sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=", "dev": true}, "slash": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/slash/-/slash-3.0.0.tgz", "integrity": "sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==", "dev": true}, "sockjs": {"version": "0.3.24", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/sockjs/-/sockjs-0.3.24.tgz", "integrity": "sha1-ybyJlfM6ERvqA5XsMKoyBr21zM4=", "dev": true, "requires": {"faye-websocket": "^0.11.3", "uuid": "^8.3.2", "websocket-driver": "^0.7.4"}}, "source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true}, "source-map-support": {"version": "0.5.21", "resolved": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz", "integrity": "sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==", "dev": true, "requires": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "sourcemap-codec": {"version": "1.4.8", "resolved": "https://registry.npmmirror.com/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz", "integrity": "sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA==", "dev": true}, "spdy": {"version": "4.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/spdy/-/spdy-4.0.2.tgz", "integrity": "sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=", "dev": true, "requires": {"debug": "^4.1.0", "handle-thing": "^2.0.0", "http-deceiver": "^1.2.7", "select-hose": "^2.0.0", "spdy-transport": "^3.0.0"}}, "spdy-transport": {"version": "3.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/spdy-transport/-/spdy-transport-3.0.0.tgz", "integrity": "sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=", "dev": true, "requires": {"debug": "^4.1.0", "detect-node": "^2.0.4", "hpack.js": "^2.1.6", "obuf": "^1.1.2", "readable-stream": "^3.0.6", "wbuf": "^1.7.3"}}, "statuses": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/statuses/-/statuses-2.0.1.tgz", "integrity": "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==", "dev": true}, "string_decoder": {"version": "1.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "requires": {"safe-buffer": "~5.1.0"}, "dependencies": {"safe-buffer": {"version": "5.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "dev": true}}}, "strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "dev": true, "requires": {"ansi-regex": "^5.0.1"}}, "strip-final-newline": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/strip-final-newline/-/strip-final-newline-2.0.0.tgz", "integrity": "sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=", "dev": true}, "strip-json-comments": {"version": "3.1.1", "resolved": "https://registry.npmmirror.com/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "integrity": "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "dev": true, "requires": {"has-flag": "^4.0.0"}}, "supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==", "dev": true}, "syttunnel": {"version": "0.1.7", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/syttunnel/-/syttunnel-0.1.7.tgz", "integrity": "sha1-9HhmtYTmE78idg0TDb2++XpndUg=", "dev": true}, "tapable": {"version": "2.2.1", "resolved": "https://registry.npmmirror.com/tapable/-/tapable-2.2.1.tgz", "integrity": "sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==", "dev": true}, "terser": {"version": "5.16.1", "resolved": "https://registry.npmjs.org/terser/-/terser-5.16.1.tgz", "integrity": "sha512-xvQfyfA1ayT0qdK47zskQgRZeWLoOQ8JQ6mIgRGVNwZKdQMU+5FkCBjmv4QjcrTzyZquRw2FVtlJSRUmMKQslw==", "dev": true, "requires": {"@jridgewell/source-map": "^0.3.2", "acorn": "^8.5.0", "commander": "^2.20.0", "source-map-support": "~0.5.20"}}, "terser-webpack-plugin": {"version": "5.3.6", "resolved": "https://registry.npmmirror.com/terser-webpack-plugin/-/terser-webpack-plugin-5.3.6.tgz", "integrity": "sha512-kfLFk+PoLUQIbLmB1+PZDMRSZS99Mp+/MHqDNmMA6tOItzRt+Npe3E+fsMs5mfcM0wCtrrdU387UnV+vnSffXQ==", "dev": true, "requires": {"@jridgewell/trace-mapping": "^0.3.14", "jest-worker": "^27.4.5", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.0", "terser": "^5.14.1"}, "dependencies": {"jest-worker": {"version": "27.5.1", "resolved": "https://registry.npmmirror.com/jest-worker/-/jest-worker-27.5.1.tgz", "integrity": "sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==", "dev": true, "requires": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}}, "serialize-javascript": {"version": "6.0.0", "resolved": "https://registry.npmmirror.com/serialize-javascript/-/serialize-javascript-6.0.0.tgz", "integrity": "sha512-Qr3TosvguFt8ePWqsvRfrKyQXIiW+nGbYpy8XK24NQHE83caxWt+mIymTT19DGFbNWNLfEwsrkSmN64lVWB9ag==", "dev": true, "requires": {"randombytes": "^2.1.0"}}, "supports-color": {"version": "8.1.1", "resolved": "https://registry.npmmirror.com/supports-color/-/supports-color-8.1.1.tgz", "integrity": "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "text-table": {"version": "0.2.0", "resolved": "https://registry.npmmirror.com/text-table/-/text-table-0.2.0.tgz", "integrity": "sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==", "dev": true}, "thunky": {"version": "1.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/thunky/-/thunky-1.1.0.tgz", "integrity": "sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30=", "dev": true}, "to-fast-properties": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/to-fast-properties/-/to-fast-properties-2.0.0.tgz", "integrity": "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=", "dev": true}, "to-regex-range": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "dev": true, "requires": {"is-number": "^7.0.0"}}, "toidentifier": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/toidentifier/-/toidentifier-1.0.1.tgz", "integrity": "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==", "dev": true}, "trim-right": {"version": "1.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/trim-right/-/trim-right-1.0.1.tgz", "integrity": "sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM=", "dev": true}, "ts-loader": {"version": "9.4.2", "resolved": "https://registry.npmmirror.com/ts-loader/-/ts-loader-9.4.2.tgz", "integrity": "sha512-OmlC4WVmFv5I0PpaxYb+qGeGOdm5giHU7HwDDUjw59emP2UYMHy9fFSDcYgSNoH8sXcj4hGCSEhlDZ9ULeDraA==", "dev": true, "requires": {"chalk": "^4.1.0", "enhanced-resolve": "^5.0.0", "micromatch": "^4.0.0", "semver": "^7.3.4"}}, "tslib": {"version": "1.14.1", "resolved": "https://registry.npmmirror.com/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==", "dev": true}, "tsutils": {"version": "3.21.0", "resolved": "https://registry.npmmirror.com/tsutils/-/tsutils-3.21.0.tgz", "integrity": "sha512-mHKK3iUXL+3UF6xL5k0PEhKRUBKPBCv/+RkEOpjRWxxx27KKRBmmA60A9pgOUvMi8GKhRMPEmjBRPzs2W7O1OA==", "dev": true, "requires": {"tslib": "^1.8.1"}}, "type-check": {"version": "0.4.0", "resolved": "https://registry.npmmirror.com/type-check/-/type-check-0.4.0.tgz", "integrity": "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==", "dev": true, "requires": {"prelude-ls": "^1.2.1"}}, "type-fest": {"version": "0.20.2", "resolved": "https://registry.npmmirror.com/type-fest/-/type-fest-0.20.2.tgz", "integrity": "sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==", "dev": true}, "type-is": {"version": "1.6.18", "resolved": "https://registry.npmmirror.com/type-is/-/type-is-1.6.18.tgz", "integrity": "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==", "dev": true, "requires": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}}, "typescript": {"version": "4.9.4", "resolved": "https://registry.npmjs.org/typescript/-/typescript-4.9.4.tgz", "integrity": "sha512-Uz+dTXYzxXXbsFpM86Wh3dKCxrQqUcVMxwU54orwlJjOpO3ao8L7j5lH+dWfTwgCwIuM9GQ2kvVotzYJMXTBZg==", "dev": true}, "unicode-canonical-property-names-ecmascript": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz", "integrity": "sha1-MBrNxSVjFnDTn2FG4Od/9rvevdw=", "dev": true}, "unicode-match-property-ecmascript": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz", "integrity": "sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=", "dev": true, "requires": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}}, "unicode-match-property-value-ecmascript": {"version": "2.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.1.0.tgz", "integrity": "sha1-y1//3NFqBRJPWksL98N3Agisu+A=", "dev": true}, "unicode-property-aliases-ecmascript": {"version": "2.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz", "integrity": "sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0=", "dev": true}, "unpipe": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==", "dev": true}, "update-browserslist-db": {"version": "1.0.10", "resolved": "https://registry.npmmirror.com/update-browserslist-db/-/update-browserslist-db-1.0.10.tgz", "integrity": "sha512-OztqDenkfFkbSG+tRxBeAnCVPckDBcvibKd35yDONx6OU8N7sqgwc7rCbkJ/WcYtVRZ4ba68d6byhC21GFh7sQ==", "dev": true, "requires": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}}, "uri-js": {"version": "4.4.1", "resolved": "https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==", "dev": true, "requires": {"punycode": "^2.1.0"}}, "util-deprecate": {"version": "1.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "dev": true}, "utila": {"version": "0.4.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/utila/-/utila-0.4.0.tgz", "integrity": "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=", "dev": true}, "utils-merge": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==", "dev": true}, "uuid": {"version": "8.3.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/uuid/-/uuid-8.3.2.tgz", "integrity": "sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=", "dev": true}, "vary": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/vary/-/vary-1.1.2.tgz", "integrity": "sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==", "dev": true}, "watchpack": {"version": "2.4.0", "resolved": "https://registry.npmmirror.com/watchpack/-/watchpack-2.4.0.tgz", "integrity": "sha512-Lcvm7MGST/4fup+ifyKi2hjyIAwcdI4HRgtvTpIUxBRhB+RFtUh8XtDOxUfctVCnhVi+QQj49i91OyvzkJl6cg==", "dev": true, "requires": {"glob-to-regexp": "^0.4.1", "graceful-fs": "^4.1.2"}}, "wbuf": {"version": "1.7.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/wbuf/-/wbuf-1.7.3.tgz", "integrity": "sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=", "dev": true, "requires": {"minimalistic-assert": "^1.0.0"}}, "webpack": {"version": "5.75.0", "resolved": "https://registry.npmmirror.com/webpack/-/webpack-5.75.0.tgz", "integrity": "sha512-piaIaoVJlqMsPtX/+3KTTO6jfvrSYgauFVdt8cr9LTHKmcq/AMd4mhzsiP7ZF/PGRNPGA8336jldh9l2Kt2ogQ==", "dev": true, "requires": {"@types/eslint-scope": "^3.7.3", "@types/estree": "^0.0.51", "@webassemblyjs/ast": "1.11.1", "@webassemblyjs/wasm-edit": "1.11.1", "@webassemblyjs/wasm-parser": "1.11.1", "acorn": "^8.7.1", "acorn-import-assertions": "^1.7.6", "browserslist": "^4.14.5", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^5.10.0", "es-module-lexer": "^0.9.0", "eslint-scope": "5.1.1", "events": "^3.2.0", "glob-to-regexp": "^0.4.1", "graceful-fs": "^4.2.9", "json-parse-even-better-errors": "^2.3.1", "loader-runner": "^4.2.0", "mime-types": "^2.1.27", "neo-async": "^2.6.2", "schema-utils": "^3.1.0", "tapable": "^2.1.1", "terser-webpack-plugin": "^5.1.3", "watchpack": "^2.4.0", "webpack-sources": "^3.2.3"}, "dependencies": {"@types/estree": {"version": "0.0.51", "resolved": "https://registry.npmmirror.com/@types/estree/-/estree-0.0.51.tgz", "integrity": "sha512-CuPgU6f3eT/XgKKPqKd/gLZV1Xmvf1a2R5POBOGQa6uv82xpls89HU5zKeVoyR8XzHd1RGNOlQlvUe3CFkjWNQ==", "dev": true}, "eslint-scope": {"version": "5.1.1", "resolved": "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-5.1.1.tgz", "integrity": "sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==", "dev": true, "requires": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}}, "estraverse": {"version": "4.3.0", "resolved": "https://registry.npmmirror.com/estraverse/-/estraverse-4.3.0.tgz", "integrity": "sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==", "dev": true}}}, "webpack-cli": {"version": "5.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/webpack-cli/-/webpack-cli-5.0.1.tgz", "integrity": "sha1-lfwElaxAZelCOnIt7JF1VgtvLZo=", "dev": true, "requires": {"@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^2.0.1", "@webpack-cli/info": "^2.0.1", "@webpack-cli/serve": "^2.0.1", "colorette": "^2.0.14", "commander": "^9.4.1", "cross-spawn": "^7.0.3", "envinfo": "^7.7.3", "fastest-levenshtein": "^1.0.12", "import-local": "^3.0.2", "interpret": "^3.1.1", "rechoir": "^0.8.0", "webpack-merge": "^5.7.3"}, "dependencies": {"commander": {"version": "9.5.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/commander/-/commander-9.5.0.tgz", "integrity": "sha1-vAjR61zt98y3l6lhmdQce8PmDTA=", "dev": true}}}, "webpack-dev-middleware": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/webpack-dev-middleware/-/webpack-dev-middleware-6.0.1.tgz", "integrity": "sha512-PZPZ6jFinmqVPJZbisfggDiC+2EeGZ1ZByyMP5sOFJcPPWSexalISz+cvm+j+oYPT7FIJyxT76esjnw9DhE5sw==", "dev": true, "requires": {"colorette": "^2.0.10", "memfs": "^3.4.12", "mime-types": "^2.1.31", "range-parser": "^1.2.1", "schema-utils": "^4.0.0"}, "dependencies": {"ajv": {"version": "8.11.2", "resolved": "https://registry.npmmirror.com/ajv/-/ajv-8.11.2.tgz", "integrity": "sha512-E4bfmKAhGiSTvMfL1Myyycaub+cUEU2/IvpylXkUu7CHBkBj1f/ikdzbD7YQ6FKUbixDxeYvB/xY4fvyroDlQg==", "dev": true, "requires": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}}, "ajv-keywords": {"version": "5.1.0", "resolved": "https://registry.npmmirror.com/ajv-keywords/-/ajv-keywords-5.1.0.tgz", "integrity": "sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==", "dev": true, "requires": {"fast-deep-equal": "^3.1.3"}}, "json-schema-traverse": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "integrity": "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==", "dev": true}, "schema-utils": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/schema-utils/-/schema-utils-4.0.0.tgz", "integrity": "sha512-1edyXKgh6XnJsJSQ8mKWXnN/BVaIbFMLpouRUrXgVq7WYne5kw3MW7UPhO44uRXQSIpTSXoJbmrR2X0w9kUTyg==", "dev": true, "requires": {"@types/json-schema": "^7.0.9", "ajv": "^8.8.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.0.0"}}}}, "webpack-dev-server": {"version": "4.11.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/webpack-dev-server/-/webpack-dev-server-4.11.1.tgz", "integrity": "sha1-rgfw1xygQ4z4hEbwkCm5LOgTgLU=", "dev": true, "requires": {"@types/bonjour": "^3.5.9", "@types/connect-history-api-fallback": "^1.3.5", "@types/express": "^4.17.13", "@types/serve-index": "^1.9.1", "@types/serve-static": "^1.13.10", "@types/sockjs": "^0.3.33", "@types/ws": "^8.5.1", "ansi-html-community": "^0.0.8", "bonjour-service": "^1.0.11", "chokidar": "^3.5.3", "colorette": "^2.0.10", "compression": "^1.7.4", "connect-history-api-fallback": "^2.0.0", "default-gateway": "^6.0.3", "express": "^4.17.3", "graceful-fs": "^4.2.6", "html-entities": "^2.3.2", "http-proxy-middleware": "^2.0.3", "ipaddr.js": "^2.0.1", "open": "^8.0.9", "p-retry": "^4.5.0", "rimraf": "^3.0.2", "schema-utils": "^4.0.0", "selfsigned": "^2.1.1", "serve-index": "^1.9.1", "sockjs": "^0.3.24", "spdy": "^4.0.2", "webpack-dev-middleware": "^5.3.1", "ws": "^8.4.2"}, "dependencies": {"ajv": {"version": "8.12.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ajv/-/ajv-8.12.0.tgz", "integrity": "sha1-0aBScyPiL1NWLFZ8AJkVd9++GdE=", "dev": true, "requires": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}}, "ajv-keywords": {"version": "5.1.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ajv-keywords/-/ajv-keywords-5.1.0.tgz", "integrity": "sha1-adTThaRzPNvqtElkoRcKiPh/DhY=", "dev": true, "requires": {"fast-deep-equal": "^3.1.3"}}, "brace-expansion": {"version": "1.1.11", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "glob": {"version": "7.2.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/glob/-/glob-7.2.3.tgz", "integrity": "sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "ipaddr.js": {"version": "2.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ipaddr.js/-/ipaddr.js-2.0.1.tgz", "integrity": "sha1-7KJWp6h36Reus2iwp0l930LvgcA=", "dev": true}, "json-schema-traverse": {"version": "1.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "integrity": "sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=", "dev": true}, "minimatch": {"version": "3.1.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "requires": {"brace-expansion": "^1.1.7"}}, "rimraf": {"version": "3.0.2", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/rimraf/-/rimraf-3.0.2.tgz", "integrity": "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=", "dev": true, "requires": {"glob": "^7.1.3"}}, "schema-utils": {"version": "4.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/schema-utils/-/schema-utils-4.0.0.tgz", "integrity": "sha1-YDMenjrnjsXRY1PEZ8NLOgodPfc=", "dev": true, "requires": {"@types/json-schema": "^7.0.9", "ajv": "^8.8.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.0.0"}}, "webpack-dev-middleware": {"version": "5.3.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/webpack-dev-middleware/-/webpack-dev-middleware-5.3.3.tgz", "integrity": "sha1-765nwnk5COcxHx2bBvKgjcyX5R8=", "dev": true, "requires": {"colorette": "^2.0.10", "memfs": "^3.4.3", "mime-types": "^2.1.31", "range-parser": "^1.2.1", "schema-utils": "^4.0.0"}}}}, "webpack-hot-middleware": {"version": "2.25.3", "resolved": "https://registry.npmmirror.com/webpack-hot-middleware/-/webpack-hot-middleware-2.25.3.tgz", "integrity": "sha512-IK/0WAHs7MTu1tzLTjio73LjS3Ov+VvBKQmE8WPlJutgG5zT6Urgq/BbAdRrHTRpyzK0dvAvFh1Qg98akxgZpA==", "dev": true, "requires": {"ansi-html-community": "0.0.8", "html-entities": "^2.1.0", "strip-ansi": "^6.0.0"}}, "webpack-merge": {"version": "5.8.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/webpack-merge/-/webpack-merge-5.8.0.tgz", "integrity": "sha1-Kznb8ir4d3atdEw5AiNzHTCmj2E=", "dev": true, "requires": {"clone-deep": "^4.0.1", "wildcard": "^2.0.0"}}, "webpack-sources": {"version": "3.2.3", "resolved": "https://registry.npmmirror.com/webpack-sources/-/webpack-sources-3.2.3.tgz", "integrity": "sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==", "dev": true}, "websocket-driver": {"version": "0.7.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/websocket-driver/-/websocket-driver-0.7.4.tgz", "integrity": "sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=", "dev": true, "requires": {"http-parser-js": ">=0.5.1", "safe-buffer": ">=5.1.0", "websocket-extensions": ">=0.1.1"}}, "websocket-extensions": {"version": "0.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/websocket-extensions/-/websocket-extensions-0.1.4.tgz", "integrity": "sha1-f4RzvIOd/YdgituV1+sHUhFXikI=", "dev": true}, "which": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/which/-/which-2.0.2.tgz", "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==", "dev": true, "requires": {"isexe": "^2.0.0"}}, "wildcard": {"version": "2.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/wildcard/-/wildcard-2.0.0.tgz", "integrity": "sha1-p30g5SAMb6qsl55LOq3Hs91/j+w=", "dev": true}, "word-wrap": {"version": "1.2.3", "resolved": "https://registry.npmmirror.com/word-wrap/-/word-wrap-1.2.3.tgz", "integrity": "sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ==", "dev": true}, "wrappy": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==", "dev": true}, "ws": {"version": "8.12.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ws/-/ws-8.12.1.tgz", "integrity": "sha1-xR5YPXkUC15C45vkjJNBMZQtSo8=", "dev": true}, "yallist": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/yallist/-/yallist-4.0.0.tgz", "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==", "dev": true}, "yocto-queue": {"version": "0.1.0", "resolved": "https://registry.npmmirror.com/yocto-queue/-/yocto-queue-0.1.0.tgz", "integrity": "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==", "dev": true}}}
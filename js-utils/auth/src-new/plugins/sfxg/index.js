import { success, error } from '../../core/utils';
import nativeBridgeCaller from '../../natives/bridge';
export default function loader(auth) {
  auth.hook('beforeAuth', async (data, context) => {
    console.log('beforeAuth sfxg-plugin');
    try {
      if (nativeBridgeCaller) {
        const tokenString = await nativeBridgeCaller('getToken'); // JsonString
        let tips = '授权操作成功';
        let token = {};
        if (tokenString) {
          const tokenObj = JSON.parse(tokenString);
          if (tokenObj.success) {
            token = tokenObj.data.token;
          } else {
            tips = tips + '，获取token失败';
          }
        }
        const userInfoString = await nativeBridgeCaller('getUserInfo'); // JsonString
        let userInfo = {};
        if (userInfoString) {
          const userInfoObj = JSON.parse(userInfoString);
          if (userInfoObj.success) {
            userInfo = userInfoObj.data;
          } else {
            tips = tips + '；获取userInfo失败';
          }
        }
        Object.assign(
          context,
          success({ obj: { token, ...userInfo }, errMsg: tips }),
        );
      } else {
        Object.assign(context, error());
      }
    } catch (err) {
      Object.assign(context, error({ errMsg: `请求失败：${err}` }));
    }
  });
}

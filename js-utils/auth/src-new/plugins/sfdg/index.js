import { success,error } from "../../core/utils";
import nativeBridgeCaller from "../../natives/bridge";

export default function loader(auth) {
	auth.hook("beforeAuth", async (data, context) => {
		console.log("beforeAuth sfdg-plugin");
		try {
			if (nativeBridgeCaller) {
				const userInfo = await nativeBridgeCaller("getUserInfo");
				Object.assign(context, success({ obj: userInfo }));
			} else {
				Object.assign(context, error());
			}
		} catch (err){
			Object.assign(context, error({ errMsg: `请求失败：${err}` }));
		}
	});
}
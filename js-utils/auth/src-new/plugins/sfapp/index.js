import { appUser } from 'syttunnel';

export default function loader(auth) {
  auth.hook('beforeAuth', async (data, context) => {
    console.log('beforeAuth 顺丰速运+APP');
    if (!data.appid) {
      console.warn('watch中缺少appid参数');
      context.errMsg = 'fail:缺少参数（appid）';
      context.success = false;
      context.obj = null;
      return;
    }

    const { errorCode, code } = await appUser.authorize({
      appid: data.appid,
      scope: 'sf_base,sf_userinfo,sf_mobile,sf_emp', // TODO: scope看下是否需要给个默认值，然后用户可以选择的
      reserved: '',
    });

    switch (Number(errorCode)) {
      case -1:
        context.errMsg = 'fail:拒绝授权';
        context.success = false;
        context.obj = null;
        break;
      case 0:
        context.errMsg = '';
        context.success = true;
        context.obj = { code };
        break;
      default:
        context.errMsg = `授权失败：${errorCode}`;
        context.success = false;
        context.obj = null;
        break;
    }
  });
}

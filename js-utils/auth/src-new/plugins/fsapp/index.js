import { success, error } from '../../core/utils';
import { request } from '../../core/request';

export default function loader(auth) {
  auth.hook('beforeAuth', async (data, context) => {
    console.log('beforeAuth fsapp-plugin');
    if (!data.baseUrl) {
      console.warn('watch中缺少baseUrl参数');
      context.errMsg = 'fail:缺少参数（baseUrl）';
      context.success = false;
      context.obj = null;
      return;
    }
    try {
      const {
        success: resSuccess,
        obj,
        errorMessage,
      } = await request(`${data.baseUrl}/apis-auth/login/check_token`, {
        method: 'GET',
      });
      if (resSuccess) {
        // context
        Object.assign(context, success({ obj }));
      } else {
        Object.assign(context, error({ errMsg: errorMessage }));
      }
    } catch (err) {
      console.log('err', err);
      Object.assign(context, error({ errMsg: `请求失败：${err}` }));
    }
  });
}

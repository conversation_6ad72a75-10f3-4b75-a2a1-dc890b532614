// 是否在顺丰大哥app 环境下
export const inSFAPP = () => /SFEXPRESSAPP/.test(navigator.userAgent);
export const inSFWEAPP = () => /mediaCode=wxapp/.test(location.href);
export const inSFDG = () =>
  /(freight_ca|freight_ssmp)/.test(navigator.userAgent);
export const inSFXG = () =>
  /com\.sf-express\.SFOrigin|com\.sgs\.next/.test(navigator.userAgent);
export const inCGJ = () => /leafnet/.test(navigator.userAgent); // 仓管家
export const inFSAPP = () => /sf-service-mobile/.test(navigator.userAgent);

// 判断环境，以及引入插件
export const envs = {
  SFAPP: {
    isEnv() {
      return inSFAPP();
    },
    loader: () => import('../plugins/sfapp/index'),
  },
  SFWEAPP: {
    isEnv() {
      return inSFWEAPP();
    },
    loader: () => import('../plugins/sfweapp/index'),
  },
  SFDG: {
    isEnv() {
      return inSFDG();
    },
    loader: () => import('../plugins/sfdg/index'),
  },
  SFXG: {
    isEnv() {
      return inSFXG();
    },
    loader: () => import('../plugins/sfxg/index'),
  },
  CGJ: {
    isEnv() {
      return inCGJ();
    },
    loader: () => import('../plugins/cgj/index'),
  },
  FSAPP: {
    isEnv() {
      return inFSAPP();
    },
    loader: () => import('../plugins/fsapp/index'),
  },
};

/** 获取当前的环境loader */
export function getEnvLoader(envConfig, envName) {
  if (envName && envConfig[envName]) {
    const { loader } = envConfig[envName];

    return { loader, envName };
  } else if (!envName) {
    for (const key in envConfig) {
      const { isEnv, loader } = envConfig[key] || {};

      // TODO: 是否可能存在两个满足两个环境的情况
      if (typeof isEnv === 'function' && isEnv()) {
        return { loader, envName: key };
      }
    }
  }

  return null;
}

export function request(url, options) {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    const { method = 'get', headers = {}, data = null } = options;
    xhr.open(method.toUpperCase(), url, true);
    for (let header in headers) {
      if (headers.hasOwnProperty(header)) {
        xhr.setRequestHeader(header, headers[header]);
      }
    }
    xhr.onreadystatechange = function () {
      if (xhr.readyState === XMLHttpRequest.DONE) {
        if (xhr.status >= 200 && xhr.status < 300) {
          let response;
          try {
            response = JSON.parse(xhr.responseText);
          } catch (err) {
            response = xhr.responseText;
          }
          resolve(response);
        } else {
          reject(
            new Error(
              `request failed with status ${xhr.status}: ${xhr.responseText}`,
            ),
          );
        }
      }
    };
    /**
     * 网络错误，例如无法连接到服务器、DNS 解析失败等。
     * 请求被阻止，例如跨域请求被浏览器拦截。
     * 请求被服务器拒绝，例如请求的 URL 不存在或服务器出现错误。
     */
    xhr.onerror = function () {
      reject(new Error(`request error: ${xhr.status}`));
    };
    xhr.send(data);
  });
}

export function readonlyData(data) {
	// const obj = {};
	// Object.defineProperty(obj)
}

function getUrlParams() {
	const search = location.search.replace("?", "");
	const [, hashParams = ""] = location.hash.split("?");

	return {
		search,
		hashParams
	};
}

function parseUrl(url) {
	const queryArr = url.split("&");

	return queryArr.reduce((prev, current) => {
		const [key, value] = current.split("=");

		if (key) {
			return { [key]: value, ...prev };
		}

		return prev;
	}, {});
}

/** 解析url */
export function queryString() {
	const { search, hashParams } = getUrlParams();

	return {
		searchParams: parseUrl(search),
		hashParams: parseUrl(hashParams)
	};
}

/**
 * 授权函数响应的默认值
 * @param success  授权是否成功;
 * @param obj 授权结果，例：{ token: 'xxxxx' };
 * @param errCode  0：调用第三方授权成功，获取结果结果成功  1：调用成功， 获取失败(例：拉起了授权，用户拒绝授权) —1：调用失败;
 * @param errMsg  错误提示;
 * @returns 
 */
export const RESPONSE_DEFAULT = { success: false, obj: null, errMsg: "", errCode: -1 };
export const RESPONSE_SUCCESS = { success: true, obj: {} , errMsg: "ok:获取授权成功", errCode: 0 };
export const RESPONSE_ERROR = { success: false, obj: null, errMsg: "fail:获取授权失败", errCode: -1 };

/**
 * 合并返回参数
 * @param data 对象{}
 * @returns { success: false, obj: null, errMsg: "", errCode: -1 }
 */
export const response = (data = {}) => ({ ...RESPONSE_DEFAULT, ...data  });
export const success = (data = {}) => ({ ...RESPONSE_SUCCESS, ...data  });
export const error = (data = {}) => ({ ...RESPONSE_ERROR, ...data  });
export const noEnv = (env) => `未匹配程序运行环境：${env || ""}，请核对程序运行环境是否正确或查看文档是否支持该程序授权`;
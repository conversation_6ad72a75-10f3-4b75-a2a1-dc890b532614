function Hook() {
	this.context = {}; // 钩子可以保存上下文
	this.hookList = {};
}

Hook.prototype.hook = function hook(hookName, fn) {
	if (!this.hookList[hookName]) {
		// hook挂在钩子，有且只有一个待执行的钩子
		this.hookList[hookName] = fn;
	} else {
		console.warn("当前钩子已经被注册");
	}
};

Hook.prototype.call = function call(hookName, data) {
	if (typeof this.hookList[hookName] === "function") {      

		// TODO: 后续将data改为只读
		return this.hookList[hookName](data, this.context);
	}
};

Hook.prototype.getContext = function getContext() {
	return this.context;
};

export default Hook;
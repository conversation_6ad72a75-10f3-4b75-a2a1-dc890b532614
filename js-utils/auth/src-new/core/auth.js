import { getEnvLoader, envs } from "./env";
import Hook from "./hook";
import { queryString } from "./utils";
// eslint-disable-next-line @typescript-eslint/no-empty-function
const noop = () => {};
// 空loader
const emptyLoader = () => Promise.resolve({ default: null });

export default class Auth {
	// 初始化环境，环境名称
	constructor(envName) {
		// 环境加载器
		this.envLoader = null;
		// 当前环境名称
		this.envName = "";
		this.envs = envs; // 环境对象
		this.extraEnvs = null; // 额外环境对象
		this.envHook = new Hook(); // 环境鉴权hook
		this.globalValue = null; // 全局的变量

		// 初始化器
		this.initializer = null;
		// 执行器
		this.execuor = null;
		// 监听对象
		this.watchObj = {};

		// 如果实例化的时候有环境名，则初始化环境加载器和环境名称
		if (envName) {
			const envResult = getEnvLoader(envs, envName) || { envName: "", loader: emptyLoader };

			if (!envResult.envName) console.warn("未找到当前环境", envName);
			
			this.envLoader = envResult.loader;
			this.envName = envResult.envName;
		}
	}

	/** 外部监听对应环境 */
	watch(envName, params, callback) {
		let data = {};
		let cb = callback || noop;

		// 中间的params可以不用填，第二个参数可以直接填入回调函数
		if (typeof params === "function") {
			cb = params;
		} else {
			data = params;
		}

		this.watchObj[envName] = {
			params: data,
			cb
		};
	}

	/** 初始化全局对象 */
	initGlobalValue() {
		const cache = JSON.parse(sessionStorage.getItem("queryCache") || "{}");
		const queryStringParams = queryString();
		const urlParams = {
			hashParams: {
				...cache.hashParams,
				...queryStringParams.hashParams
			},
			searchParams: {
				...cache.searchParams,
				...queryStringParams.searchParams
			}
		};

		this.globalValue = {
			userAgent: navigator.userAgent,
			urlParams: urlParams
		};

		sessionStorage.setItem("queryCache", JSON.stringify(urlParams));

		console.log("全局对象", this.globalValue);
	}

	/** 外部添加额外环境 */
	addEnvs(extraEnvs) {
		this.extraEnvs = Object.keys(extraEnvs).reduce((prev, current) => {
			return {
				...prev,
				[current]: {
					isEnv: extraEnvs[current],
					loader: emptyLoader
				}
			};
		}, {});
	}

	/** 初始化 该方法时在外部调用的 */
	init() {
		// 当初始化一次后，后续就不会再进行初始化了
		if (this.initializer) {
			return this.initializer;
		}

		this.initGlobalValue();

		// 当没有环境加载器时，需要先判断当前的环境来获取加载器
		if (!this.envLoader) {
			// TODO: 如果实例化时候的环境没有找到，那么可能会去查找其它环境，是否需要优化
			const envResult = getEnvLoader(envs)
												|| getEnvLoader(this.extraEnvs)
												|| { envName: "", loader: emptyLoader };

			this.envLoader = envResult.loader;
			this.envName = envResult.envName;
		}

		this.initializer = this.envLoader().then(({ default: l }) => {
			// 只有标准的环境才有注册钩子，并且开始绑定beforeAuth钩子
			if (l) l(this.envHook);

			// 如果有环境名，则绑定对应的回调
			if (this.envName && this.watchObj[this.envName]) {
				// 绑定hook的通用auth钩子，每个环境都需要绑定auth钩子，来调用回调
				this.envHook.hook("auth", this.watchObj[this.envName].cb || noop);
			}
			
			return { envName: this.envName };
		});

		return this.initializer;
	}

	start() {
		// 如果当前环境值没有，则说明未执行init方法
		if (!this.initializer) {
			return Promise.resolve({
				data: null,
				err: new Error("请先进行鉴权对象的初始化（调用init方法）")
			});
		}

		// 有且只能有一个正在执行的流程
		if (!this.execuor) {
			this.execuor = this.initializer.then(async ({ envName }) => {
				let data = null;
				let err = null;

				if (envName) {
					try {
						const watchExtraParams = this.watchObj[envName].params;
						const callData = {
							...watchExtraParams,
							...this.globalValue
						};
	
						await this.envHook.call("beforeAuth", callData);
						await this.envHook.call("auth", callData);

						data = {
							envName: envName,
							globalValue: this.globalValue,
							context: this.envHook.getContext()
						};
					} catch(error) {
						console.log("err", error);
						err = error;
					}
				}

				this.execuor = null;
	
				return {
					data,
					err
				};
			});
		}

		return this.execuor;
	}

	getData() {
		return {
			envName: this.envName,
			globalValue: this.globalValue,
			context: this.envHook.getContext()
		}; 
	}
}

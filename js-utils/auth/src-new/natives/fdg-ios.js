/**
 * 顺丰大哥 ios 端通信
 */
function JSBridge() {
	this.name = "JSBridge";
}

/**
 * 设置 WebViewJavascriptBridge
 * @param {Function} callback 回调函数
 */
function setupWebViewJavascriptBridge(callback) {
	if (window.WebViewJavascriptBridge) {
		return callback(window.WebViewJavascriptBridge);
	}
	if (window.WVJBCallbacks) {
		return window.WVJBCallbacks.push(callback);
	}
	window.WVJBCallbacks = [callback];
	const WVJBIframe = document.createElement("iframe");
	WVJBIframe.style.display = "none";
	WVJBIframe.src = "https://__bridge_loaded__"; // URL scheme 劫持，然后回调函数
	document.documentElement.appendChild(WVJBIframe);
	setTimeout(function () {
		document.documentElement.removeChild(WVJBIframe);
	}, 0);
}

/**
 * 调用原生方法
 * @param {String} method 方法名
 * @param {Object} data 参数
 * @returns {Promise} Promise 对象
 */
function callHandler(method, data = null) {
	return new Promise(resolve => {
		setupWebViewJavascriptBridge(function (bridge) {
			console.log("bridge", bridge);
			bridge.callHandler(method, data, function responseCallback(responseData) {
				resolve(responseData);
			});
		});
	});
}

JSBridge.prototype.call = async function (method, data) {
	const res = await callHandler(method, data).catch(err => {
		console.log(err);
	});
	return res;
};

/**
 * 获取用户信息
 * @param {Object} data 参数
 * @returns {Promise} Promise 对象
 */
JSBridge.prototype.getUserInfo = async function (data) {
	const res = await this.call("fdg_getToken", data);
	return res;
};

const fdgBridge = new JSBridge();

export default fdgBridge;

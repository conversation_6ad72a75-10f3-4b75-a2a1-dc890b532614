/**
 * 顺丰大哥APP 安卓端通信
 */

// JSBridge 类
function JSBridge() {
	this.name = "JSBridge";
	this.reset = true;
}

/**
 * @param null
 * 初始化设置 WebViewJavascriptBridge
 */
JSBridge.prototype._init = function () {
	return new Promise((resolve, reject) => {
		try {
			if (window.WebViewJavascriptBridge || window.FDGNaive) {
				window.FDGNaive = window.WebViewJavascriptBridge;
				console.log("WebViewJavascriptBridgeReady");
				resolve(window.WebViewJavascriptBridge);
			} else {
				document.addEventListener(
					"WebViewJavascriptBridgeReady",
					function (WebViewJavascriptBridge) {
						console.log("WebViewJavascriptBridgeReady");
						window.FDGNaive = WebViewJavascriptBridge;
						resolve(WebViewJavascriptBridge);
					},
					false,
				);
			}
		} catch (error) {
			reject(error);
		}
	});
};

/**
 * @param null
 * JSBridge 安卓初始化回调接口
 */
JSBridge.prototype.__init__ = function (e) {
	console.log("__init__", e);
	const bridge = e["init"] ? e : e.bridge;
	if (this.reset) {
		this.reset = false;
		bridge.init(function (message, responseCallback) {
			console.log("JS got a message", message);
			const data = {
				"Javascript Responds": "测试中文!",
			};
			console.log("JS responding with", data);
			responseCallback(data);
		});
	}
};

/**
 * @param data
 * 调用原生客户端方法
 */
function callHandler(bridge, method, data) {
	return new Promise(resolve => {
		bridge.callHandler(method, data, function (response) {
			console.log("response", response);
			if (typeof response === "object") {
				resolve(response);
			} else {
				resolve(JSON.parse(response));
			}
		});
	});
}

// 调用原生客户端方法
JSBridge.prototype.call = async function (method, data) {
	const bridge = await this._init(); 
	this.__init__(bridge);
	console.log("this.__init__");

	const res = await callHandler(
		bridge["init"] ? bridge : bridge.bridge,
		method,
		data,
	).catch(err => {
		console.log(err);
	});
	console.log("res", res);
	return res;
};

// 获取用户信息
JSBridge.prototype.getUserInfo = async function (data) {
	const res = await this.call("fdg_getToken", data);
	return res;
};

// 初始化
// JSBridge.prototype.init = async function () {
// 	const res = await this._init();
// 	return res;
// };

const fdgBridge = new JSBridge();

export default fdgBridge;

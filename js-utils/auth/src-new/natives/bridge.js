/**
 * 返回当前应用所在的 app 环境能调用原生 APP 能力的方法。
 */

import { envs, getCurrentEnv } from "./utils.js";

// 获取当前 app 环境
const appEnv = getCurrentEnv();
console.log("app", appEnv);

// 初始化原生方法
const init = async (appEnv) => {
	try {
		let nativeBridge;
		if (envs[appEnv]) {
			nativeBridge = await envs[appEnv].loader();
		} else {
			console.error("Invalid app environment:", appEnv);
			return null;
		}
		return nativeBridge.default;
	} catch (error) {
		console.error("Failed to import native bridge:", error);
		return null;
	}
};

// 调用原生方法
const caller = async (method, data, path) => {
	const native = await init(path);
	if (!native) {
		console.error("Failed to initialize native bridge.");
		return null;
	}
	try {
		if (typeof native[method] === "function") {
			return native[method](data);
		} else {
			console.error(`Failed to call native method ${method}: native[method] is not function`);
			return null;
		}
	} catch (error) {
		console.error(`Failed to call native method ${method}:`, error);
		return null;
	}
};

// 根据 app 环境返回对应的原生方法
const appEnvConstructor = (appEnv) => {
	if (!envs[appEnv]) {
		console.error(`Unknown app environment: ${appEnv}`);
		return null;
	}
	return (method, data) => caller(method, data, appEnv);
};

export default appEnvConstructor(appEnv);

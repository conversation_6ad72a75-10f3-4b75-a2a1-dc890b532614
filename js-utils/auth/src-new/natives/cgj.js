// JSBridge 类
function JSBridge() {
  this.name = 'JSBridge';
}

// 调用原生客户端方法
JSBridge.prototype.call = async function call(method, data) {
  const bridge = window.nativeBridge; // 原生端提前注入
  if (!bridge) {
    throw new Error('原生端没有注入 nativeBridge，bridge is null or undefined');
  }
  if (data) {
    // 避免传递不必要的参数，导致原生端读取错误
    return bridge[method](data);
  }
  return bridge[method]();
};

// 获取用户信息
JSBridge.prototype.getUserInfo = async function getUserInfo(data) {
  const res = await this.call('sgsApp_getUserInfo', data);
  return res;
};

// 获取用户 token
JSBridge.prototype.getToken = async function getToken(data) {
  const res = await this.call('sgsApp_getSGSToken', data);
  return res;
};

const fdgBridge = new JSBridge();

export default fdgBridge;

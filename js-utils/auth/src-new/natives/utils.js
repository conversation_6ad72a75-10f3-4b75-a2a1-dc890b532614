import { inCGJ, inSFDG, inSFXG } from '../core/env.js';

const { userAgent } = navigator;

export const envs = {
  fdg_android: {
    isEnv() {
      return (
        inSFDG() &&
        (userAgent.indexOf('Android') !== -1 || userAgent.indexOf('Adr') !== -1)
      );
    },
    loader: () => import('./fdg.js'),
  },
  fdg_ios: {
    isEnv() {
      return (
        inSFDG() &&
        !(
          userAgent.indexOf('Android') !== -1 || userAgent.indexOf('Adr') !== -1
        )
      );
    },
    loader: () => import('./fdg-ios.js'),
  },
  fxg: {
    isEnv() {
      return inSFXG();
    },
    loader: () => import('./fxg.js'),
  },
  cgj: {
    isEnv() {
      return inCGJ();
    },
    loader: () => import('./cgj.js'),
  },
};

/**
 * 获取当前环境
 * @return {string} - 当前环境，如果没有设置环境则返回 null
 */
export const getCurrentEnv = () => {
  let currentEnv = null;
  for (let envKey in envs) {
    if (
      Object.prototype.hasOwnProperty.call(envs, envKey) &&
      envs[envKey].isEnv()
    ) {
      currentEnv = envKey;
      break;
    }
  }
  return currentEnv;
};

{"name": "@ky/auth", "version": "1.0.34", "description": "", "main": "dist/es/main.js", "module": "dist/es/main.js", "files": ["dist"], "scripts": {"buildDemo": "rimraf examples/dist && webpack --config ./examples/webpack.config.js", "startDemo": "webpack serve --config ./examples/webpack.config.js", "clear": "<PERSON><PERSON><PERSON> dist", "start": "npm run clear && rollup -w -c", "build": "npm run clear && npm run buildEsm && npm run buildUmd", "buildEsm": "rollup -c rollup.config.esm.js", "buildUmd": "rollup -c rollup.config.umd.js", "release": "npm version patch && npm run build && npm publish --registry=https://artifactory.sf-express.com/artifactory/api/npm/npm-sf-local/"}, "repository": {"type": "git", "url": "http://git.sf-express.com/scm/FOP-WEB/fop-web-eca-ky-auth-oss.git"}, "publishConfig": {"registry": "https://artifactory.sf-express.com/artifactory/api/npm/npm-sf-local/"}, "author": "linjiyu <<EMAIL>>", "license": "ISC", "dependencies": {}, "devDependencies": {"@babel/core": "^7.21.4", "@babel/plugin-transform-runtime": "^7.21.4", "@babel/preset-env": "^7.21.4", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-commonjs": "^23.0.4", "@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-typescript": "^9.0.2", "@typescript-eslint/eslint-plugin": "^5.42.1", "@typescript-eslint/parser": "^5.42.1", "babel-core": "^6.26.3", "copy-webpack-plugin": "^11.0.0", "eslint": "^8.27.0", "express": "^4.18.2", "html-webpack-plugin": "^5.5.0", "rimraf": "^4.1.2", "rollup": "^3.7.0", "rollup-plugin-terser": "^7.0.2", "syttunnel": "^0.1.7", "ts-loader": "^9.4.2", "typescript": "^4.9.4", "webpack": "^5.75.0", "webpack-cli": "^5.0.1", "webpack-dev-middleware": "^6.0.1", "webpack-dev-server": "^4.11.1", "webpack-hot-middleware": "^2.25.3"}}
const path = require("path");
const babel = require("@rollup/plugin-babel");
const resolve = require("@rollup/plugin-node-resolve");
const commonjs = require("@rollup/plugin-commonjs");


const appDir = path.resolve(process.cwd(), "src-new");

module.exports = {
	input: {
		"main":  path.resolve(appDir, "./index.js"),
	},
	output: {
		name: "ky-auth",
		format: "amd",
		dir: "dist/amd",
		entryFileNames: "[name].js",
		chunkFileNames: "[name].js",	
	},
	plugins: [
		resolve(),
		commonjs(),
		babel({
			babelHelpers: "runtime",
			exclude: "node_modules/**",
			presets: ["@babel/preset-env"],
			plugins: ["@babel/plugin-transform-runtime"]
		})
	],
	external: ["syttunnel"],
	treeshake: true
};
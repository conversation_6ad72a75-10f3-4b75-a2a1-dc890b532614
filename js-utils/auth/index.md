```js
import auth from 'ky-auth';

auth.SFAPP.hook("auth", (data, context) => {
	// context中获取鉴权的值
    return {
        
    }

    context.token = ''
});

auth.SFAPP1.hook("auth", (data, context) => {
	// context中获取鉴权的值
    return {
        
    }
});

auth.SFAPP2.hook("auth", (data, context) => {
	// context中获取鉴权的值
    return {

    }
});

auth.watch('SFAPP', {}, (data, context) => {

})

// auth.SFAPP = {
//     hook: (hookName, fn) => {
//         this.SFAPP.hooks[hookName] = fn;
//     },
//     hooks: {
//         'auth': fn,
//         'xxx': fn1
//     }
// }


// 先初始化，在初始化中加载对应的环境
auth.init({
    appid: "202108272254221331",
    appid1: "",
}).then(res => {
    
});

// 在初始化后调用执行方法
auth.exec().then((data) => {
    // data 为鉴权函数
    console.log("鉴权成功");
}).catch(err => {
    console.log("err", err);
    alert(err.message);
});

auth.exec().then()
```
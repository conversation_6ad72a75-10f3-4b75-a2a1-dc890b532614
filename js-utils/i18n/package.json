{"name": "@ky/i18n", "version": "0.1.3", "description": "自动替换中文", "bin": {"rezh": "./src/index.js"}, "publishConfig": {"registry": "http://artifactory.sf-express.com/artifactory/api/npm/npm-sf-local/"}, "keywords": ["vue", "react", "i18n", "jsx"], "license": "MIT", "author": "dong", "dependencies": {"axios": "^0.20.0", "md5": "^2.3.0", "progress": "^2.0.3", "walk": "^2.3.14"}, "devDependencies": {"babel-eslint": "^10.1.0", "eslint": "^7.2.0", "eslint-config-airbnb-base": "^14.2.0", "eslint-plugin-import": "^2.22.0", "jest": "^26.4.2", "ndir": "^0.1.5"}}
{"name": "@ky/i18n", "version": "0.1.3", "lockfileVersion": 1, "requires": true, "dependencies": {"@babel/code-frame": {"version": "7.12.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/code-frame/download/@babel/code-frame-7.12.11.tgz", "integrity": "sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8=", "dev": true, "requires": {"@babel/highlight": "^7.10.4"}}, "@babel/core": {"version": "7.12.10", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/core/download/@babel/core-7.12.10.tgz", "integrity": "sha1-t5ouG59w7T2Eu/ttjE74JfYGvM0=", "dev": true, "requires": {"@babel/code-frame": "^7.10.4", "@babel/generator": "^7.12.10", "@babel/helper-module-transforms": "^7.12.1", "@babel/helpers": "^7.12.5", "@babel/parser": "^7.12.10", "@babel/template": "^7.12.7", "@babel/traverse": "^7.12.10", "@babel/types": "^7.12.10", "convert-source-map": "^1.7.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.1", "json5": "^2.1.2", "lodash": "^4.17.19", "semver": "^5.4.1", "source-map": "^0.5.0"}, "dependencies": {"json5": {"version": "2.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/json5/download/json5-2.1.3.tgz", "integrity": "sha1-ybD3+pIzv+WAf+ZvzzpWF+1ZfUM=", "dev": true, "requires": {"minimist": "^1.2.5"}}, "semver": {"version": "5.7.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/download/semver-5.7.1.tgz", "integrity": "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=", "dev": true}}}, "@babel/generator": {"version": "7.12.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/generator/download/@babel/generator-7.12.11.tgz", "integrity": "sha1-mKffe4w1jJo3qweiQFaFMBaro68=", "dev": true, "requires": {"@babel/types": "^7.12.11", "jsesc": "^2.5.1", "source-map": "^0.5.0"}}, "@babel/helper-function-name": {"version": "7.12.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-function-name/download/@babel/helper-function-name-7.12.11.tgz", "integrity": "sha1-H9dziu5dz1PD7P8k8dqcUR7Ee0I=", "dev": true, "requires": {"@babel/helper-get-function-arity": "^7.12.10", "@babel/template": "^7.12.7", "@babel/types": "^7.12.11"}}, "@babel/helper-get-function-arity": {"version": "7.12.10", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.12.10.tgz", "integrity": "sha1-sViBejFltfqiBHgl36YZcN3MFs8=", "dev": true, "requires": {"@babel/types": "^7.12.10"}}, "@babel/helper-member-expression-to-functions": {"version": "7.12.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.12.7.tgz", "integrity": "sha1-qne9A5bsgRTl4weH76eFmdh0qFU=", "dev": true, "requires": {"@babel/types": "^7.12.7"}}, "@babel/helper-module-imports": {"version": "7.12.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-module-imports/download/@babel/helper-module-imports-7.12.5.tgz", "integrity": "sha1-G/wCKfeUmI927QpNTpCGCFC1Tfs=", "dev": true, "requires": {"@babel/types": "^7.12.5"}}, "@babel/helper-module-transforms": {"version": "7.12.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.12.1.tgz", "integrity": "sha1-eVT+xx9bMsSOSzA7Q3w0RT/XJHw=", "dev": true, "requires": {"@babel/helper-module-imports": "^7.12.1", "@babel/helper-replace-supers": "^7.12.1", "@babel/helper-simple-access": "^7.12.1", "@babel/helper-split-export-declaration": "^7.11.0", "@babel/helper-validator-identifier": "^7.10.4", "@babel/template": "^7.10.4", "@babel/traverse": "^7.12.1", "@babel/types": "^7.12.1", "lodash": "^4.17.19"}}, "@babel/helper-optimise-call-expression": {"version": "7.12.10", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.12.10.tgz", "integrity": "sha1-lMpOMG7hGn3W6fQoI+Ksa0mIHi0=", "dev": true, "requires": {"@babel/types": "^7.12.10"}}, "@babel/helper-plugin-utils": {"version": "7.10.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.10.4.tgz", "integrity": "sha1-L3WoMSadT2d95JmG3/WZJ1M883U=", "dev": true}, "@babel/helper-replace-supers": {"version": "7.12.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.12.11.tgz", "integrity": "sha1-6lEWWPxmx5CPkjEG3YjgjRmX1g0=", "dev": true, "requires": {"@babel/helper-member-expression-to-functions": "^7.12.7", "@babel/helper-optimise-call-expression": "^7.12.10", "@babel/traverse": "^7.12.10", "@babel/types": "^7.12.11"}}, "@babel/helper-simple-access": {"version": "7.12.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-simple-access/download/@babel/helper-simple-access-7.12.1.tgz", "integrity": "sha1-MkJ+WqYVR9OOsebq9f0UJv2tkTY=", "dev": true, "requires": {"@babel/types": "^7.12.1"}}, "@babel/helper-split-export-declaration": {"version": "7.12.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.12.11.tgz", "integrity": "sha1-G0zEJEWGQ8R9NwIiI9oz126kYDo=", "dev": true, "requires": {"@babel/types": "^7.12.11"}}, "@babel/helper-validator-identifier": {"version": "7.12.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.12.11.tgz", "integrity": "sha1-yaHwIZF9y1zPDU5FPjmQIpgfye0=", "dev": true}, "@babel/helpers": {"version": "7.12.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helpers/download/@babel/helpers-7.12.5.tgz", "integrity": "sha1-Ghukp2jZtYMQ7aUWxEmRP+ZHEW4=", "dev": true, "requires": {"@babel/template": "^7.10.4", "@babel/traverse": "^7.12.5", "@babel/types": "^7.12.5"}}, "@babel/highlight": {"version": "7.10.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/highlight/download/@babel/highlight-7.10.4.tgz", "integrity": "sha1-fRvf1ldTU4+r5sOFls23bZrGAUM=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.10.4", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.12.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/parser/download/@babel/parser-7.12.11.tgz", "integrity": "sha1-nONZW810vFxGaQXobFNbiyUBHnk=", "dev": true}, "@babel/plugin-syntax-async-generators": {"version": "7.8.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz", "integrity": "sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-bigint": {"version": "7.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-bigint/download/@babel/plugin-syntax-bigint-7.8.3.tgz", "integrity": "sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-class-properties": {"version": "7.12.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.12.1.tgz", "integrity": "sha1-vLKXxTZueb663vUJVJzZOwTxmXg=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-import-meta": {"version": "7.10.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-import-meta/download/@babel/plugin-syntax-import-meta-7.10.4.tgz", "integrity": "sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-json-strings": {"version": "7.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz", "integrity": "sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-logical-assignment-operators": {"version": "7.10.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "integrity": "sha1-ypHvRjA1MESLkGZSusLp/plB9pk=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "integrity": "sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.10.4.tgz", "integrity": "sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz", "integrity": "sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz", "integrity": "sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz", "integrity": "sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-top-level-await": {"version": "7.12.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.12.1.tgz", "integrity": "sha1-3WwLNXrBuxQtmFN0UKMZYl0T0qA=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/template": {"version": "7.12.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/template/download/@babel/template-7.12.7.tgz", "integrity": "sha1-yBcjNpYBjjn7tsSR0vtoTgXtQ7w=", "dev": true, "requires": {"@babel/code-frame": "^7.10.4", "@babel/parser": "^7.12.7", "@babel/types": "^7.12.7"}}, "@babel/traverse": {"version": "7.12.12", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/traverse/download/@babel/traverse-7.12.12.tgz", "integrity": "sha1-0M2HiScE7djaAC1nS8gRzmR0M3Y=", "dev": true, "requires": {"@babel/code-frame": "^7.12.11", "@babel/generator": "^7.12.11", "@babel/helper-function-name": "^7.12.11", "@babel/helper-split-export-declaration": "^7.12.11", "@babel/parser": "^7.12.11", "@babel/types": "^7.12.12", "debug": "^4.1.0", "globals": "^11.1.0", "lodash": "^4.17.19"}}, "@babel/types": {"version": "7.12.12", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/download/@babel/types-7.12.12.tgz", "integrity": "sha1-Rgim7DE6u9h6+lUATTc60EqWwpk=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.12.11", "lodash": "^4.17.19", "to-fast-properties": "^2.0.0"}}, "@bcoe/v8-coverage": {"version": "0.2.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@bcoe/v8-coverage/download/@bcoe/v8-coverage-0.2.3.tgz", "integrity": "sha1-daLotRy3WKdVPWgEpZMteqznXDk=", "dev": true}, "@cnakazawa/watch": {"version": "1.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@cnakazawa/watch/download/@cnakazawa/watch-1.0.4.tgz", "integrity": "sha1-+GSuhQBND8q29QvpFBxNo2jRZWo=", "dev": true, "requires": {"exec-sh": "^0.3.2", "minimist": "^1.2.0"}}, "@eslint/eslintrc": {"version": "0.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@eslint/eslintrc/download/@eslint/eslintrc-0.2.2.tgz", "integrity": "sha1-0B/HkeL8M+iKKdbz3H6T0M14S3Y=", "dev": true, "requires": {"ajv": "^6.12.4", "debug": "^4.1.1", "espree": "^7.3.0", "globals": "^12.1.0", "ignore": "^4.0.6", "import-fresh": "^3.2.1", "js-yaml": "^3.13.1", "lodash": "^4.17.19", "minimatch": "^3.0.4", "strip-json-comments": "^3.1.1"}, "dependencies": {"globals": {"version": "12.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/globals/download/globals-12.4.0.tgz", "integrity": "sha1-oYgTV2pBsAokqX5/gVkYwuGZJfg=", "dev": true, "requires": {"type-fest": "^0.8.1"}}}}, "@istanbuljs/load-nyc-config": {"version": "1.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@istanbuljs/load-nyc-config/download/@istanbuljs/load-nyc-config-1.1.0.tgz", "integrity": "sha1-/T2x1Z7PfPEh6AZQu4ZxL5tV7O0=", "dev": true, "requires": {"camelcase": "^5.3.1", "find-up": "^4.1.0", "get-package-type": "^0.1.0", "js-yaml": "^3.13.1", "resolve-from": "^5.0.0"}, "dependencies": {"find-up": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/find-up/download/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "dev": true, "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}, "locate-path": {"version": "5.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/locate-path/download/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "dev": true, "requires": {"p-locate": "^4.1.0"}}, "p-limit": {"version": "2.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-limit/download/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-locate/download/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "dev": true, "requires": {"p-limit": "^2.2.0"}}, "p-try": {"version": "2.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-try/download/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "dev": true}, "path-exists": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-exists/download/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "dev": true}, "resolve-from": {"version": "5.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/resolve-from/download/resolve-from-5.0.0.tgz", "integrity": "sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=", "dev": true}}}, "@istanbuljs/schema": {"version": "0.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@istanbuljs/schema/download/@istanbuljs/schema-0.1.2.tgz", "integrity": "sha1-JlIL8Jq+SlZEzVQU43ElqJVCQd0=", "dev": true}, "@jest/console": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/console/download/@jest/console-26.6.2.tgz", "integrity": "sha1-TgS8RkAUNYsDq0k3gF7jagrrmPI=", "dev": true, "requires": {"@jest/types": "^26.6.2", "@types/node": "*", "chalk": "^4.0.0", "jest-message-util": "^26.6.2", "jest-util": "^26.6.2", "slash": "^3.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "@jest/core": {"version": "26.6.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/core/download/@jest/core-26.6.3.tgz", "integrity": "sha1-djn8s4M9dIpGVq2lS94ZMFHkX60=", "dev": true, "requires": {"@jest/console": "^26.6.2", "@jest/reporters": "^26.6.2", "@jest/test-result": "^26.6.2", "@jest/transform": "^26.6.2", "@jest/types": "^26.6.2", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "exit": "^0.1.2", "graceful-fs": "^4.2.4", "jest-changed-files": "^26.6.2", "jest-config": "^26.6.3", "jest-haste-map": "^26.6.2", "jest-message-util": "^26.6.2", "jest-regex-util": "^26.0.0", "jest-resolve": "^26.6.2", "jest-resolve-dependencies": "^26.6.3", "jest-runner": "^26.6.3", "jest-runtime": "^26.6.3", "jest-snapshot": "^26.6.2", "jest-util": "^26.6.2", "jest-validate": "^26.6.2", "jest-watcher": "^26.6.2", "micromatch": "^4.0.2", "p-each-series": "^2.1.0", "rimraf": "^3.0.0", "slash": "^3.0.0", "strip-ansi": "^6.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "@jest/environment": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/environment/download/@jest/environment-26.6.2.tgz", "integrity": "sha1-ujZMxy4iHnnMjwqZVVv111d8+Sw=", "dev": true, "requires": {"@jest/fake-timers": "^26.6.2", "@jest/types": "^26.6.2", "@types/node": "*", "jest-mock": "^26.6.2"}}, "@jest/fake-timers": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/fake-timers/download/@jest/fake-timers-26.6.2.tgz", "integrity": "sha1-RZwym89wzuSvTX4/PmeEgSNTWq0=", "dev": true, "requires": {"@jest/types": "^26.6.2", "@sinonjs/fake-timers": "^6.0.1", "@types/node": "*", "jest-message-util": "^26.6.2", "jest-mock": "^26.6.2", "jest-util": "^26.6.2"}}, "@jest/globals": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/globals/download/@jest/globals-26.6.2.tgz", "integrity": "sha1-W2E7eKGqJlWukI66Y4zJaiDfcgo=", "dev": true, "requires": {"@jest/environment": "^26.6.2", "@jest/types": "^26.6.2", "expect": "^26.6.2"}}, "@jest/reporters": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/reporters/download/@jest/reporters-26.6.2.tgz", "integrity": "sha1-H1GLmWN6Xxgwe9Ps+SdfaIKmZ/Y=", "dev": true, "requires": {"@bcoe/v8-coverage": "^0.2.3", "@jest/console": "^26.6.2", "@jest/test-result": "^26.6.2", "@jest/transform": "^26.6.2", "@jest/types": "^26.6.2", "chalk": "^4.0.0", "collect-v8-coverage": "^1.0.0", "exit": "^0.1.2", "glob": "^7.1.2", "graceful-fs": "^4.2.4", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-instrument": "^4.0.3", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.0", "istanbul-reports": "^3.0.2", "jest-haste-map": "^26.6.2", "jest-resolve": "^26.6.2", "jest-util": "^26.6.2", "jest-worker": "^26.6.2", "node-notifier": "^8.0.0", "slash": "^3.0.0", "source-map": "^0.6.0", "string-length": "^4.0.1", "terminal-link": "^2.0.0", "v8-to-istanbul": "^7.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "@jest/source-map": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/source-map/download/@jest/source-map-26.6.2.tgz", "integrity": "sha1-Ka9eHi4yTK/MyTbyGDCfVKtp1TU=", "dev": true, "requires": {"callsites": "^3.0.0", "graceful-fs": "^4.2.4", "source-map": "^0.6.0"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "@jest/test-result": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/test-result/download/@jest/test-result-26.6.2.tgz", "integrity": "sha1-VdpYti3xNFdsyVR276X3lJ4/Xxg=", "dev": true, "requires": {"@jest/console": "^26.6.2", "@jest/types": "^26.6.2", "@types/istanbul-lib-coverage": "^2.0.0", "collect-v8-coverage": "^1.0.0"}}, "@jest/test-sequencer": {"version": "26.6.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/test-sequencer/download/@jest/test-sequencer-26.6.3.tgz", "integrity": "sha1-mOikUQCGOIbQdCBej/3Fp+tYKxc=", "dev": true, "requires": {"@jest/test-result": "^26.6.2", "graceful-fs": "^4.2.4", "jest-haste-map": "^26.6.2", "jest-runner": "^26.6.3", "jest-runtime": "^26.6.3"}}, "@jest/transform": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/transform/download/@jest/transform-26.6.2.tgz", "integrity": "sha1-WsV8X6GtF7Kq6D5z5FgTiU3PLks=", "dev": true, "requires": {"@babel/core": "^7.1.0", "@jest/types": "^26.6.2", "babel-plugin-istanbul": "^6.0.0", "chalk": "^4.0.0", "convert-source-map": "^1.4.0", "fast-json-stable-stringify": "^2.0.0", "graceful-fs": "^4.2.4", "jest-haste-map": "^26.6.2", "jest-regex-util": "^26.0.0", "jest-util": "^26.6.2", "micromatch": "^4.0.2", "pirates": "^4.0.1", "slash": "^3.0.0", "source-map": "^0.6.1", "write-file-atomic": "^3.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "@jest/types": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/types/download/@jest/types-26.6.2.tgz", "integrity": "sha1-vvWlMgMOHYii9abZM/hOlyJu1I4=", "dev": true, "requires": {"@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^15.0.0", "chalk": "^4.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "@sinonjs/commons": {"version": "1.8.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@sinonjs/commons/download/@sinonjs/commons-1.8.1.tgz", "integrity": "sha1-598A+YogMyT23HzGBsrZ1KirIhc=", "dev": true, "requires": {"type-detect": "4.0.8"}}, "@sinonjs/fake-timers": {"version": "6.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@sinonjs/fake-timers/download/@sinonjs/fake-timers-6.0.1.tgz", "integrity": "sha1-KTZ0/MsyYqx4LHqt/eyoaxDHXEA=", "dev": true, "requires": {"@sinonjs/commons": "^1.7.0"}}, "@types/babel__core": {"version": "7.1.12", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/babel__core/download/@types/babel__core-7.1.12.tgz", "integrity": "sha1-TY6eUesmVVKn5PH/IhmrYTO9+y0=", "dev": true, "requires": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "@types/babel__generator": {"version": "7.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/babel__generator/download/@types/babel__generator-7.6.2.tgz", "integrity": "sha1-89cReOGHhY98ReMDgPjxt0FaEtg=", "dev": true, "requires": {"@babel/types": "^7.0.0"}}, "@types/babel__template": {"version": "7.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/babel__template/download/@types/babel__template-7.4.0.tgz", "integrity": "sha1-DIiN1ws+6e67bk8gDoCdoAdiYr4=", "dev": true, "requires": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "@types/babel__traverse": {"version": "7.11.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/babel__traverse/download/@types/babel__traverse-7.11.0.tgz", "integrity": "sha1-uaHvpjUgG6m8hQMjqHk+4tNsBKA=", "dev": true, "requires": {"@babel/types": "^7.3.0"}}, "@types/graceful-fs": {"version": "4.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/graceful-fs/download/@types/graceful-fs-4.1.4.tgz", "integrity": "sha1-T/n2QafG0aNQj/iLwxQbFSdy51M=", "dev": true, "requires": {"@types/node": "*"}}, "@types/istanbul-lib-coverage": {"version": "2.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.3.tgz", "integrity": "sha1-S6jdtyAiH0MuRDvV+RF/0iz9R2I=", "dev": true}, "@types/istanbul-lib-report": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/istanbul-lib-report/download/@types/istanbul-lib-report-3.0.0.tgz", "integrity": "sha1-wUwk8Y6oGQwRjudWK3/5mjZVJoY=", "dev": true, "requires": {"@types/istanbul-lib-coverage": "*"}}, "@types/istanbul-reports": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/istanbul-reports/download/@types/istanbul-reports-3.0.0.tgz", "integrity": "sha1-UIsTqjRPpJdiNOdd3cw0klc32CE=", "dev": true, "requires": {"@types/istanbul-lib-report": "*"}}, "@types/json5": {"version": "0.0.29", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/json5/download/@types/json5-0.0.29.tgz", "integrity": "sha1-7ihweulOEdK4J7y+UnC86n8+ce4=", "dev": true}, "@types/node": {"version": "14.14.16", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/node/download/@types/node-14.14.16.tgz", "integrity": "sha1-PMNR+NSBAd6t/tTJ5PEWBI1De0s=", "dev": true}, "@types/normalize-package-data": {"version": "2.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/normalize-package-data/download/@types/normalize-package-data-2.4.0.tgz", "integrity": "sha1-5IbQ2XOW15vu3QpuM/RTT/a0lz4=", "dev": true}, "@types/prettier": {"version": "2.1.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/prettier/download/@types/prettier-2.1.6.tgz", "integrity": "sha1-9LHvp4To20ec24sUQD4hRLHp/wM=", "dev": true}, "@types/stack-utils": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/stack-utils/download/@types/stack-utils-2.0.0.tgz", "integrity": "sha1-cDZkC04hzC8lmugmzoQ9J32tjP8=", "dev": true}, "@types/yargs": {"version": "15.0.12", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/yargs/download/@types/yargs-15.0.12.tgz", "integrity": "sha1-YjTOPj4/oyxdswGhcPlqWZyWDXQ=", "dev": true, "requires": {"@types/yargs-parser": "*"}}, "@types/yargs-parser": {"version": "20.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/yargs-parser/download/@types/yargs-parser-20.2.0.tgz", "integrity": "sha1-3T5mmboyN/A0jNCF5GmHgCBIQvk=", "dev": true}, "abab": {"version": "2.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/abab/download/abab-2.0.5.tgz", "integrity": "sha1-wLZ4+zLWD8EhnHhNaoJv44Wut5o=", "dev": true}, "acorn": {"version": "7.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/acorn/download/acorn-7.4.1.tgz", "integrity": "sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=", "dev": true}, "acorn-globals": {"version": "6.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/acorn-globals/download/acorn-globals-6.0.0.tgz", "integrity": "sha1-Rs3Tnw+P8IqHZhm1X1rIptx3C0U=", "dev": true, "requires": {"acorn": "^7.1.1", "acorn-walk": "^7.1.1"}}, "acorn-jsx": {"version": "5.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/acorn-jsx/download/acorn-jsx-5.3.1.tgz", "integrity": "sha1-/IZh4Rt6wVOcR9v+oucrOvNNJns=", "dev": true}, "acorn-walk": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/acorn-walk/download/acorn-walk-7.2.0.tgz", "integrity": "sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w=", "dev": true}, "ajv": {"version": "6.12.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ajv/download/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "dev": true, "requires": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ansi-colors": {"version": "4.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-colors/download/ansi-colors-4.1.1.tgz", "integrity": "sha1-y7muJWv3UK8eqzRPIpqif+lLo0g=", "dev": true}, "ansi-escapes": {"version": "4.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-escapes/download/ansi-escapes-4.3.1.tgz", "integrity": "sha1-pcR8xDGB8fOP/XB2g3cA05VSKmE=", "dev": true, "requires": {"type-fest": "^0.11.0"}, "dependencies": {"type-fest": {"version": "0.11.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/type-fest/download/type-fest-0.11.0.tgz", "integrity": "sha1-l6vwhyMQ/tiKXEZrJWgVdhReM/E=", "dev": true}}}, "ansi-regex": {"version": "5.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-regex/download/ansi-regex-5.0.0.tgz", "integrity": "sha1-OIU59VF5vzkznIGvMKZU1p+Hy3U=", "dev": true}, "ansi-styles": {"version": "3.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "anymatch": {"version": "3.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/anymatch/download/anymatch-3.1.1.tgz", "integrity": "sha1-xV7PAhheJGklk5kxDBc84xIzsUI=", "dev": true, "requires": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}}, "argparse": {"version": "1.0.10", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/argparse/download/argparse-1.0.10.tgz", "integrity": "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=", "dev": true, "requires": {"sprintf-js": "~1.0.2"}}, "arr-diff": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/arr-diff/download/arr-diff-4.0.0.tgz", "integrity": "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=", "dev": true}, "arr-flatten": {"version": "1.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/arr-flatten/download/arr-flatten-1.1.0.tgz", "integrity": "sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=", "dev": true}, "arr-union": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/arr-union/download/arr-union-3.1.0.tgz", "integrity": "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=", "dev": true}, "array-includes": {"version": "3.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/array-includes/download/array-includes-3.1.2.tgz", "integrity": "sha1-qNsD4LiMjGrt3EnLEy+byrTr+cg=", "dev": true, "requires": {"call-bind": "^1.0.0", "define-properties": "^1.1.3", "es-abstract": "^1.18.0-next.1", "get-intrinsic": "^1.0.1", "is-string": "^1.0.5"}}, "array-unique": {"version": "0.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/array-unique/download/array-unique-0.3.2.tgz", "integrity": "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=", "dev": true}, "array.prototype.flat": {"version": "1.2.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/array.prototype.flat/download/array.prototype.flat-1.2.4.tgz", "integrity": "sha1-bvY4tDMSvUAbTGGZ/ex+LcnpoSM=", "dev": true, "requires": {"call-bind": "^1.0.0", "define-properties": "^1.1.3", "es-abstract": "^1.18.0-next.1"}}, "asn1": {"version": "0.2.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/asn1/download/asn1-0.2.4.tgz", "integrity": "sha1-jSR136tVO7M+d7VOWeiAu4ziMTY=", "dev": true, "requires": {"safer-buffer": "~2.1.0"}}, "assert-plus": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/assert-plus/download/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=", "dev": true}, "assign-symbols": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/assign-symbols/download/assign-symbols-1.0.0.tgz", "integrity": "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=", "dev": true}, "astral-regex": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/astral-regex/download/astral-regex-2.0.0.tgz", "integrity": "sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=", "dev": true}, "asynckit": {"version": "0.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/asynckit/download/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k=", "dev": true}, "atob": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/atob/download/atob-2.1.2.tgz", "integrity": "sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=", "dev": true}, "aws-sign2": {"version": "0.7.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/aws-sign2/download/aws-sign2-0.7.0.tgz", "integrity": "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=", "dev": true}, "aws4": {"version": "1.11.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/aws4/download/aws4-1.11.0.tgz", "integrity": "sha1-1h9G2DslGSUOJ4Ta9bCUeai0HFk=", "dev": true}, "axios": {"version": "0.20.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/axios/download/axios-0.20.0.tgz", "integrity": "sha1-BXujDwSIRpSZOozQf6OUz/EcUL0=", "requires": {"follow-redirects": "^1.10.0"}}, "babel-eslint": {"version": "10.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-eslint/download/babel-eslint-10.1.0.tgz", "integrity": "sha1-aWjlaKkQt4+zd5zdi2rC9HmUMjI=", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "@babel/parser": "^7.7.0", "@babel/traverse": "^7.7.0", "@babel/types": "^7.7.0", "eslint-visitor-keys": "^1.0.0", "resolve": "^1.12.0"}}, "babel-jest": {"version": "26.6.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-jest/download/babel-jest-26.6.3.tgz", "integrity": "sha1-2H0lywA3V3oMifguV1XF0pPAEFY=", "dev": true, "requires": {"@jest/transform": "^26.6.2", "@jest/types": "^26.6.2", "@types/babel__core": "^7.1.7", "babel-plugin-istanbul": "^6.0.0", "babel-preset-jest": "^26.6.2", "chalk": "^4.0.0", "graceful-fs": "^4.2.4", "slash": "^3.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "babel-plugin-istanbul": {"version": "6.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-istanbul/download/babel-plugin-istanbul-6.0.0.tgz", "integrity": "sha1-4VnM3Jr5XgtXDHW0Vzt8NNZx12U=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^4.0.0", "test-exclude": "^6.0.0"}}, "babel-plugin-jest-hoist": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-26.6.2.tgz", "integrity": "sha1-gYW9AwNI0lTG192XQ1Xmoosh5i0=", "dev": true, "requires": {"@babel/template": "^7.3.3", "@babel/types": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}}, "babel-preset-current-node-syntax": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-preset-current-node-syntax/download/babel-preset-current-node-syntax-1.0.1.tgz", "integrity": "sha1-tDmSObibKgEfndvj5PQB/EDP9zs=", "dev": true, "requires": {"@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-class-properties": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.8.3", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.8.3", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.8.3", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-top-level-await": "^7.8.3"}}, "babel-preset-jest": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-preset-jest/download/babel-preset-jest-26.6.2.tgz", "integrity": "sha1-dHhysRcd8DIlJCZYaIHWLTF5j+4=", "dev": true, "requires": {"babel-plugin-jest-hoist": "^26.6.2", "babel-preset-current-node-syntax": "^1.0.0"}}, "balanced-match": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/balanced-match/download/balanced-match-1.0.0.tgz", "integrity": "sha1-ibTRmasr7kneFk6gK4nORi1xt2c=", "dev": true}, "base": {"version": "0.11.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/base/download/base-0.11.2.tgz", "integrity": "sha1-e95c7RRbbVUakNuH+DxVi060io8=", "dev": true, "requires": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/download/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-descriptor/download/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "bcrypt-pbkdf": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=", "dev": true, "requires": {"tweetnacl": "^0.14.3"}}, "brace-expansion": {"version": "1.1.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/brace-expansion/download/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "3.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/braces/download/braces-3.0.2.tgz", "integrity": "sha1-NFThpGLujVmeI23zNs2epPiv4Qc=", "dev": true, "requires": {"fill-range": "^7.0.1"}}, "browser-process-hrtime": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/browser-process-hrtime/download/browser-process-hrtime-1.0.0.tgz", "integrity": "sha1-PJtLfXgsgSHlbxAQbYTA0P/JRiY=", "dev": true}, "bser": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bser/download/bser-2.1.1.tgz", "integrity": "sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=", "dev": true, "requires": {"node-int64": "^0.4.0"}}, "buffer-from": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/buffer-from/download/buffer-from-1.1.1.tgz", "integrity": "sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8=", "dev": true}, "cache-base": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cache-base/download/cache-base-1.0.1.tgz", "integrity": "sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=", "dev": true, "requires": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}}, "call-bind": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/call-bind/download/call-bind-1.0.0.tgz", "integrity": "sha1-JBJwVLs/m9y0sfuCQYGGBy93uM4=", "dev": true, "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.0"}}, "callsites": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/callsites/download/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=", "dev": true}, "camelcase": {"version": "5.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/camelcase/download/camelcase-5.3.1.tgz", "integrity": "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=", "dev": true}, "capture-exit": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/capture-exit/download/capture-exit-2.0.0.tgz", "integrity": "sha1-+5U7+uvreB9iiYI52rtCbQilCaQ=", "dev": true, "requires": {"rsvp": "^4.8.4"}}, "caseless": {"version": "0.12.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/caseless/download/caseless-0.12.0.tgz", "integrity": "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=", "dev": true}, "chalk": {"version": "2.4.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "char-regex": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/char-regex/download/char-regex-1.0.2.tgz", "integrity": "sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8=", "dev": true}, "charenc": {"version": "0.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/charenc/download/charenc-0.0.2.tgz", "integrity": "sha1-wKHS86cJLgN3S/qD8UwPxXkKhmc="}, "ci-info": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ci-info/download/ci-info-2.0.0.tgz", "integrity": "sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y=", "dev": true}, "cjs-module-lexer": {"version": "0.6.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cjs-module-lexer/download/cjs-module-lexer-0.6.0.tgz", "integrity": "sha1-QYb8yg6uF1lwruhwuf4tbPjVZV8=", "dev": true}, "class-utils": {"version": "0.3.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/class-utils/download/class-utils-0.3.6.tgz", "integrity": "sha1-+TNprouafOAv1B+q0MqDAzGQxGM=", "dev": true, "requires": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/download/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}}}, "cliui": {"version": "6.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cliui/download/cliui-6.0.0.tgz", "integrity": "sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE=", "dev": true, "requires": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^6.2.0"}}, "co": {"version": "4.6.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/co/download/co-4.6.0.tgz", "integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=", "dev": true}, "collect-v8-coverage": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/collect-v8-coverage/download/collect-v8-coverage-1.0.1.tgz", "integrity": "sha1-zCyOlPwYu9/+ZNZTRXDIpnOyf1k=", "dev": true}, "collection-visit": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/collection-visit/download/collection-visit-1.0.0.tgz", "integrity": "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=", "dev": true, "requires": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}}, "color-convert": {"version": "1.9.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "dev": true, "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=", "dev": true}, "combined-stream": {"version": "1.0.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/combined-stream/download/combined-stream-1.0.8.tgz", "integrity": "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=", "dev": true, "requires": {"delayed-stream": "~1.0.0"}}, "component-emitter": {"version": "1.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/component-emitter/download/component-emitter-1.3.0.tgz", "integrity": "sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=", "dev": true}, "concat-map": {"version": "0.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/concat-map/download/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true}, "confusing-browser-globals": {"version": "1.0.10", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/confusing-browser-globals/download/confusing-browser-globals-1.0.10.tgz", "integrity": "sha1-MNHn89G4grJexJM9HRraw1PSClk=", "dev": true}, "contains-path": {"version": "0.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/contains-path/download/contains-path-0.1.0.tgz", "integrity": "sha1-/ozxhP9mcLa67wGp1IYaXL7EEgo=", "dev": true}, "convert-source-map": {"version": "1.7.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/convert-source-map/download/convert-source-map-1.7.0.tgz", "integrity": "sha1-F6LLiC1/d9NJBYXizmxSRCSjpEI=", "dev": true, "requires": {"safe-buffer": "~5.1.1"}}, "copy-descriptor": {"version": "0.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/copy-descriptor/download/copy-descriptor-0.1.1.tgz", "integrity": "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=", "dev": true}, "core-util-is": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/core-util-is/download/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=", "dev": true}, "cross-spawn": {"version": "7.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cross-spawn/download/cross-spawn-7.0.3.tgz", "integrity": "sha1-9zqFudXUHQRVUcF34ogtSshXKKY=", "dev": true, "requires": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}}, "crypt": {"version": "0.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/crypt/download/crypt-0.0.2.tgz", "integrity": "sha1-iNf/fsDfuG9xPch7u0LQRNPmxBs="}, "cssom": {"version": "0.4.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cssom/download/cssom-0.4.4.tgz", "integrity": "sha1-WmbPk9LQtmHYC/akT7ZfXC5OChA=", "dev": true}, "cssstyle": {"version": "2.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cssstyle/download/cssstyle-2.3.0.tgz", "integrity": "sha1-/2ZaDdvcMYZLCWR/NBY0Q9kLCFI=", "dev": true, "requires": {"cssom": "~0.3.6"}, "dependencies": {"cssom": {"version": "0.3.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cssom/download/cssom-0.3.8.tgz", "integrity": "sha1-nxJ29bK0Y/IRTT8sdSUK+MGjb0o=", "dev": true}}}, "dashdash": {"version": "1.14.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/dashdash/download/dashdash-1.14.1.tgz", "integrity": "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=", "dev": true, "requires": {"assert-plus": "^1.0.0"}}, "data-urls": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/data-urls/download/data-urls-2.0.0.tgz", "integrity": "sha1-FWSFpyljqXD11YIar2Qr7yvy25s=", "dev": true, "requires": {"abab": "^2.0.3", "whatwg-mimetype": "^2.3.0", "whatwg-url": "^8.0.0"}}, "debug": {"version": "4.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/download/debug-4.3.1.tgz", "integrity": "sha1-8NIpxQXgxtjEmsVT0bE9wYP2su4=", "dev": true, "requires": {"ms": "2.1.2"}}, "decamelize": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/decamelize/download/decamelize-1.2.0.tgz", "integrity": "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=", "dev": true}, "decimal.js": {"version": "10.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/decimal.js/download/decimal.js-10.2.1.tgz", "integrity": "sha1-I4rnsPDHk9PjzqQQEIs1osAUJqM=", "dev": true}, "decode-uri-component": {"version": "0.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/decode-uri-component/download/decode-uri-component-0.2.0.tgz", "integrity": "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=", "dev": true}, "deep-is": {"version": "0.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/deep-is/download/deep-is-0.1.3.tgz", "integrity": "sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=", "dev": true}, "deepmerge": {"version": "4.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/deepmerge/download/deepmerge-4.2.2.tgz", "integrity": "sha1-RNLqNnm49NT/ujPwPYZfwee/SVU=", "dev": true}, "define-properties": {"version": "1.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-properties/download/define-properties-1.1.3.tgz", "integrity": "sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=", "dev": true, "requires": {"object-keys": "^1.0.12"}}, "define-property": {"version": "2.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/download/define-property-2.0.2.tgz", "integrity": "sha1-1Flono1lS6d+AqgX+HENcCyxbp0=", "dev": true, "requires": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "dependencies": {"is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-descriptor/download/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "delayed-stream": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/delayed-stream/download/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk=", "dev": true}, "detect-newline": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/detect-newline/download/detect-newline-3.1.0.tgz", "integrity": "sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE=", "dev": true}, "diff-sequences": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/diff-sequences/download/diff-sequences-26.6.2.tgz", "integrity": "sha1-SLqZFX3hkjQS7tQdtrbUqpynwLE=", "dev": true}, "doctrine": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/doctrine/download/doctrine-3.0.0.tgz", "integrity": "sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=", "dev": true, "requires": {"esutils": "^2.0.2"}}, "domexception": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/domexception/download/domexception-2.0.1.tgz", "integrity": "sha1-+0Su+6eT4VdLCvau0oAdBXUp8wQ=", "dev": true, "requires": {"webidl-conversions": "^5.0.0"}, "dependencies": {"webidl-conversions": {"version": "5.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/webidl-conversions/download/webidl-conversions-5.0.0.tgz", "integrity": "sha1-rlnIoAsSFUOirMZcBDT1ew/BGv8=", "dev": true}}}, "ecc-jsbn": {"version": "0.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz", "integrity": "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=", "dev": true, "requires": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "emittery": {"version": "0.7.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/emittery/download/emittery-0.7.2.tgz", "integrity": "sha1-JVlZCOE68PVnSrQZOW4vs5TN+oI=", "dev": true}, "emoji-regex": {"version": "8.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/emoji-regex/download/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "dev": true}, "end-of-stream": {"version": "1.4.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/end-of-stream/download/end-of-stream-1.4.4.tgz", "integrity": "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=", "dev": true, "requires": {"once": "^1.4.0"}}, "enquirer": {"version": "2.3.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/enquirer/download/enquirer-2.3.6.tgz", "integrity": "sha1-Kn/l3WNKHkElqXXsmU/1RW3Dc00=", "dev": true, "requires": {"ansi-colors": "^4.1.1"}}, "error-ex": {"version": "1.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/error-ex/download/error-ex-1.3.2.tgz", "integrity": "sha1-tKxAZIEH/c3PriQvQovqihTU8b8=", "dev": true, "requires": {"is-arrayish": "^0.2.1"}}, "es-abstract": {"version": "1.18.0-next.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/es-abstract/download/es-abstract-1.18.0-next.1.tgz", "integrity": "sha1-bjoKS9pxflAjqzuOkL7DYQjSLGg=", "dev": true, "requires": {"es-to-primitive": "^1.2.1", "function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1", "is-callable": "^1.2.2", "is-negative-zero": "^2.0.0", "is-regex": "^1.1.1", "object-inspect": "^1.8.0", "object-keys": "^1.1.1", "object.assign": "^4.1.1", "string.prototype.trimend": "^1.0.1", "string.prototype.trimstart": "^1.0.1"}}, "es-to-primitive": {"version": "1.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/es-to-primitive/download/es-to-primitive-1.2.1.tgz", "integrity": "sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=", "dev": true, "requires": {"is-callable": "^1.1.4", "is-date-object": "^1.0.1", "is-symbol": "^1.0.2"}}, "escape-string-regexp": {"version": "1.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=", "dev": true}, "escodegen": {"version": "1.14.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/escodegen/download/escodegen-1.14.3.tgz", "integrity": "sha1-TnuB+6YVgdyXWC7XjKt/Do1j9QM=", "dev": true, "requires": {"esprima": "^4.0.1", "estraverse": "^4.2.0", "esutils": "^2.0.2", "optionator": "^0.8.1", "source-map": "~0.6.1"}, "dependencies": {"levn": {"version": "0.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/levn/download/levn-0.3.0.tgz", "integrity": "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=", "dev": true, "requires": {"prelude-ls": "~1.1.2", "type-check": "~0.3.2"}}, "optionator": {"version": "0.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/optionator/download/optionator-0.8.3.tgz", "integrity": "sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=", "dev": true, "requires": {"deep-is": "~0.1.3", "fast-levenshtein": "~2.0.6", "levn": "~0.3.0", "prelude-ls": "~1.1.2", "type-check": "~0.3.2", "word-wrap": "~1.2.3"}}, "prelude-ls": {"version": "1.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/prelude-ls/download/prelude-ls-1.1.2.tgz", "integrity": "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=", "dev": true}, "source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "optional": true}, "type-check": {"version": "0.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/type-check/download/type-check-0.3.2.tgz", "integrity": "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=", "dev": true, "requires": {"prelude-ls": "~1.1.2"}}}}, "eslint": {"version": "7.16.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/eslint/download/eslint-7.16.0.tgz", "integrity": "sha1-p2FgW/mnsy0ku3zeWa6w/XbwYJI=", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "@eslint/eslintrc": "^0.2.2", "ajv": "^6.10.0", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.0.1", "doctrine": "^3.0.0", "enquirer": "^2.3.5", "eslint-scope": "^5.1.1", "eslint-utils": "^2.1.0", "eslint-visitor-keys": "^2.0.0", "espree": "^7.3.1", "esquery": "^1.2.0", "esutils": "^2.0.2", "file-entry-cache": "^6.0.0", "functional-red-black-tree": "^1.0.1", "glob-parent": "^5.0.0", "globals": "^12.1.0", "ignore": "^4.0.6", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "js-yaml": "^3.13.1", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash": "^4.17.19", "minimatch": "^3.0.4", "natural-compare": "^1.4.0", "optionator": "^0.9.1", "progress": "^2.0.0", "regexpp": "^3.1.0", "semver": "^7.2.1", "strip-ansi": "^6.0.0", "strip-json-comments": "^3.1.0", "table": "^6.0.4", "text-table": "^0.2.0", "v8-compile-cache": "^2.0.3"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "eslint-visitor-keys": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/eslint-visitor-keys/download/eslint-visitor-keys-2.0.0.tgz", "integrity": "sha1-If3I+82ceVzAMh8FY3AglXUVEag=", "dev": true}, "globals": {"version": "12.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/globals/download/globals-12.4.0.tgz", "integrity": "sha1-oYgTV2pBsAokqX5/gVkYwuGZJfg=", "dev": true, "requires": {"type-fest": "^0.8.1"}}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "eslint-config-airbnb-base": {"version": "14.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/eslint-config-airbnb-base/download/eslint-config-airbnb-base-14.2.1.tgz", "integrity": "sha1-ii6zhFXcWjElUBk7MZza7vBCzR4=", "dev": true, "requires": {"confusing-browser-globals": "^1.0.10", "object.assign": "^4.1.2", "object.entries": "^1.1.2"}}, "eslint-import-resolver-node": {"version": "0.3.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.4.tgz", "integrity": "sha1-hf+oGULCUBLYIxCW3fZ5wDBCxxc=", "dev": true, "requires": {"debug": "^2.6.9", "resolve": "^1.13.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "eslint-module-utils": {"version": "2.6.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/eslint-module-utils/download/eslint-module-utils-2.6.0.tgz", "integrity": "sha1-V569CU9Wr3eX0ZyYZsnJSGYpv6Y=", "dev": true, "requires": {"debug": "^2.6.9", "pkg-dir": "^2.0.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "eslint-plugin-import": {"version": "2.22.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/eslint-plugin-import/download/eslint-plugin-import-2.22.1.tgz", "integrity": "sha1-CJbH5qDPRBCaLZe5WQPCu2iddwI=", "dev": true, "requires": {"array-includes": "^3.1.1", "array.prototype.flat": "^1.2.3", "contains-path": "^0.1.0", "debug": "^2.6.9", "doctrine": "1.5.0", "eslint-import-resolver-node": "^0.3.4", "eslint-module-utils": "^2.6.0", "has": "^1.0.3", "minimatch": "^3.0.4", "object.values": "^1.1.1", "read-pkg-up": "^2.0.0", "resolve": "^1.17.0", "tsconfig-paths": "^3.9.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "doctrine": {"version": "1.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/doctrine/download/doctrine-1.5.0.tgz", "integrity": "sha1-N53Ocw9hZvds76TmcHoVmwLFpvo=", "dev": true, "requires": {"esutils": "^2.0.2", "isarray": "^1.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "eslint-scope": {"version": "5.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/eslint-scope/download/eslint-scope-5.1.1.tgz", "integrity": "sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=", "dev": true, "requires": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}}, "eslint-utils": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/eslint-utils/download/eslint-utils-2.1.0.tgz", "integrity": "sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=", "dev": true, "requires": {"eslint-visitor-keys": "^1.1.0"}}, "eslint-visitor-keys": {"version": "1.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz", "integrity": "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=", "dev": true}, "espree": {"version": "7.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/espree/download/espree-7.3.1.tgz", "integrity": "sha1-8t8zC3Usb1UBn4vYm3ZgA5wbu7Y=", "dev": true, "requires": {"acorn": "^7.4.0", "acorn-jsx": "^5.3.1", "eslint-visitor-keys": "^1.3.0"}}, "esprima": {"version": "4.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/esprima/download/esprima-4.0.1.tgz", "integrity": "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=", "dev": true}, "esquery": {"version": "1.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/esquery/download/esquery-1.3.1.tgz", "integrity": "sha1-t4tYKKqOIU4p+3TE1bdS4cAz2lc=", "dev": true, "requires": {"estraverse": "^5.1.0"}, "dependencies": {"estraverse": {"version": "5.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/estraverse/download/estraverse-5.2.0.tgz", "integrity": "sha1-MH30JUfmzHMk088DwVXVzbjFOIA=", "dev": true}}}, "esrecurse": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/esrecurse/download/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "dev": true, "requires": {"estraverse": "^5.2.0"}, "dependencies": {"estraverse": {"version": "5.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/estraverse/download/estraverse-5.2.0.tgz", "integrity": "sha1-MH30JUfmzHMk088DwVXVzbjFOIA=", "dev": true}}}, "estraverse": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/estraverse/download/estraverse-4.3.0.tgz", "integrity": "sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=", "dev": true}, "esutils": {"version": "2.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/esutils/download/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=", "dev": true}, "exec-sh": {"version": "0.3.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/exec-sh/download/exec-sh-0.3.4.tgz", "integrity": "sha1-OgGM61JsxvbfK7UEsr/o46STTsU=", "dev": true}, "execa": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/execa/download/execa-1.0.0.tgz", "integrity": "sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=", "dev": true, "requires": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "dependencies": {"cross-spawn": {"version": "6.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cross-spawn/download/cross-spawn-6.0.5.tgz", "integrity": "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=", "dev": true, "requires": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "path-key": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-key/download/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=", "dev": true}, "semver": {"version": "5.7.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/download/semver-5.7.1.tgz", "integrity": "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=", "dev": true}, "shebang-command": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/shebang-command/download/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "dev": true, "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/shebang-regex/download/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=", "dev": true}, "which": {"version": "1.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/which/download/which-1.3.1.tgz", "integrity": "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=", "dev": true, "requires": {"isexe": "^2.0.0"}}}}, "exit": {"version": "0.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/exit/download/exit-0.1.2.tgz", "integrity": "sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=", "dev": true}, "expand-brackets": {"version": "2.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/expand-brackets/download/expand-brackets-2.1.4.tgz", "integrity": "sha1-t3c14xXOMPa27/D4OwQVGiJEliI=", "dev": true, "requires": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/download/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "ms": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "expect": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/expect/download/expect-26.6.2.tgz", "integrity": "sha1-xrmWvya/P+GLZ7LQ9R/JgbqTRBc=", "dev": true, "requires": {"@jest/types": "^26.6.2", "ansi-styles": "^4.0.0", "jest-get-type": "^26.3.0", "jest-matcher-utils": "^26.6.2", "jest-message-util": "^26.6.2", "jest-regex-util": "^26.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}}}, "extend": {"version": "3.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend/download/extend-3.0.2.tgz", "integrity": "sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=", "dev": true}, "extend-shallow": {"version": "3.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/download/extend-shallow-3.0.2.tgz", "integrity": "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=", "dev": true, "requires": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-extendable/download/is-extendable-1.0.1.tgz", "integrity": "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=", "dev": true, "requires": {"is-plain-object": "^2.0.4"}}}}, "extglob": {"version": "2.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extglob/download/extglob-2.0.4.tgz", "integrity": "sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=", "dev": true, "requires": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/download/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-descriptor/download/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "extsprintf": {"version": "1.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extsprintf/download/extsprintf-1.3.0.tgz", "integrity": "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=", "dev": true}, "fast-deep-equal": {"version": "3.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=", "dev": true}, "fast-json-stable-stringify": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=", "dev": true}, "fast-levenshtein": {"version": "2.0.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=", "dev": true}, "fb-watchman": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fb-watchman/download/fb-watchman-2.0.1.tgz", "integrity": "sha1-/IT7OdJwnPP/bXQ3BhV7tXCKioU=", "dev": true, "requires": {"bser": "2.1.1"}}, "file-entry-cache": {"version": "6.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/file-entry-cache/download/file-entry-cache-6.0.0.tgz", "integrity": "sha1-eSGonDkcbZPv7CFprGvzAMUn6go=", "dev": true, "requires": {"flat-cache": "^3.0.4"}}, "fill-range": {"version": "7.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fill-range/download/fill-range-7.0.1.tgz", "integrity": "sha1-GRmmp8df44ssfHflGYU12prN2kA=", "dev": true, "requires": {"to-regex-range": "^5.0.1"}}, "find-up": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/find-up/download/find-up-2.1.0.tgz", "integrity": "sha1-RdG35QbHF93UgndaK3eSCjwMV6c=", "dev": true, "requires": {"locate-path": "^2.0.0"}}, "flat-cache": {"version": "3.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/flat-cache/download/flat-cache-3.0.4.tgz", "integrity": "sha1-YbAzgwKy/p+Vfcwy/CqH8cMEixE=", "dev": true, "requires": {"flatted": "^3.1.0", "rimraf": "^3.0.2"}}, "flatted": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/flatted/download/flatted-3.1.0.tgz", "integrity": "sha1-pdBrSosB46Y3cdqly3oZA+LlcGc=", "dev": true}, "follow-redirects": {"version": "1.13.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/follow-redirects/download/follow-redirects-1.13.1.tgz", "integrity": "sha1-X2m4Ezds7k/QR0o6uoNd8Eq3Y7c="}, "for-in": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/for-in/download/for-in-1.0.2.tgz", "integrity": "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=", "dev": true}, "foreachasync": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/foreachasync/download/foreachasync-3.0.0.tgz", "integrity": "sha1-VQKYfchxS+M5IJfzLgBxyd7gfPY="}, "forever-agent": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/forever-agent/download/forever-agent-0.6.1.tgz", "integrity": "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=", "dev": true}, "form-data": {"version": "2.3.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/form-data/download/form-data-2.3.3.tgz", "integrity": "sha1-3M5SwF9kTymManq5Nr1yTO/786Y=", "dev": true, "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}}, "fragment-cache": {"version": "0.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fragment-cache/download/fragment-cache-0.2.1.tgz", "integrity": "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=", "dev": true, "requires": {"map-cache": "^0.2.2"}}, "fs.realpath": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fs.realpath/download/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "dev": true}, "fsevents": {"version": "2.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fsevents/download/fsevents-2.2.1.tgz", "integrity": "sha1-H7At7SA2qKwojVB6ZZYr2HuXYo0=", "dev": true, "optional": true}, "function-bind": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/function-bind/download/function-bind-1.1.1.tgz", "integrity": "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=", "dev": true}, "functional-red-black-tree": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz", "integrity": "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=", "dev": true}, "gensync": {"version": "1.0.0-beta.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/gensync/download/gensync-1.0.0-beta.2.tgz", "integrity": "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=", "dev": true}, "get-caller-file": {"version": "2.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/get-caller-file/download/get-caller-file-2.0.5.tgz", "integrity": "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=", "dev": true}, "get-intrinsic": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/get-intrinsic/download/get-intrinsic-1.0.2.tgz", "integrity": "sha1-aCDaIm5QskiU4IhZRp3Gg2FUXUk=", "dev": true, "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1"}}, "get-package-type": {"version": "0.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/get-package-type/download/get-package-type-0.1.0.tgz", "integrity": "sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro=", "dev": true}, "get-stream": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/get-stream/download/get-stream-4.1.0.tgz", "integrity": "sha1-wbJVV189wh1Zv8ec09K0axw6VLU=", "dev": true, "requires": {"pump": "^3.0.0"}}, "get-value": {"version": "2.0.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/get-value/download/get-value-2.0.6.tgz", "integrity": "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=", "dev": true}, "getpass": {"version": "0.1.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/getpass/download/getpass-0.1.7.tgz", "integrity": "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=", "dev": true, "requires": {"assert-plus": "^1.0.0"}}, "glob": {"version": "7.1.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/glob/download/glob-7.1.6.tgz", "integrity": "sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY=", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-parent": {"version": "5.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/glob-parent/download/glob-parent-5.1.1.tgz", "integrity": "sha1-tsHvQXxOVmPqSY8cRa+saRa7wik=", "dev": true, "requires": {"is-glob": "^4.0.1"}}, "globals": {"version": "11.12.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/globals/download/globals-11.12.0.tgz", "integrity": "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=", "dev": true}, "graceful-fs": {"version": "4.2.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/graceful-fs/download/graceful-fs-4.2.4.tgz", "integrity": "sha1-Ila94U02MpWMRl68ltxGfKB6Kfs=", "dev": true}, "growly": {"version": "1.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/growly/download/growly-1.3.0.tgz", "integrity": "sha1-8QdIy+dq+WS3yWyTxrzCivEgwIE=", "dev": true, "optional": true}, "har-schema": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/har-schema/download/har-schema-2.0.0.tgz", "integrity": "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=", "dev": true}, "har-validator": {"version": "5.1.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/har-validator/download/har-validator-5.1.5.tgz", "integrity": "sha1-HwgDufjLIMD6E4It8ezds2veHv0=", "dev": true, "requires": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}}, "has": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has/download/has-1.0.3.tgz", "integrity": "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=", "dev": true, "requires": {"function-bind": "^1.1.1"}}, "has-flag": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "dev": true}, "has-symbols": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-symbols/download/has-symbols-1.0.1.tgz", "integrity": "sha1-n1IUdYpEGWxAbZvXbOv4HsLdMeg=", "dev": true}, "has-value": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-value/download/has-value-1.0.0.tgz", "integrity": "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=", "dev": true, "requires": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}}, "has-values": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-values/download/has-values-1.0.0.tgz", "integrity": "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=", "dev": true, "requires": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "dependencies": {"is-number": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-number/download/is-number-3.0.0.tgz", "integrity": "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "kind-of": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/download/kind-of-4.0.0.tgz", "integrity": "sha1-IIE989cSkosgc3hpGkUGb65y3Vc=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "hosted-git-info": {"version": "2.8.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/hosted-git-info/download/hosted-git-info-2.8.8.tgz", "integrity": "sha1-dTm9S8Hg4KiVgVouAmJCCxKFhIg=", "dev": true}, "html-encoding-sniffer": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/html-encoding-sniffer/download/html-encoding-sniffer-2.0.1.tgz", "integrity": "sha1-QqbcT9M/ACgRduiyN1nKTk+hhfM=", "dev": true, "requires": {"whatwg-encoding": "^1.0.5"}}, "html-escaper": {"version": "2.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/html-escaper/download/html-escaper-2.0.2.tgz", "integrity": "sha1-39YAJ9o2o238viNiYsAKWCJoFFM=", "dev": true}, "http-signature": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/http-signature/download/http-signature-1.2.0.tgz", "integrity": "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=", "dev": true, "requires": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}}, "human-signals": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/human-signals/download/human-signals-1.1.1.tgz", "integrity": "sha1-xbHNFPUK6uCatsWf5jujOV/k36M=", "dev": true}, "iconv-lite": {"version": "0.4.24", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/iconv-lite/download/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "dev": true, "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "ignore": {"version": "4.0.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ignore/download/ignore-4.0.6.tgz", "integrity": "sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=", "dev": true}, "import-fresh": {"version": "3.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/import-fresh/download/import-fresh-3.3.0.tgz", "integrity": "sha1-NxYsJfy566oublPVtNiM4X2eDCs=", "dev": true, "requires": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}}, "import-local": {"version": "3.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/import-local/download/import-local-3.0.2.tgz", "integrity": "sha1-qM/QQx0d5KIZlwPQA+PmI2T6bbY=", "dev": true, "requires": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}, "dependencies": {"find-up": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/find-up/download/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "dev": true, "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}, "locate-path": {"version": "5.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/locate-path/download/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "dev": true, "requires": {"p-locate": "^4.1.0"}}, "p-limit": {"version": "2.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-limit/download/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-locate/download/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "dev": true, "requires": {"p-limit": "^2.2.0"}}, "p-try": {"version": "2.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-try/download/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "dev": true}, "path-exists": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-exists/download/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "dev": true}, "pkg-dir": {"version": "4.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pkg-dir/download/pkg-dir-4.2.0.tgz", "integrity": "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=", "dev": true, "requires": {"find-up": "^4.0.0"}}}}, "imurmurhash": {"version": "0.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/imurmurhash/download/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true}, "inflight": {"version": "1.0.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/inflight/download/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "dev": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/inherits/download/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "dev": true}, "ip-regex": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ip-regex/download/ip-regex-2.1.0.tgz", "integrity": "sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk=", "dev": true}, "is-accessor-descriptor": {"version": "0.1.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz", "integrity": "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "is-arrayish": {"version": "0.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-arrayish/download/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=", "dev": true}, "is-buffer": {"version": "1.1.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-buffer/download/is-buffer-1.1.6.tgz", "integrity": "sha1-76ouqdqg16suoTqXsritUf776L4="}, "is-callable": {"version": "1.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-callable/download/is-callable-1.2.2.tgz", "integrity": "sha1-x8ZxXNItTdtI0+GZcCI6zquwgNk=", "dev": true}, "is-ci": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-ci/download/is-ci-2.0.0.tgz", "integrity": "sha1-a8YzQYGBDgS1wis9WJ/cpVAmQEw=", "dev": true, "requires": {"ci-info": "^2.0.0"}}, "is-core-module": {"version": "2.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-core-module/download/is-core-module-2.2.0.tgz", "integrity": "sha1-lwN+89UiJNhRY/VZeytj2a/tmBo=", "dev": true, "requires": {"has": "^1.0.3"}}, "is-data-descriptor": {"version": "0.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz", "integrity": "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "is-date-object": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-date-object/download/is-date-object-1.0.2.tgz", "integrity": "sha1-vac28s2P0G0yhE53Q7+nSUw7/X4=", "dev": true}, "is-descriptor": {"version": "0.1.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-descriptor/download/is-descriptor-0.1.6.tgz", "integrity": "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=", "dev": true, "requires": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "dependencies": {"kind-of": {"version": "5.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/download/kind-of-5.1.0.tgz", "integrity": "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=", "dev": true}}}, "is-docker": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-docker/download/is-docker-2.1.1.tgz", "integrity": "sha1-QSWojkTkUNOE4JBH7eca3C0UQVY=", "dev": true, "optional": true}, "is-extendable": {"version": "0.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-extendable/download/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "dev": true}, "is-extglob": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-extglob/download/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "dev": true}, "is-generator-fn": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-generator-fn/download/is-generator-fn-2.1.0.tgz", "integrity": "sha1-fRQK3DiarzARqPKipM+m+q3/sRg=", "dev": true}, "is-glob": {"version": "4.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-glob/download/is-glob-4.0.1.tgz", "integrity": "sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw=", "dev": true, "requires": {"is-extglob": "^2.1.1"}}, "is-negative-zero": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-negative-zero/download/is-negative-zero-2.0.1.tgz", "integrity": "sha1-PedGwY3aIxkkGlNnWQjY92bxHCQ=", "dev": true}, "is-number": {"version": "7.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-number/download/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "dev": true}, "is-plain-object": {"version": "2.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-plain-object/download/is-plain-object-2.0.4.tgz", "integrity": "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=", "dev": true, "requires": {"isobject": "^3.0.1"}}, "is-potential-custom-element-name": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-potential-custom-element-name/download/is-potential-custom-element-name-1.0.0.tgz", "integrity": "sha1-DFLlS8yjkbssSUsh6GJtczbG45c=", "dev": true}, "is-regex": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-regex/download/is-regex-1.1.1.tgz", "integrity": "sha1-xvmKrMVG9s7FRooHt7FTq1ZKV7k=", "dev": true, "requires": {"has-symbols": "^1.0.1"}}, "is-stream": {"version": "1.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-stream/download/is-stream-1.1.0.tgz", "integrity": "sha1-EtSj3U5o4Lec6428hBc66A2RykQ=", "dev": true}, "is-string": {"version": "1.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-string/download/is-string-1.0.5.tgz", "integrity": "sha1-QEk+0ZjvP/R3uMf5L2ROyCpc06Y=", "dev": true}, "is-symbol": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-symbol/download/is-symbol-1.0.3.tgz", "integrity": "sha1-OOEBS55jKb4N6dJKQU/XRB7GGTc=", "dev": true, "requires": {"has-symbols": "^1.0.1"}}, "is-typedarray": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-typedarray/download/is-typedarray-1.0.0.tgz", "integrity": "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=", "dev": true}, "is-windows": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-windows/download/is-windows-1.0.2.tgz", "integrity": "sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=", "dev": true}, "is-wsl": {"version": "2.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-wsl/download/is-wsl-2.2.0.tgz", "integrity": "sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=", "dev": true, "optional": true, "requires": {"is-docker": "^2.0.0"}}, "isarray": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/isarray/download/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "isexe": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/isexe/download/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true}, "isobject": {"version": "3.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/isobject/download/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8=", "dev": true}, "isstream": {"version": "0.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/isstream/download/isstream-0.1.2.tgz", "integrity": "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=", "dev": true}, "istanbul-lib-coverage": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/istanbul-lib-coverage/download/istanbul-lib-coverage-3.0.0.tgz", "integrity": "sha1-9ZRKN8cLVQsCp4pcOyBVsoDOyOw=", "dev": true}, "istanbul-lib-instrument": {"version": "4.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/istanbul-lib-instrument/download/istanbul-lib-instrument-4.0.3.tgz", "integrity": "sha1-hzxv/4l0UBGCIndGlqPyiQLXfB0=", "dev": true, "requires": {"@babel/core": "^7.7.5", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.0.0", "semver": "^6.3.0"}, "dependencies": {"semver": {"version": "6.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/download/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "dev": true}}}, "istanbul-lib-report": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/istanbul-lib-report/download/istanbul-lib-report-3.0.0.tgz", "integrity": "sha1-dRj+UupE3jcvRgp2tezan/tz2KY=", "dev": true, "requires": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^3.0.0", "supports-color": "^7.1.0"}, "dependencies": {"has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "istanbul-lib-source-maps": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/istanbul-lib-source-maps/download/istanbul-lib-source-maps-4.0.0.tgz", "integrity": "sha1-dXQ85tlruG3H7kNSz2Nmoj8LGtk=", "dev": true, "requires": {"debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0", "source-map": "^0.6.1"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "istanbul-reports": {"version": "3.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/istanbul-reports/download/istanbul-reports-3.0.2.tgz", "integrity": "sha1-1ZMhDlAAaDdQywn8BkTktuJ/1Ts=", "dev": true, "requires": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}}, "jest": {"version": "26.6.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest/download/jest-26.6.3.tgz", "integrity": "sha1-QOj9vkjwDfofDOgSHKdLiKyRSO8=", "dev": true, "requires": {"@jest/core": "^26.6.3", "import-local": "^3.0.2", "jest-cli": "^26.6.3"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "jest-cli": {"version": "26.6.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-cli/download/jest-cli-26.6.3.tgz", "integrity": "sha1-QxF8/vJLxM1pGhdKh5alMuE16So=", "dev": true, "requires": {"@jest/core": "^26.6.3", "@jest/test-result": "^26.6.2", "@jest/types": "^26.6.2", "chalk": "^4.0.0", "exit": "^0.1.2", "graceful-fs": "^4.2.4", "import-local": "^3.0.2", "is-ci": "^2.0.0", "jest-config": "^26.6.3", "jest-util": "^26.6.2", "jest-validate": "^26.6.2", "prompts": "^2.0.1", "yargs": "^15.4.1"}}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "jest-changed-files": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-changed-files/download/jest-changed-files-26.6.2.tgz", "integrity": "sha1-9hmEeeHMZvIvmuHiKsqgtCnAQtA=", "dev": true, "requires": {"@jest/types": "^26.6.2", "execa": "^4.0.0", "throat": "^5.0.0"}, "dependencies": {"execa": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/execa/download/execa-4.1.0.tgz", "integrity": "sha1-TlSRrRVy8vF6d9OIxshXE1sihHo=", "dev": true, "requires": {"cross-spawn": "^7.0.0", "get-stream": "^5.0.0", "human-signals": "^1.1.1", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "onetime": "^5.1.0", "signal-exit": "^3.0.2", "strip-final-newline": "^2.0.0"}}, "get-stream": {"version": "5.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/get-stream/download/get-stream-5.2.0.tgz", "integrity": "sha1-SWaheV7lrOZecGxLe+txJX1uItM=", "dev": true, "requires": {"pump": "^3.0.0"}}, "is-stream": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-stream/download/is-stream-2.0.0.tgz", "integrity": "sha1-venDJoDW+uBBKdasnZIc54FfeOM=", "dev": true}, "npm-run-path": {"version": "4.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/npm-run-path/download/npm-run-path-4.0.1.tgz", "integrity": "sha1-t+zR5e1T2o43pV4cImnguX7XSOo=", "dev": true, "requires": {"path-key": "^3.0.0"}}}}, "jest-config": {"version": "26.6.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-config/download/jest-config-26.6.3.tgz", "integrity": "sha1-ZPQURO756wPcUdXFO3XIxx9kU0k=", "dev": true, "requires": {"@babel/core": "^7.1.0", "@jest/test-sequencer": "^26.6.3", "@jest/types": "^26.6.2", "babel-jest": "^26.6.3", "chalk": "^4.0.0", "deepmerge": "^4.2.2", "glob": "^7.1.1", "graceful-fs": "^4.2.4", "jest-environment-jsdom": "^26.6.2", "jest-environment-node": "^26.6.2", "jest-get-type": "^26.3.0", "jest-jasmine2": "^26.6.3", "jest-regex-util": "^26.0.0", "jest-resolve": "^26.6.2", "jest-util": "^26.6.2", "jest-validate": "^26.6.2", "micromatch": "^4.0.2", "pretty-format": "^26.6.2"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "jest-diff": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-diff/download/jest-diff-26.6.2.tgz", "integrity": "sha1-GqdGi1LDpo19XF/c381eSb0WQ5Q=", "dev": true, "requires": {"chalk": "^4.0.0", "diff-sequences": "^26.6.2", "jest-get-type": "^26.3.0", "pretty-format": "^26.6.2"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "jest-docblock": {"version": "26.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-docblock/download/jest-docblock-26.0.0.tgz", "integrity": "sha1-Pi+iCJn8koyxO9D/aL03EaNoibU=", "dev": true, "requires": {"detect-newline": "^3.0.0"}}, "jest-each": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-each/download/jest-each-26.6.2.tgz", "integrity": "sha1-AlJkOKd6Z0AcimOC3+WZmVLBZ8s=", "dev": true, "requires": {"@jest/types": "^26.6.2", "chalk": "^4.0.0", "jest-get-type": "^26.3.0", "jest-util": "^26.6.2", "pretty-format": "^26.6.2"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "jest-environment-jsdom": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-environment-jsdom/download/jest-environment-jsdom-26.6.2.tgz", "integrity": "sha1-eNCf6c8BmjVwCbm34fEB0jvR2j4=", "dev": true, "requires": {"@jest/environment": "^26.6.2", "@jest/fake-timers": "^26.6.2", "@jest/types": "^26.6.2", "@types/node": "*", "jest-mock": "^26.6.2", "jest-util": "^26.6.2", "jsdom": "^16.4.0"}}, "jest-environment-node": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-environment-node/download/jest-environment-node-26.6.2.tgz", "integrity": "sha1-gk5Mf7SURkY1bxGsdbIpsANfKww=", "dev": true, "requires": {"@jest/environment": "^26.6.2", "@jest/fake-timers": "^26.6.2", "@jest/types": "^26.6.2", "@types/node": "*", "jest-mock": "^26.6.2", "jest-util": "^26.6.2"}}, "jest-get-type": {"version": "26.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-get-type/download/jest-get-type-26.3.0.tgz", "integrity": "sha1-6X3Dw/U8K0Bsp6+u1Ek7HQmRmeA=", "dev": true}, "jest-haste-map": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-haste-map/download/jest-haste-map-26.6.2.tgz", "integrity": "sha1-3X5g/n3A6fkRoj15xf9/tcLK/qo=", "dev": true, "requires": {"@jest/types": "^26.6.2", "@types/graceful-fs": "^4.1.2", "@types/node": "*", "anymatch": "^3.0.3", "fb-watchman": "^2.0.0", "fsevents": "^2.1.2", "graceful-fs": "^4.2.4", "jest-regex-util": "^26.0.0", "jest-serializer": "^26.6.2", "jest-util": "^26.6.2", "jest-worker": "^26.6.2", "micromatch": "^4.0.2", "sane": "^4.0.3", "walker": "^1.0.7"}}, "jest-jasmine2": {"version": "26.6.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-jasmine2/download/jest-jasmine2-26.6.3.tgz", "integrity": "sha1-rcPPkV3qy1ISyTufNUfNEpWPLt0=", "dev": true, "requires": {"@babel/traverse": "^7.1.0", "@jest/environment": "^26.6.2", "@jest/source-map": "^26.6.2", "@jest/test-result": "^26.6.2", "@jest/types": "^26.6.2", "@types/node": "*", "chalk": "^4.0.0", "co": "^4.6.0", "expect": "^26.6.2", "is-generator-fn": "^2.0.0", "jest-each": "^26.6.2", "jest-matcher-utils": "^26.6.2", "jest-message-util": "^26.6.2", "jest-runtime": "^26.6.3", "jest-snapshot": "^26.6.2", "jest-util": "^26.6.2", "pretty-format": "^26.6.2", "throat": "^5.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "jest-leak-detector": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-leak-detector/download/jest-leak-detector-26.6.2.tgz", "integrity": "sha1-dxfPEYuSI48uumUFTIoMnGU6ka8=", "dev": true, "requires": {"jest-get-type": "^26.3.0", "pretty-format": "^26.6.2"}}, "jest-matcher-utils": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-matcher-utils/download/jest-matcher-utils-26.6.2.tgz", "integrity": "sha1-jm/W6GPIstMaxkcu6yN7xZXlPno=", "dev": true, "requires": {"chalk": "^4.0.0", "jest-diff": "^26.6.2", "jest-get-type": "^26.3.0", "pretty-format": "^26.6.2"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "jest-message-util": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-message-util/download/jest-message-util-26.6.2.tgz", "integrity": "sha1-WBc3RK1vwFBrXSEVC5vlbvABygc=", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "@jest/types": "^26.6.2", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.4", "micromatch": "^4.0.2", "pretty-format": "^26.6.2", "slash": "^3.0.0", "stack-utils": "^2.0.2"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "jest-mock": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-mock/download/jest-mock-26.6.2.tgz", "integrity": "sha1-1stxKwQe1H/g2bb8NHS8ZUP+swI=", "dev": true, "requires": {"@jest/types": "^26.6.2", "@types/node": "*"}}, "jest-pnp-resolver": {"version": "1.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-pnp-resolver/download/jest-pnp-resolver-1.2.2.tgz", "integrity": "sha1-twSsCuAoqJEIpNBAs/kZ393I4zw=", "dev": true}, "jest-regex-util": {"version": "26.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-regex-util/download/jest-regex-util-26.0.0.tgz", "integrity": "sha1-0l5xhLNuOf1GbDvEG+CXHoIf7ig=", "dev": true}, "jest-resolve": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-resolve/download/jest-resolve-26.6.2.tgz", "integrity": "sha1-o6sVFyF/RptQTxtWYDxbtUH7tQc=", "dev": true, "requires": {"@jest/types": "^26.6.2", "chalk": "^4.0.0", "graceful-fs": "^4.2.4", "jest-pnp-resolver": "^1.2.2", "jest-util": "^26.6.2", "read-pkg-up": "^7.0.1", "resolve": "^1.18.1", "slash": "^3.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "find-up": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/find-up/download/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "dev": true, "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "locate-path": {"version": "5.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/locate-path/download/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "dev": true, "requires": {"p-locate": "^4.1.0"}}, "p-limit": {"version": "2.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-limit/download/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-locate/download/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "dev": true, "requires": {"p-limit": "^2.2.0"}}, "p-try": {"version": "2.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-try/download/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "dev": true}, "parse-json": {"version": "5.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/parse-json/download/parse-json-5.1.0.tgz", "integrity": "sha1-+WCIzfJKj6qa6poAny2dlCyZlkY=", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}}, "path-exists": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-exists/download/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "dev": true}, "read-pkg": {"version": "5.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/read-pkg/download/read-pkg-5.2.0.tgz", "integrity": "sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=", "dev": true, "requires": {"@types/normalize-package-data": "^2.4.0", "normalize-package-data": "^2.5.0", "parse-json": "^5.0.0", "type-fest": "^0.6.0"}, "dependencies": {"type-fest": {"version": "0.6.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/type-fest/download/type-fest-0.6.0.tgz", "integrity": "sha1-jSojcNPfiG61yQraHFv2GIrPg4s=", "dev": true}}}, "read-pkg-up": {"version": "7.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/read-pkg-up/download/read-pkg-up-7.0.1.tgz", "integrity": "sha1-86YTV1hFlzOuK5VjgFbhhU5+9Qc=", "dev": true, "requires": {"find-up": "^4.1.0", "read-pkg": "^5.2.0", "type-fest": "^0.8.1"}}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "jest-resolve-dependencies": {"version": "26.6.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-resolve-dependencies/download/jest-resolve-dependencies-26.6.3.tgz", "integrity": "sha1-ZoCFnuXSLuXc2WH+SHH1n0x4T7Y=", "dev": true, "requires": {"@jest/types": "^26.6.2", "jest-regex-util": "^26.0.0", "jest-snapshot": "^26.6.2"}}, "jest-runner": {"version": "26.6.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-runner/download/jest-runner-26.6.3.tgz", "integrity": "sha1-LR/tPUbhDyM/0dvTv6o/6JJL4Vk=", "dev": true, "requires": {"@jest/console": "^26.6.2", "@jest/environment": "^26.6.2", "@jest/test-result": "^26.6.2", "@jest/types": "^26.6.2", "@types/node": "*", "chalk": "^4.0.0", "emittery": "^0.7.1", "exit": "^0.1.2", "graceful-fs": "^4.2.4", "jest-config": "^26.6.3", "jest-docblock": "^26.0.0", "jest-haste-map": "^26.6.2", "jest-leak-detector": "^26.6.2", "jest-message-util": "^26.6.2", "jest-resolve": "^26.6.2", "jest-runtime": "^26.6.3", "jest-util": "^26.6.2", "jest-worker": "^26.6.2", "source-map-support": "^0.5.6", "throat": "^5.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "jest-runtime": {"version": "26.6.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-runtime/download/jest-runtime-26.6.3.tgz", "integrity": "sha1-T2TvvPrDmDMbdLSzyC0n1AG4+is=", "dev": true, "requires": {"@jest/console": "^26.6.2", "@jest/environment": "^26.6.2", "@jest/fake-timers": "^26.6.2", "@jest/globals": "^26.6.2", "@jest/source-map": "^26.6.2", "@jest/test-result": "^26.6.2", "@jest/transform": "^26.6.2", "@jest/types": "^26.6.2", "@types/yargs": "^15.0.0", "chalk": "^4.0.0", "cjs-module-lexer": "^0.6.0", "collect-v8-coverage": "^1.0.0", "exit": "^0.1.2", "glob": "^7.1.3", "graceful-fs": "^4.2.4", "jest-config": "^26.6.3", "jest-haste-map": "^26.6.2", "jest-message-util": "^26.6.2", "jest-mock": "^26.6.2", "jest-regex-util": "^26.0.0", "jest-resolve": "^26.6.2", "jest-snapshot": "^26.6.2", "jest-util": "^26.6.2", "jest-validate": "^26.6.2", "slash": "^3.0.0", "strip-bom": "^4.0.0", "yargs": "^15.4.1"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "strip-bom": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/strip-bom/download/strip-bom-4.0.0.tgz", "integrity": "sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "jest-serializer": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-serializer/download/jest-serializer-26.6.2.tgz", "integrity": "sha1-0Tmq/UaVfTpEjzps2r4pGboHQtE=", "dev": true, "requires": {"@types/node": "*", "graceful-fs": "^4.2.4"}}, "jest-snapshot": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-snapshot/download/jest-snapshot-26.6.2.tgz", "integrity": "sha1-87CvGssiMxaFC9FOG+6pg3+znIQ=", "dev": true, "requires": {"@babel/types": "^7.0.0", "@jest/types": "^26.6.2", "@types/babel__traverse": "^7.0.4", "@types/prettier": "^2.0.0", "chalk": "^4.0.0", "expect": "^26.6.2", "graceful-fs": "^4.2.4", "jest-diff": "^26.6.2", "jest-get-type": "^26.3.0", "jest-haste-map": "^26.6.2", "jest-matcher-utils": "^26.6.2", "jest-message-util": "^26.6.2", "jest-resolve": "^26.6.2", "natural-compare": "^1.4.0", "pretty-format": "^26.6.2", "semver": "^7.3.2"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "jest-util": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-util/download/jest-util-26.6.2.tgz", "integrity": "sha1-kHU12+TVpstMR6ybkm9q8pV2y8E=", "dev": true, "requires": {"@jest/types": "^26.6.2", "@types/node": "*", "chalk": "^4.0.0", "graceful-fs": "^4.2.4", "is-ci": "^2.0.0", "micromatch": "^4.0.2"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "jest-validate": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-validate/download/jest-validate-26.6.2.tgz", "integrity": "sha1-I9OAlxWHFQRnNCkRw9e0rFerIOw=", "dev": true, "requires": {"@jest/types": "^26.6.2", "camelcase": "^6.0.0", "chalk": "^4.0.0", "jest-get-type": "^26.3.0", "leven": "^3.1.0", "pretty-format": "^26.6.2"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "camelcase": {"version": "6.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/camelcase/download/camelcase-6.2.0.tgz", "integrity": "sha1-kkr4gcnVJaydh/QNlk5c6pgqGAk=", "dev": true}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "jest-watcher": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-watcher/download/jest-watcher-26.6.2.tgz", "integrity": "sha1-pbaDuPnWjbyx19rjIXLSzKBZKXU=", "dev": true, "requires": {"@jest/test-result": "^26.6.2", "@jest/types": "^26.6.2", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "jest-util": "^26.6.2", "string-length": "^4.0.1"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "chalk": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-4.1.0.tgz", "integrity": "sha1-ThSHCmGNni7dl92DRf2dncMVZGo=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "jest-worker": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-worker/download/jest-worker-26.6.2.tgz", "integrity": "sha1-f3LLxNZDw2Xie5/XdfnQ6qnHqO0=", "dev": true, "requires": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "dependencies": {"has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "js-tokens": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/js-tokens/download/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk=", "dev": true}, "js-yaml": {"version": "3.14.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/js-yaml/download/js-yaml-3.14.1.tgz", "integrity": "sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=", "dev": true, "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "jsbn": {"version": "0.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jsbn/download/jsbn-0.1.1.tgz", "integrity": "sha1-peZUwuWi3rXyAdls77yoDA7y9RM=", "dev": true}, "jsdom": {"version": "16.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jsdom/download/jsdom-16.4.0.tgz", "integrity": "sha1-NgBb3i0Tb3Pu4agwxtReVUCO3ds=", "dev": true, "requires": {"abab": "^2.0.3", "acorn": "^7.1.1", "acorn-globals": "^6.0.0", "cssom": "^0.4.4", "cssstyle": "^2.2.0", "data-urls": "^2.0.0", "decimal.js": "^10.2.0", "domexception": "^2.0.1", "escodegen": "^1.14.1", "html-encoding-sniffer": "^2.0.1", "is-potential-custom-element-name": "^1.0.0", "nwsapi": "^2.2.0", "parse5": "5.1.1", "request": "^2.88.2", "request-promise-native": "^1.0.8", "saxes": "^5.0.0", "symbol-tree": "^3.2.4", "tough-cookie": "^3.0.1", "w3c-hr-time": "^1.0.2", "w3c-xmlserializer": "^2.0.0", "webidl-conversions": "^6.1.0", "whatwg-encoding": "^1.0.5", "whatwg-mimetype": "^2.3.0", "whatwg-url": "^8.0.0", "ws": "^7.2.3", "xml-name-validator": "^3.0.0"}}, "jsesc": {"version": "2.5.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jsesc/download/jsesc-2.5.2.tgz", "integrity": "sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=", "dev": true}, "json-parse-even-better-errors": {"version": "2.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=", "dev": true}, "json-schema": {"version": "0.2.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/json-schema/download/json-schema-0.2.3.tgz", "integrity": "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM=", "dev": true}, "json-schema-traverse": {"version": "0.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "dev": true}, "json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=", "dev": true}, "json-stringify-safe": {"version": "5.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz", "integrity": "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=", "dev": true}, "json5": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/json5/download/json5-1.0.1.tgz", "integrity": "sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4=", "dev": true, "requires": {"minimist": "^1.2.0"}}, "jsprim": {"version": "1.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jsprim/download/jsprim-1.4.1.tgz", "integrity": "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=", "dev": true, "requires": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.2.3", "verror": "1.10.0"}}, "kind-of": {"version": "6.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/download/kind-of-6.0.3.tgz", "integrity": "sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=", "dev": true}, "kleur": {"version": "3.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kleur/download/kleur-3.0.3.tgz", "integrity": "sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=", "dev": true}, "leven": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/leven/download/leven-3.1.0.tgz", "integrity": "sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=", "dev": true}, "levn": {"version": "0.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/levn/download/levn-0.4.1.tgz", "integrity": "sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=", "dev": true, "requires": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}}, "lines-and-columns": {"version": "1.1.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/lines-and-columns/download/lines-and-columns-1.1.6.tgz", "integrity": "sha1-HADHQ7QzzQpOgHWPe2SldEDZ/wA=", "dev": true}, "load-json-file": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/load-json-file/download/load-json-file-2.0.0.tgz", "integrity": "sha1-eUfkIUmvgNaWy/eXvKq8/h/inKg=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "parse-json": "^2.2.0", "pify": "^2.0.0", "strip-bom": "^3.0.0"}}, "locate-path": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/locate-path/download/locate-path-2.0.0.tgz", "integrity": "sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=", "dev": true, "requires": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}}, "lodash": {"version": "4.17.20", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/lodash/download/lodash-4.17.20.tgz", "integrity": "sha1-tEqbYpe8tpjxxRo1RaKzs2jVnFI=", "dev": true}, "lodash.sortby": {"version": "4.7.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/lodash.sortby/download/lodash.sortby-4.7.0.tgz", "integrity": "sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=", "dev": true}, "lru-cache": {"version": "6.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/lru-cache/download/lru-cache-6.0.0.tgz", "integrity": "sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=", "dev": true, "requires": {"yallist": "^4.0.0"}}, "make-dir": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/make-dir/download/make-dir-3.1.0.tgz", "integrity": "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=", "dev": true, "requires": {"semver": "^6.0.0"}, "dependencies": {"semver": {"version": "6.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/download/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "dev": true}}}, "makeerror": {"version": "1.0.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/makeerror/download/makeerror-1.0.11.tgz", "integrity": "sha1-4BpckQnyr3lmDk6LlYd5AYT1qWw=", "dev": true, "requires": {"tmpl": "1.0.x"}}, "map-cache": {"version": "0.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/map-cache/download/map-cache-0.2.2.tgz", "integrity": "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=", "dev": true}, "map-visit": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/map-visit/download/map-visit-1.0.0.tgz", "integrity": "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=", "dev": true, "requires": {"object-visit": "^1.0.0"}}, "md5": {"version": "2.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/md5/download/md5-2.3.0.tgz", "integrity": "sha1-w9qaaq46MLRreww0m4exENw72k8=", "requires": {"charenc": "0.0.2", "crypt": "0.0.2", "is-buffer": "~1.1.6"}}, "merge-stream": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/merge-stream/download/merge-stream-2.0.0.tgz", "integrity": "sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=", "dev": true}, "micromatch": {"version": "4.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/micromatch/download/micromatch-4.0.2.tgz", "integrity": "sha1-T8sJmb+fvC/L3SEvbWKbmlbDklk=", "dev": true, "requires": {"braces": "^3.0.1", "picomatch": "^2.0.5"}}, "mime-db": {"version": "1.44.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/mime-db/download/mime-db-1.44.0.tgz", "integrity": "sha1-+hHF6wrKEzS0Izy01S8QxaYnL5I=", "dev": true}, "mime-types": {"version": "2.1.27", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/mime-types/download/mime-types-2.1.27.tgz", "integrity": "sha1-R5SfmOJ56lMRn1ci4PNOUpvsAJ8=", "dev": true, "requires": {"mime-db": "1.44.0"}}, "mimic-fn": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/mimic-fn/download/mimic-fn-2.1.0.tgz", "integrity": "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=", "dev": true}, "minimatch": {"version": "3.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/minimatch/download/minimatch-3.0.4.tgz", "integrity": "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=", "dev": true, "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/minimist/download/minimist-1.2.5.tgz", "integrity": "sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=", "dev": true}, "mixin-deep": {"version": "1.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/mixin-deep/download/mixin-deep-1.3.2.tgz", "integrity": "sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=", "dev": true, "requires": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-extendable/download/is-extendable-1.0.1.tgz", "integrity": "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=", "dev": true, "requires": {"is-plain-object": "^2.0.4"}}}}, "ms": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/download/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "dev": true}, "nanomatch": {"version": "1.2.13", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/nanomatch/download/nanomatch-1.2.13.tgz", "integrity": "sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=", "dev": true, "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}}, "natural-compare": {"version": "1.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/natural-compare/download/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "dev": true}, "ndir": {"version": "0.1.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ndir/download/ndir-0.1.5.tgz", "integrity": "sha1-EgiR12l7u+ghTP7/CWAgYNNFRVg=", "dev": true}, "nice-try": {"version": "1.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/nice-try/download/nice-try-1.0.5.tgz", "integrity": "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=", "dev": true}, "node-int64": {"version": "0.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/node-int64/download/node-int64-0.4.0.tgz", "integrity": "sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=", "dev": true}, "node-modules-regexp": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/node-modules-regexp/download/node-modules-regexp-1.0.0.tgz", "integrity": "sha1-jZ2+KJZKSsVxLpExZCEHxx6Q7EA=", "dev": true}, "node-notifier": {"version": "8.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/node-notifier/download/node-notifier-8.0.1.tgz", "integrity": "sha1-+G6Ju8kl8rBoeEsx84Kv3Gyla+E=", "dev": true, "optional": true, "requires": {"growly": "^1.3.0", "is-wsl": "^2.2.0", "semver": "^7.3.2", "shellwords": "^0.1.1", "uuid": "^8.3.0", "which": "^2.0.2"}}, "normalize-package-data": {"version": "2.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/normalize-package-data/download/normalize-package-data-2.5.0.tgz", "integrity": "sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=", "dev": true, "requires": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}, "dependencies": {"semver": {"version": "5.7.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/download/semver-5.7.1.tgz", "integrity": "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=", "dev": true}}}, "normalize-path": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/normalize-path/download/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "dev": true}, "npm-run-path": {"version": "2.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/npm-run-path/download/npm-run-path-2.0.2.tgz", "integrity": "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=", "dev": true, "requires": {"path-key": "^2.0.0"}, "dependencies": {"path-key": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-key/download/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=", "dev": true}}}, "nwsapi": {"version": "2.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/nwsapi/download/nwsapi-2.2.0.tgz", "integrity": "sha1-IEh5qePQaP8qVROcLHcngGgaOLc=", "dev": true}, "oauth-sign": {"version": "0.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/oauth-sign/download/oauth-sign-0.9.0.tgz", "integrity": "sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=", "dev": true}, "object-copy": {"version": "0.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object-copy/download/object-copy-0.1.0.tgz", "integrity": "sha1-fn2Fi3gb18mRpBupde04EnVOmYw=", "dev": true, "requires": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/download/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "kind-of": {"version": "3.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "object-inspect": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object-inspect/download/object-inspect-1.9.0.tgz", "integrity": "sha1-yQUh104RJ7ZyZt7TOUrWEWmGUzo=", "dev": true}, "object-keys": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object-keys/download/object-keys-1.1.1.tgz", "integrity": "sha1-HEfyct8nfzsdrwYWd9nILiMixg4=", "dev": true}, "object-visit": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object-visit/download/object-visit-1.0.1.tgz", "integrity": "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=", "dev": true, "requires": {"isobject": "^3.0.0"}}, "object.assign": {"version": "4.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object.assign/download/object.assign-4.1.2.tgz", "integrity": "sha1-DtVKNC7Os3s4/3brgxoOeIy2OUA=", "dev": true, "requires": {"call-bind": "^1.0.0", "define-properties": "^1.1.3", "has-symbols": "^1.0.1", "object-keys": "^1.1.1"}}, "object.entries": {"version": "1.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object.entries/download/object.entries-1.1.3.tgz", "integrity": "sha1-xgHH8Wi2I3RUGgfdvT4tXk93EaY=", "dev": true, "requires": {"call-bind": "^1.0.0", "define-properties": "^1.1.3", "es-abstract": "^1.18.0-next.1", "has": "^1.0.3"}}, "object.pick": {"version": "1.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object.pick/download/object.pick-1.3.0.tgz", "integrity": "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=", "dev": true, "requires": {"isobject": "^3.0.1"}}, "object.values": {"version": "1.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object.values/download/object.values-1.1.2.tgz", "integrity": "sha1-eiAV4G/LD1Rr1lJIbOhYOkcxxzE=", "dev": true, "requires": {"call-bind": "^1.0.0", "define-properties": "^1.1.3", "es-abstract": "^1.18.0-next.1", "has": "^1.0.3"}}, "once": {"version": "1.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/once/download/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "dev": true, "requires": {"wrappy": "1"}}, "onetime": {"version": "5.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/onetime/download/onetime-5.1.2.tgz", "integrity": "sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=", "dev": true, "requires": {"mimic-fn": "^2.1.0"}}, "optionator": {"version": "0.9.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/optionator/download/optionator-0.9.1.tgz", "integrity": "sha1-TyNqY3Pa4FZqbUPhMmZ09QwpFJk=", "dev": true, "requires": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.3"}}, "p-each-series": {"version": "2.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-each-series/download/p-each-series-2.2.0.tgz", "integrity": "sha1-EFqwNXznKyAqiouUkzZyZXteKpo=", "dev": true}, "p-finally": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-finally/download/p-finally-1.0.0.tgz", "integrity": "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=", "dev": true}, "p-limit": {"version": "1.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-limit/download/p-limit-1.3.0.tgz", "integrity": "sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=", "dev": true, "requires": {"p-try": "^1.0.0"}}, "p-locate": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-locate/download/p-locate-2.0.0.tgz", "integrity": "sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=", "dev": true, "requires": {"p-limit": "^1.1.0"}}, "p-try": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-try/download/p-try-1.0.0.tgz", "integrity": "sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=", "dev": true}, "parent-module": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/parent-module/download/parent-module-1.0.1.tgz", "integrity": "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=", "dev": true, "requires": {"callsites": "^3.0.0"}}, "parse-json": {"version": "2.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/parse-json/download/parse-json-2.2.0.tgz", "integrity": "sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=", "dev": true, "requires": {"error-ex": "^1.2.0"}}, "parse5": {"version": "5.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/parse5/download/parse5-5.1.1.tgz", "integrity": "sha1-9o5OW6GFKsLK3AD0VV//bCq7YXg=", "dev": true}, "pascalcase": {"version": "0.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pascalcase/download/pascalcase-0.1.1.tgz", "integrity": "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=", "dev": true}, "path-exists": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-exists/download/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "dev": true}, "path-is-absolute": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-is-absolute/download/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "dev": true}, "path-key": {"version": "3.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-key/download/path-key-3.1.1.tgz", "integrity": "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=", "dev": true}, "path-parse": {"version": "1.0.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-parse/download/path-parse-1.0.6.tgz", "integrity": "sha1-1i27VnlAXXLEc37FhgDp3c8G0kw=", "dev": true}, "path-type": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-type/download/path-type-2.0.0.tgz", "integrity": "sha1-8BLMuEFbcJb8LaoQVMPXI4lZTHM=", "dev": true, "requires": {"pify": "^2.0.0"}}, "performance-now": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/performance-now/download/performance-now-2.1.0.tgz", "integrity": "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=", "dev": true}, "picomatch": {"version": "2.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/picomatch/download/picomatch-2.2.2.tgz", "integrity": "sha1-IfMz6ba46v8CRo9RRupAbTRfTa0=", "dev": true}, "pify": {"version": "2.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pify/download/pify-2.3.0.tgz", "integrity": "sha1-7RQaasBDqEnqWISY59yosVMw6Qw=", "dev": true}, "pirates": {"version": "4.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pirates/download/pirates-4.0.1.tgz", "integrity": "sha1-ZDqSyviUVm+RsrmG0sZpUKji+4c=", "dev": true, "requires": {"node-modules-regexp": "^1.0.0"}}, "pkg-dir": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pkg-dir/download/pkg-dir-2.0.0.tgz", "integrity": "sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s=", "dev": true, "requires": {"find-up": "^2.1.0"}}, "posix-character-classes": {"version": "0.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/posix-character-classes/download/posix-character-classes-0.1.1.tgz", "integrity": "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=", "dev": true}, "prelude-ls": {"version": "1.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/prelude-ls/download/prelude-ls-1.2.1.tgz", "integrity": "sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=", "dev": true}, "pretty-format": {"version": "26.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pretty-format/download/pretty-format-26.6.2.tgz", "integrity": "sha1-41wnBfFMt/4v6U+geDRbREEg/JM=", "dev": true, "requires": {"@jest/types": "^26.6.2", "ansi-regex": "^5.0.0", "ansi-styles": "^4.0.0", "react-is": "^17.0.1"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}}}, "progress": {"version": "2.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/progress/download/progress-2.0.3.tgz", "integrity": "sha1-foz42PW48jnBvGi+tOt4Vn1XLvg="}, "prompts": {"version": "2.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/prompts/download/prompts-2.4.0.tgz", "integrity": "sha1-SqXeByOiMdHukSHED99mPfc/Ydc=", "dev": true, "requires": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}}, "psl": {"version": "1.8.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/psl/download/psl-1.8.0.tgz", "integrity": "sha1-kyb4vPsBOtzABf3/BWrM4CDlHCQ=", "dev": true}, "pump": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pump/download/pump-3.0.0.tgz", "integrity": "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=", "dev": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "punycode": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/punycode/download/punycode-2.1.1.tgz", "integrity": "sha1-tYsBCsQMIsVldhbI0sLALHv0eew=", "dev": true}, "qs": {"version": "6.5.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/qs/download/qs-6.5.2.tgz", "integrity": "sha1-yzroBuh0BERYTvFUzo7pjUA/PjY=", "dev": true}, "react-is": {"version": "17.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/react-is/download/react-is-17.0.1.tgz", "integrity": "sha1-WzUxvXamRaTJ+25pPtNkGeMwEzk=", "dev": true}, "read-pkg": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/read-pkg/download/read-pkg-2.0.0.tgz", "integrity": "sha1-jvHAYjxqbbDcZxPEv6xGMysjaPg=", "dev": true, "requires": {"load-json-file": "^2.0.0", "normalize-package-data": "^2.3.2", "path-type": "^2.0.0"}}, "read-pkg-up": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/read-pkg-up/download/read-pkg-up-2.0.0.tgz", "integrity": "sha1-a3KoBImE4MQeeVEP1en6mbO1Sb4=", "dev": true, "requires": {"find-up": "^2.0.0", "read-pkg": "^2.0.0"}}, "regex-not": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/regex-not/download/regex-not-1.0.2.tgz", "integrity": "sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=", "dev": true, "requires": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}}, "regexpp": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/regexpp/download/regexpp-3.1.0.tgz", "integrity": "sha1-IG0K0KVkjP+9uK5GQ489xRyfeOI=", "dev": true}, "remove-trailing-separator": {"version": "1.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz", "integrity": "sha1-wkvOKig62tW8P1jg1IJJuSN52O8=", "dev": true}, "repeat-element": {"version": "1.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/repeat-element/download/repeat-element-1.1.3.tgz", "integrity": "sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4=", "dev": true}, "repeat-string": {"version": "1.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/repeat-string/download/repeat-string-1.6.1.tgz", "integrity": "sha1-jcrkcOHIirwtYA//Sndihtp15jc=", "dev": true}, "request": {"version": "2.88.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/request/download/request-2.88.2.tgz", "integrity": "sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=", "dev": true, "requires": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.3", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.5.0", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}, "dependencies": {"tough-cookie": {"version": "2.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tough-cookie/download/tough-cookie-2.5.0.tgz", "integrity": "sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=", "dev": true, "requires": {"psl": "^1.1.28", "punycode": "^2.1.1"}}, "uuid": {"version": "3.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/uuid/download/uuid-3.4.0.tgz", "integrity": "sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=", "dev": true}}}, "request-promise-core": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/request-promise-core/download/request-promise-core-1.1.4.tgz", "integrity": "sha1-Pu3UIjII1BmGe3jOgVFn0QWToi8=", "dev": true, "requires": {"lodash": "^4.17.19"}}, "request-promise-native": {"version": "1.0.9", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/request-promise-native/download/request-promise-native-1.0.9.tgz", "integrity": "sha1-5AcSBSal79yaObKKVnm/R7nZ3Cg=", "dev": true, "requires": {"request-promise-core": "1.1.4", "stealthy-require": "^1.1.1", "tough-cookie": "^2.3.3"}, "dependencies": {"tough-cookie": {"version": "2.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tough-cookie/download/tough-cookie-2.5.0.tgz", "integrity": "sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=", "dev": true, "requires": {"psl": "^1.1.28", "punycode": "^2.1.1"}}}}, "require-directory": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/require-directory/download/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I=", "dev": true}, "require-main-filename": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/require-main-filename/download/require-main-filename-2.0.0.tgz", "integrity": "sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=", "dev": true}, "resolve": {"version": "1.19.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/resolve/download/resolve-1.19.0.tgz", "integrity": "sha1-GvW/YwQJc0oGfK4pMYqsf6KaJnw=", "dev": true, "requires": {"is-core-module": "^2.1.0", "path-parse": "^1.0.6"}}, "resolve-cwd": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/resolve-cwd/download/resolve-cwd-3.0.0.tgz", "integrity": "sha1-DwB18bslRHZs9zumpuKt/ryxPy0=", "dev": true, "requires": {"resolve-from": "^5.0.0"}, "dependencies": {"resolve-from": {"version": "5.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/resolve-from/download/resolve-from-5.0.0.tgz", "integrity": "sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=", "dev": true}}}, "resolve-from": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/resolve-from/download/resolve-from-4.0.0.tgz", "integrity": "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=", "dev": true}, "resolve-url": {"version": "0.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/resolve-url/download/resolve-url-0.2.1.tgz", "integrity": "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=", "dev": true}, "ret": {"version": "0.1.15", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ret/download/ret-0.1.15.tgz", "integrity": "sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=", "dev": true}, "rimraf": {"version": "3.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/rimraf/download/rimraf-3.0.2.tgz", "integrity": "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=", "dev": true, "requires": {"glob": "^7.1.3"}}, "rsvp": {"version": "4.8.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/rsvp/download/rsvp-4.8.5.tgz", "integrity": "sha1-yPFVMR0Wf2jyHhaN9x7FsIMRNzQ=", "dev": true}, "safe-buffer": {"version": "5.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/safe-buffer/download/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "dev": true}, "safe-regex": {"version": "1.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/safe-regex/download/safe-regex-1.1.0.tgz", "integrity": "sha1-QKNmnzsHfR6UPURinhV91IAjvy4=", "dev": true, "requires": {"ret": "~0.1.10"}}, "safer-buffer": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/safer-buffer/download/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "dev": true}, "sane": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/sane/download/sane-4.1.0.tgz", "integrity": "sha1-7Ygf2SJzOmxGG8GJ3CtsAG8//e0=", "dev": true, "requires": {"@cnakazawa/watch": "^1.0.3", "anymatch": "^2.0.0", "capture-exit": "^2.0.0", "exec-sh": "^0.3.2", "execa": "^1.0.0", "fb-watchman": "^2.0.0", "micromatch": "^3.1.4", "minimist": "^1.1.1", "walker": "~1.0.5"}, "dependencies": {"anymatch": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/anymatch/download/anymatch-2.0.0.tgz", "integrity": "sha1-vLJLTzeTTZqnrBe0ra+J58du8us=", "dev": true, "requires": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}}, "braces": {"version": "2.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/braces/download/braces-2.3.2.tgz", "integrity": "sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=", "dev": true, "requires": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "fill-range": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fill-range/download/fill-range-4.0.0.tgz", "integrity": "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=", "dev": true, "requires": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "is-number": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-number/download/is-number-3.0.0.tgz", "integrity": "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "micromatch": {"version": "3.1.10", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/micromatch/download/micromatch-3.1.10.tgz", "integrity": "sha1-cIWbyVyYQJUvNZoGij/En57PrCM=", "dev": true, "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}}, "normalize-path": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/normalize-path/download/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "dev": true, "requires": {"remove-trailing-separator": "^1.0.1"}}, "to-regex-range": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/to-regex-range/download/to-regex-range-2.1.1.tgz", "integrity": "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=", "dev": true, "requires": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}}}}, "saxes": {"version": "5.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/saxes/download/saxes-5.0.1.tgz", "integrity": "sha1-7rq5U/o7dgjb6U5drbFciI+maW0=", "dev": true, "requires": {"xmlchars": "^2.2.0"}}, "semver": {"version": "7.3.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/download/semver-7.3.4.tgz", "integrity": "sha1-J6qn0uTKdkUvmNOt0JOnLJQ+3Jc=", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "set-blocking": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/set-blocking/download/set-blocking-2.0.0.tgz", "integrity": "sha1-BF+XgtARrppoA93TgrJDkrPYkPc=", "dev": true}, "set-value": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/set-value/download/set-value-2.0.1.tgz", "integrity": "sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=", "dev": true, "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "shebang-command": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/shebang-command/download/shebang-command-2.0.0.tgz", "integrity": "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=", "dev": true, "requires": {"shebang-regex": "^3.0.0"}}, "shebang-regex": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/shebang-regex/download/shebang-regex-3.0.0.tgz", "integrity": "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=", "dev": true}, "shellwords": {"version": "0.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/shellwords/download/shellwords-0.1.1.tgz", "integrity": "sha1-1rkYHBpI05cyTISHHvvPxz/AZUs=", "dev": true, "optional": true}, "signal-exit": {"version": "3.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/signal-exit/download/signal-exit-3.0.3.tgz", "integrity": "sha1-oUEMLt2PB3sItOJTyOrPyvBXRhw=", "dev": true}, "sisteransi": {"version": "1.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/sisteransi/download/sisteransi-1.0.5.tgz", "integrity": "sha1-E01oEpd1ZDfMBcoBNw06elcQde0=", "dev": true}, "slash": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/slash/download/slash-3.0.0.tgz", "integrity": "sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=", "dev": true}, "slice-ansi": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/slice-ansi/download/slice-ansi-4.0.0.tgz", "integrity": "sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=", "dev": true, "requires": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}}}, "snapdragon": {"version": "0.8.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/snapdragon/download/snapdragon-0.8.2.tgz", "integrity": "sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=", "dev": true, "requires": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/download/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "ms": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "snapdragon-node": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/snapdragon-node/download/snapdragon-node-2.1.1.tgz", "integrity": "sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=", "dev": true, "requires": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/download/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-descriptor/download/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "snapdragon-util": {"version": "3.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/snapdragon-util/download/snapdragon-util-3.0.1.tgz", "integrity": "sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=", "dev": true, "requires": {"kind-of": "^3.2.0"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "source-map": {"version": "0.5.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/download/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true}, "source-map-resolve": {"version": "0.5.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map-resolve/download/source-map-resolve-0.5.3.tgz", "integrity": "sha1-GQhmvs51U+H48mei7oLGBrVQmho=", "dev": true, "requires": {"atob": "^2.1.2", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "source-map-support": {"version": "0.5.19", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map-support/download/source-map-support-0.5.19.tgz", "integrity": "sha1-qYti+G3K9PZzmWSMCFKRq56P7WE=", "dev": true, "requires": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "source-map-url": {"version": "0.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map-url/download/source-map-url-0.4.0.tgz", "integrity": "sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=", "dev": true}, "spdx-correct": {"version": "3.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/spdx-correct/download/spdx-correct-3.1.1.tgz", "integrity": "sha1-3s6BrJweZxPl99G28X1Gj6U9iak=", "dev": true, "requires": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "spdx-exceptions": {"version": "2.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz", "integrity": "sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=", "dev": true}, "spdx-expression-parse": {"version": "3.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz", "integrity": "sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=", "dev": true, "requires": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "spdx-license-ids": {"version": "3.0.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/spdx-license-ids/download/spdx-license-ids-3.0.7.tgz", "integrity": "sha1-6cGKQQ5e1+EkQqVJ+9ivp2cDjWU=", "dev": true}, "split-string": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/split-string/download/split-string-3.1.0.tgz", "integrity": "sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=", "dev": true, "requires": {"extend-shallow": "^3.0.0"}}, "sprintf-js": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/sprintf-js/download/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=", "dev": true}, "sshpk": {"version": "1.16.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/sshpk/download/sshpk-1.16.1.tgz", "integrity": "sha1-+2YcC+8ps520B2nuOfpwCT1vaHc=", "dev": true, "requires": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}}, "stack-utils": {"version": "2.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/stack-utils/download/stack-utils-2.0.3.tgz", "integrity": "sha1-zV8DASb/EWt4zLPAJ/4wJxO2Enc=", "dev": true, "requires": {"escape-string-regexp": "^2.0.0"}, "dependencies": {"escape-string-regexp": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/escape-string-regexp/download/escape-string-regexp-2.0.0.tgz", "integrity": "sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=", "dev": true}}}, "static-extend": {"version": "0.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/static-extend/download/static-extend-0.1.2.tgz", "integrity": "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=", "dev": true, "requires": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/download/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}}}, "stealthy-require": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/stealthy-require/download/stealthy-require-1.1.1.tgz", "integrity": "sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks=", "dev": true}, "string-length": {"version": "4.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/string-length/download/string-length-4.0.1.tgz", "integrity": "sha1-Spc78x73fE7bzq3WryYRmWmF+KE=", "dev": true, "requires": {"char-regex": "^1.0.2", "strip-ansi": "^6.0.0"}}, "string-width": {"version": "4.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/string-width/download/string-width-4.2.0.tgz", "integrity": "sha1-lSGCxGzHssMT0VluYjmSvRY7crU=", "dev": true, "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.0"}}, "string.prototype.trimend": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/string.prototype.trimend/download/string.prototype.trimend-1.0.3.tgz", "integrity": "sha1-oivVPMpcfPRNfJ1ccyEYhz1s0Ys=", "dev": true, "requires": {"call-bind": "^1.0.0", "define-properties": "^1.1.3"}}, "string.prototype.trimstart": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/string.prototype.trimstart/download/string.prototype.trimstart-1.0.3.tgz", "integrity": "sha1-m0y1kOEjuzZWRAHVmCQpjeUP1ao=", "dev": true, "requires": {"call-bind": "^1.0.0", "define-properties": "^1.1.3"}}, "strip-ansi": {"version": "6.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/strip-ansi/download/strip-ansi-6.0.0.tgz", "integrity": "sha1-CxVx3XZpzNTz4G4U7x7tJiJa5TI=", "dev": true, "requires": {"ansi-regex": "^5.0.0"}}, "strip-bom": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/strip-bom/-/strip-bom-3.0.0.tgz", "integrity": "sha1-NG/BXiSPQ1MJZnZSY1Gb01Kpbxk=", "dev": true}, "strip-eof": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/strip-eof/download/strip-eof-1.0.0.tgz", "integrity": "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=", "dev": true}, "strip-final-newline": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/strip-final-newline/download/strip-final-newline-2.0.0.tgz", "integrity": "sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=", "dev": true}, "strip-json-comments": {"version": "3.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/strip-json-comments/download/strip-json-comments-3.1.1.tgz", "integrity": "sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=", "dev": true}, "supports-color": {"version": "5.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "dev": true, "requires": {"has-flag": "^3.0.0"}}, "supports-hyperlinks": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-hyperlinks/download/supports-hyperlinks-2.1.0.tgz", "integrity": "sha1-9mPfJSr183xdSbvX7u+p4Lnlnkc=", "dev": true, "requires": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "dependencies": {"has-flag": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "symbol-tree": {"version": "3.2.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/symbol-tree/download/symbol-tree-3.2.4.tgz", "integrity": "sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=", "dev": true}, "table": {"version": "6.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/table/download/table-6.0.4.tgz", "integrity": "sha1-xSPdGCF36SbHI+sg4bNBI4GIqg0=", "dev": true, "requires": {"ajv": "^6.12.4", "lodash": "^4.17.20", "slice-ansi": "^4.0.0", "string-width": "^4.2.0"}}, "terminal-link": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/terminal-link/download/terminal-link-2.1.1.tgz", "integrity": "sha1-FKZKJ6s8Dfkz6lRvulXy0HjtyZQ=", "dev": true, "requires": {"ansi-escapes": "^4.2.1", "supports-hyperlinks": "^2.0.0"}}, "test-exclude": {"version": "6.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/test-exclude/download/test-exclude-6.0.0.tgz", "integrity": "sha1-BKhphmHYBepvopO2y55jrARO8V4=", "dev": true, "requires": {"@istanbuljs/schema": "^0.1.2", "glob": "^7.1.4", "minimatch": "^3.0.4"}}, "text-table": {"version": "0.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/text-table/download/text-table-0.2.0.tgz", "integrity": "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=", "dev": true}, "throat": {"version": "5.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/throat/download/throat-5.0.0.tgz", "integrity": "sha1-xRmSNYA6rRh1SmZ9ZZtecs4Wdks=", "dev": true}, "tmpl": {"version": "1.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tmpl/download/tmpl-1.0.4.tgz", "integrity": "sha1-I2QN17QtAEM5ERQIIOXPRA5SHdE=", "dev": true}, "to-fast-properties": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/to-fast-properties/download/to-fast-properties-2.0.0.tgz", "integrity": "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=", "dev": true}, "to-object-path": {"version": "0.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/to-object-path/download/to-object-path-0.3.0.tgz", "integrity": "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "to-regex": {"version": "3.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/to-regex/download/to-regex-3.0.2.tgz", "integrity": "sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=", "dev": true, "requires": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}}, "to-regex-range": {"version": "5.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/to-regex-range/download/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "dev": true, "requires": {"is-number": "^7.0.0"}}, "tough-cookie": {"version": "3.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tough-cookie/download/tough-cookie-3.0.1.tgz", "integrity": "sha1-nfT1fnOcJpMKAYGEiH9K233Kc7I=", "dev": true, "requires": {"ip-regex": "^2.1.0", "psl": "^1.1.28", "punycode": "^2.1.1"}}, "tr46": {"version": "2.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tr46/download/tr46-2.0.2.tgz", "integrity": "sha1-Ayc1ht7xWVrgj+2zjXczzukdJHk=", "dev": true, "requires": {"punycode": "^2.1.1"}}, "tsconfig-paths": {"version": "3.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tsconfig-paths/download/tsconfig-paths-3.9.0.tgz", "integrity": "sha1-CYVHpsREiAfo/Ljq4IEGTumjyQs=", "dev": true, "requires": {"@types/json5": "^0.0.29", "json5": "^1.0.1", "minimist": "^1.2.0", "strip-bom": "^3.0.0"}}, "tunnel-agent": {"version": "0.6.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tunnel-agent/download/tunnel-agent-0.6.0.tgz", "integrity": "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=", "dev": true, "requires": {"safe-buffer": "^5.0.1"}}, "tweetnacl": {"version": "0.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tweetnacl/download/tweetnacl-0.14.5.tgz", "integrity": "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=", "dev": true}, "type-check": {"version": "0.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/type-check/download/type-check-0.4.0.tgz", "integrity": "sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=", "dev": true, "requires": {"prelude-ls": "^1.2.1"}}, "type-detect": {"version": "4.0.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/type-detect/download/type-detect-4.0.8.tgz", "integrity": "sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=", "dev": true}, "type-fest": {"version": "0.8.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/type-fest/download/type-fest-0.8.1.tgz", "integrity": "sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=", "dev": true}, "typedarray-to-buffer": {"version": "3.1.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/typedarray-to-buffer/download/typedarray-to-buffer-3.1.5.tgz", "integrity": "sha1-qX7nqf9CaRufeD/xvFES/j/KkIA=", "dev": true, "requires": {"is-typedarray": "^1.0.0"}}, "union-value": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/union-value/download/union-value-1.0.1.tgz", "integrity": "sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=", "dev": true, "requires": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^2.0.1"}}, "unset-value": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/unset-value/download/unset-value-1.0.0.tgz", "integrity": "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=", "dev": true, "requires": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "dependencies": {"has-value": {"version": "0.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-value/download/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "dev": true, "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "dependencies": {"isobject": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/isobject/download/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "dev": true, "requires": {"isarray": "1.0.0"}}}}, "has-values": {"version": "0.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-values/download/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E=", "dev": true}}}, "uri-js": {"version": "4.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/uri-js/download/uri-js-4.4.0.tgz", "integrity": "sha1-qnFCYd55PoqCNHp7zJznTobyhgI=", "dev": true, "requires": {"punycode": "^2.1.0"}}, "urix": {"version": "0.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/urix/download/urix-0.1.0.tgz", "integrity": "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=", "dev": true}, "use": {"version": "3.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/use/download/use-3.1.1.tgz", "integrity": "sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=", "dev": true}, "uuid": {"version": "8.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/uuid/download/uuid-8.3.2.tgz", "integrity": "sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=", "dev": true, "optional": true}, "v8-compile-cache": {"version": "2.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/v8-compile-cache/download/v8-compile-cache-2.2.0.tgz", "integrity": "sha1-lHHvo++RKNL3xqfKOcTda1BVsTI=", "dev": true}, "v8-to-istanbul": {"version": "7.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/v8-to-istanbul/download/v8-to-istanbul-7.1.0.tgz", "integrity": "sha1-W5XO9FwPgyF+x5+Px+4ci0hq7gc=", "dev": true, "requires": {"@types/istanbul-lib-coverage": "^2.0.1", "convert-source-map": "^1.6.0", "source-map": "^0.7.3"}, "dependencies": {"source-map": {"version": "0.7.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/download/source-map-0.7.3.tgz", "integrity": "sha1-UwL4FpAxc1ImVECS5kmB91F1A4M=", "dev": true}}}, "validate-npm-package-license": {"version": "3.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz", "integrity": "sha1-/JH2uce6FchX9MssXe/uw51PQQo=", "dev": true, "requires": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "verror": {"version": "1.10.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/verror/download/verror-1.10.0.tgz", "integrity": "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=", "dev": true, "requires": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "w3c-hr-time": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/w3c-hr-time/download/w3c-hr-time-1.0.2.tgz", "integrity": "sha1-ConN9cwVgi35w2BUNnaWPgzDCM0=", "dev": true, "requires": {"browser-process-hrtime": "^1.0.0"}}, "w3c-xmlserializer": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/w3c-xmlserializer/download/w3c-xmlserializer-2.0.0.tgz", "integrity": "sha1-PnEEoFt1FGzGD1ZDgLf2g6zxAgo=", "dev": true, "requires": {"xml-name-validator": "^3.0.0"}}, "walk": {"version": "2.3.14", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/walk/download/walk-2.3.14.tgz", "integrity": "sha1-YOyGMc/SMnauHnNjzhHWJkUuHvM=", "requires": {"foreachasync": "^3.0.0"}}, "walker": {"version": "1.0.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/walker/download/walker-1.0.7.tgz", "integrity": "sha1-L3+bj9ENZ3JisYqITijRlhjgKPs=", "dev": true, "requires": {"makeerror": "1.0.x"}}, "webidl-conversions": {"version": "6.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/webidl-conversions/download/webidl-conversions-6.1.0.tgz", "integrity": "sha1-kRG01+qArNQPUnDWZmIa+ni2lRQ=", "dev": true}, "whatwg-encoding": {"version": "1.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/whatwg-encoding/download/whatwg-encoding-1.0.5.tgz", "integrity": "sha1-WrrPd3wyFmpR0IXWtPPn0nET3bA=", "dev": true, "requires": {"iconv-lite": "0.4.24"}}, "whatwg-mimetype": {"version": "2.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/whatwg-mimetype/download/whatwg-mimetype-2.3.0.tgz", "integrity": "sha1-PUseAxLSB5h5+Cav8Y2+7KWWD78=", "dev": true}, "whatwg-url": {"version": "8.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/whatwg-url/download/whatwg-url-8.4.0.tgz", "integrity": "sha1-UPuWFbBUaVkdKyvW367SlC7XKDc=", "dev": true, "requires": {"lodash.sortby": "^4.7.0", "tr46": "^2.0.2", "webidl-conversions": "^6.1.0"}}, "which": {"version": "2.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/which/download/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "dev": true, "requires": {"isexe": "^2.0.0"}}, "which-module": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/which-module/download/which-module-2.0.0.tgz", "integrity": "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=", "dev": true}, "word-wrap": {"version": "1.2.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/word-wrap/download/word-wrap-1.2.3.tgz", "integrity": "sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=", "dev": true}, "wrap-ansi": {"version": "6.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/wrap-ansi/download/wrap-ansi-6.2.0.tgz", "integrity": "sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=", "dev": true, "requires": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "color-convert": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}}}, "wrappy": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/wrappy/download/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "dev": true}, "write-file-atomic": {"version": "3.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/write-file-atomic/download/write-file-atomic-3.0.3.tgz", "integrity": "sha1-Vr1cWlxwSBzRnFcb05q5ZaXeVug=", "dev": true, "requires": {"imurmurhash": "^0.1.4", "is-typedarray": "^1.0.0", "signal-exit": "^3.0.2", "typedarray-to-buffer": "^3.1.5"}}, "ws": {"version": "7.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ws/download/ws-7.4.1.tgz", "integrity": "sha1-ozO+Amlr0OVM6gQ04h3MiprClLs=", "dev": true}, "xml-name-validator": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/xml-name-validator/download/xml-name-validator-3.0.0.tgz", "integrity": "sha1-auc+Bt5NjG5H+fsYH3jWSK1FfGo=", "dev": true}, "xmlchars": {"version": "2.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/xmlchars/download/xmlchars-2.2.0.tgz", "integrity": "sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs=", "dev": true}, "y18n": {"version": "4.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/y18n/download/y18n-4.0.1.tgz", "integrity": "sha1-jbK4PDHF11CZu4kLI/MJSJHiR9Q=", "dev": true}, "yallist": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/yallist/download/yallist-4.0.0.tgz", "integrity": "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=", "dev": true}, "yargs": {"version": "15.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/yargs/download/yargs-15.4.1.tgz", "integrity": "sha1-DYehbeAa7p2L7Cv7909nhRcw9Pg=", "dev": true, "requires": {"cliui": "^6.0.0", "decamelize": "^1.2.0", "find-up": "^4.1.0", "get-caller-file": "^2.0.1", "require-directory": "^2.1.1", "require-main-filename": "^2.0.0", "set-blocking": "^2.0.0", "string-width": "^4.2.0", "which-module": "^2.0.0", "y18n": "^4.0.0", "yargs-parser": "^18.1.2"}, "dependencies": {"find-up": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/find-up/download/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "dev": true, "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}, "locate-path": {"version": "5.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/locate-path/download/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "dev": true, "requires": {"p-locate": "^4.1.0"}}, "p-limit": {"version": "2.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-limit/download/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-locate/download/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "dev": true, "requires": {"p-limit": "^2.2.0"}}, "p-try": {"version": "2.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-try/download/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "dev": true}, "path-exists": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-exists/download/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "dev": true}}}, "yargs-parser": {"version": "18.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/yargs-parser/download/yargs-parser-18.1.3.tgz", "integrity": "sha1-vmjEl1xrKr9GkjawyHA2L6sJp7A=", "dev": true, "requires": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}}}}
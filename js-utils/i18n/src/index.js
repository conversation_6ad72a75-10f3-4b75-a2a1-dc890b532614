#!/usr/bin/env node
// const fs = require('fs');
const ndir = require('ndir');
const Replace = require('./replace');
// const walker = walk.walk('./src', {});
// walker.on('file', function(root, fileStats, next) {
//   console.log(fileStats.name, 'file');
//   next();
// });
const fileList = [];
ndir.walk(
  './src',
  function onDir(dirpath, files) {
    // console.log(' * %s', dirpath);
    for (let i = 0, l = files.length; i < l; i++) {
      const info = files[i];
      if (info[1].isFile()) {
        // console.log('   * %s', info[0]);
        if (
          info[0].match(/(\.js|\.jsx)$/) &&
          info[0].indexOf('src/locales') === -1
        ) {
          fileList.push(info[0]);
        }
      }
    }
  },
  function end() {
    console.log('walk end.');
    // console.log(fileList);
    const replaceZh = new Replace(fileList);
    replaceZh.start();
  },
  function error(err, errPath) {
    console.error('%s error: %s', errPath, err);
  },
);

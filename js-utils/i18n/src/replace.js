const fs = require('fs');
// const replace = require('./replace');
function lineToUpper(str) {
  const strAy = str.split('-');
  for (let i = 1; i < strAy.length; i++) {
    strAy[i] = strAy[i][0].toUpperCase() + strAy[i].slice(1);
  }
  return strAy.join('');
}

class Replace {
  constructor(fileList) {
    // this.options = options;
    this.lang = {
      pages: {},
    };
    this.replaceAmount = 0;
    this.fileList = fileList;
    // this.fileList = [
    //   // '/Users/<USER>/code/fop-web-ica-opoms/src/pages/BidsManage/index/index.js',
    //   // '/Users/<USER>/code/fop-web-ica-opoms/src/pages/BidsManage/index/index.js',
    //   '/Users/<USER>/code/fop-web-ica-opoms/src/pages/BidsManage/Components/detail.js',
    // ];
  }

  start() {
    this.fileList.forEach(item => {
      this.replace(item);
    });
    const path = process.cwd();
    let langFile = '';
    let exportString = '';
    Object.keys(this.lang).forEach(key => {
      // console.log(key, this.lang[key]);
      langFile += `const ${key}=${JSON.stringify(this.lang[key])};
      \n`;
      exportString += `${key},\n`;
    });
    langFile += `export {${exportString}};`;
    fs.mkdirSync('src/lang', { recursive: true });
    fs.writeFileSync(`${path}/src/lang/zh-CN.js`, langFile);
    console.log(
      `替换中文完成(共计${this.fileList.length}个文件，${this.replaceAmount}处中文)！可能会有部分错误，请运行项目手动修改`,
    );

    // console.log(111);
  }

  replace(filePath) {
    console.log(`处理 文件${filePath}`);
    if (
      filePath &&
      filePath.match(/.+(src).+(.js|.jsx)$/) &&
      filePath.indexOf('node_modules') === -1 &&
      filePath.indexOf('src/locales') === -1
    ) {
      let importField = 'window';
      let exportField = 'window';
      const langObj = {};
      const pathPr = process.cwd();
      const filePathAy = filePath
        .replace(pathPr, '')
        .split('/')
        .filter(item => item);
      // 通常以src下面第一层文件夹名称作为命名空间， pages内的页面用第二层作为命名空间
      if (filePathAy[1].indexOf('.') !== -1) {
        const field = lineToUpper(filePathAy[1].split('.')[0]);
        exportField = field;
        importField = `lang.${field}`;
      } else if (filePathAy[1].indexOf('pages') !== -1) {
        const field = lineToUpper(filePathAy[2].split('.')[0]);
        exportField = field;
        importField = `lang.pages.${field}`;
      } else {
        const field = lineToUpper(filePathAy[1]);
        exportField = field;
        importField = `lang.${field}`;
      }
      langObj[exportField] = {};
      let data = fs.readFileSync(filePath).toString();
      // 首先剔除字符串模板语法
      const time = new Date().getTime();
      const stringTemp = data.match(/`[^`]*`/gms) || [];
      console.log(stringTemp, 'stringTemp');
      stringTemp.forEach(item => {
        data = data.replace(item, `"${time}"`);
      });

      // jsx attr 被"'包围，并且前面包含<,=后面不带{，包含中文的字符串
      let resultJsxAttr =
        data.match(
          /(?<=<[^/<>]*=\s?["'])(?=\w*[\u4e00-\u9fa5])[^"']+(?=\s*["'].*>)/gms,
        ) || [];
      resultJsxAttr = Array.from(new Set(resultJsxAttr));
      console.log(resultJsxAttr, 'r1');
      resultJsxAttr.forEach(item => {
        data = data.replace(
          new RegExp(`(?<=<[^/<>\`]*=\\s?)["']${item}\\s*["']`, 'gms'),
          `{${importField}['${item}']}`,
        );
        langObj[exportField][item] = item;
      });

      // Jsx text  跟在>}后面，不包含<>{}的包含中文的字符串
      let resultJsxText =
        data.match(
          /(?<=[>}]+\s*)(?=\w*[\u4e00-\u9fa5])[^<>{}]+(?=\s*[<{]+)/gms,
        ) || [];
      resultJsxText = Array.from(new Set(resultJsxText));
      console.log(resultJsxText, 'r2');

      resultJsxText.forEach(item => {
        const noEnter = item.replace(/\s*/g, '').trim();
        data = data.replace(
          new RegExp(
            `(?<=[>}]+\\s*)${item.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`,
            'gms',
          ),
          `{${importField}['${noEnter}']}`,
        );
        langObj[exportField][`${noEnter}`] = `${noEnter}`;
      });

      // js 字符串 被"'包围，前面没有<>，包含中文的字符串，并且排除已经替换好的文本
      let resultJsStr =
        data.match(
          new RegExp(
            `(?<!{${importField}\\[)(?<=["'])(?=\\w*[\u4e00-\u9fa5])[^"']+`,
            'gms',
          ),
          // gms,
        ) || [];
      resultJsStr = Array.from(new Set(resultJsStr));
      console.log(resultJsStr, 'r3');

      resultJsStr.forEach(item => {
        data = data.replace(
          new RegExp(`(?<!{${importField}\\[)["']${item}["']`, 'gms'),
          `${importField}['${item}']`,
        );
        langObj[exportField][item] = item;
      });

      stringTemp.forEach(item => {
        data = data.replace(`"${time}"`, item);
      });

      // 统一引入
      data = `import * as lang from "src/lang/zh-CN";\n${data}`;

      fs.writeFileSync(filePath, data);
      this.replaceAmount +=
        resultJsStr.length + resultJsxText.length + resultJsxAttr.length;
      if (filePathAy[1].indexOf('pages') !== -1) {
        const originData = this.lang.pages || {};
        const sameData = originData[exportField] || {};
        // console.log(originData, exportField, 'originData');

        Object.assign(this.lang, {
          pages: {
            ...originData,
            [exportField]: { ...sameData, ...langObj[exportField] },
          },
        });
      } else {
        const originData = this.lang[exportField] || {};

        this.lang[exportField] = { ...originData, ...langObj[exportField] };
      }
    }
  }
}
module.exports = Replace;

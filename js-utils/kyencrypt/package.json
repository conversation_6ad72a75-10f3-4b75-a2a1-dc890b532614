{"name": "@ky/kyencrypt", "version": "1.0.9", "main": "dist/how-long-till-lunch.cjs.js", "module": "dist/how-long-till-lunch.esm.js", "browser": "dist/how-long-till-lunch.umd.js", "author": "<PERSON>", "dependencies": {"ms": "^2.0.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^11.0.1", "@rollup/plugin-node-resolve": "^7.0.0", "jsencrypt": "^3.0.0-rc.1", "rollup": "^1.29.0"}, "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "test": "node test/test.js", "pretest": "npm run build"}, "files": ["dist"], "publishConfig": {"registry": "http://artifactory.sf-express.com/artifactory/api/npm/npm-sf-local/"}}
function hexToBytes(hex) {
  for (var bytes = [], c = 0; c < hex.length; c += 2)
    bytes.push(parseInt(hex.substr(c, 2), 16));
  return bytes;
}

// 字节转十六进制
function base64Encode(buffer) {
  var binary = "";
  var bytes = new Uint8Array(buffer);
  var len = bytes.byteLength;
  for (var i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return window.btoa(binary);
}

function GetXmlHttpObject() {
  let xhr;
  if (window.XMLHttpRequest) {
    xhr = new XMLHttpRequest();
  } else {
    xhr = new window.ActiveXObject("Microsoft.XMLHTTP");
  }
  return xhr;
}
export default class KyEncrypter {
  constructor(publicKey, privateKey) {
    this.publicKey =
      publicKey ||
      "-----BEGIN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCpx+d9J3+gxB4s/IYipSB2jP13\n5fWjYUutXcADW77fgICa2d49iMvpNybxlHzzf81fAAEaxTTqtm/rt9lB/70Tvxpf\nF9mIzBFARwdYR/Yk/FSfH62+w9fQqBw7iDfFgmz/XcIYcLnKTRF1gfBvnTVfEP+n\n5Ck2y6GnpeGhU4ojdwIDAQAB\n-----END PUBLIC KEY-----";
    this.privateKey = privateKey;
    // if (!publicKey) {
    //   alert("却少公钥 publicKey");
    // }
  }
  setPrivateKey(privateKey) {
    this.privateKey = privateKey;
  }
  setPublicKey() {
    return new Promise(function (resolve, reject) {
      const xhr = GetXmlHttpObject();
      xhr.onreadystatechange = function onreadystatechange() {
        if (xhr.readyState === 4) {
          if (xhr.status === 200) {
            const res = JSON.parse(xhr.response);
            if (res.success) {
              resolve(res.obj);
            } else {
              // resolve(res.errorMessage);
              resolve();
            }
          } else {
            resolve();
          }
        }
      };

      xhr.open("GET", "/restApi/eosFmsSfpsServices/userFeedback/getPublicKey", true);
      xhr.setRequestHeader("content-type", "application/json");
      xhr.send();
    });
  }
  // 加密
  encrypt(text) {
    let that = this;
    return new Promise((resolve, reject) => {
      async function encypt() {
        var crypt = new window.JSEncrypt();
        const key = await that.setPublicKey();
        if (key) {
          crypt.setPublicKey(key);
        } else if (that.publicKey) {
          crypt.setPublicKey(that.publicKey);
        } else {
          console.log("缺少公钥");
          return;
        }
        return base64Encode(crypt.encryptLong(encodeURIComponent(text)));
      }
      if (!window.JSEncrypt) {
        // 加载
        that.loadJSEnctypt().then(() => {
          resolve(encypt());
        });
      } else {
        resolve(encypt());
      }
    });
  }
  loadScript(src) {
    return new Promise((resolve, reject) => {
      var script = document.createElement("script");
      script.src = src;
      script.onerror = function () {
        reject();
      };

      script.onload = () => {
        resolve(true);
        if (!window.JSEncrypt || window.JSEncrypt.prototype.encryptLong) return;
        window.JSEncrypt.prototype.encryptLong = function (string) {
          var k = this.getKey();
          try {
            // var lt = '';
            var ct = "";
            //RSA每次加密117bytes，需要辅助方法判断字符串截取位置
            //1.获取字符串截取点
            var bytes = new Array();
            bytes.push(0);
            var byteNo = 0;
            var len, c;
            len = string.length;
            var temp = 0;
            for (var i = 0; i < len; i++) {
              c = string.charCodeAt(i);
              if (c >= 0x010000 && c <= 0x10ffff) {
                byteNo += 4;
              } else if (c >= 0x000800 && c <= 0x00ffff) {
                byteNo += 3;
              } else if (c >= 0x000080 && c <= 0x0007ff) {
                byteNo += 2;
              } else {
                byteNo += 1;
              }
              if (byteNo % 117 >= 114 || byteNo % 117 == 0) {
                if (byteNo - temp >= 114) {
                  bytes.push(i);
                  temp = byteNo;
                }
              }
            }
            //2.截取字符串并分段加密
            if (bytes.length > 1) {
              for (var i = 0; i < bytes.length - 1; i++) {
                var str;
                if (i == 0) {
                  str = string.substring(0, bytes[i + 1] + 1);
                } else {
                  str = string.substring(bytes[i] + 1, bytes[i + 1] + 1);
                }
                var t1 = k.encrypt(str);
                ct += t1;
              }
              if (bytes[bytes.length - 1] != string.length - 1) {
                var lastStr = string.substring(bytes[bytes.length - 1] + 1);
                ct += k.encrypt(lastStr);
              }
              return hexToBytes(ct);
            }
            var t = k.encrypt(string);
            var y = hexToBytes(t);
            return y;
          } catch (ex) {
            reject();
            return false;
          }
        };
      };
      var s = document.getElementsByTagName("script")[0];
      try {
        s.parentNode.insertBefore(script, s);
      } catch (error) {
        reject();
      }
    });
  }
  loadJSEnctypt() {
    return new Promise(async (resolve, reject) => {
      var dependences = [
        "https://freight.sf-express.com/publicResource/feedback/jsencrypt.min.js",
      ];
      const loaded = await this.loadScript(dependences[0]);
      if (loaded) {
        resolve();
      } else {
        reject();
      }
    });
  }

  // 解密
  decrypt() {
    if (!this.privateKey) {
      console.error("缺少密钥");
      return false;
    }
  }
}

const encypter = new KyEncrypter();

encypter.encrypt("aaaaaa").then((cipherText) => {
  console.log(cipherText);
}); //

/* eslint-disable*/
import { addClass, removeClass, trim } from './utils/base';
import { queryXpathDoms, readXPath } from './utils/xpath';

import {
  getDomain,
  _getVisitTime,
  _getBrowser,
  _getSystem,
  _getUrl,
  _getCookie,
  _getResolution,
  _getProtocol,
  _getAgent,
  _getLanguage,
  _setUid,
  _getUid,
  _getVisitid,
} from './utils/info';

class KY {
  static getBaseUrl = () =>
    KY._debug
      ? 'http://fop.sit.sf-express.com/node'
      : 'https://freight.sf-express.com/node';

  static _token = '__easmpage__';

  static _circle_template_url = '';

  static _ilog_img_url = function() {
    return KY.getBaseUrl() + '/img/bp.gif';
  };

  static _remote_circle_data_url = '';

  static _ip = '';

  static _ip_Url = () => KY.getBaseUrl() + '/ip';

  static _requestParam = {};
  static _BG = '';
  static extraInfo = {};
  static _jsversion = 0;
  static _debug = true;
  static _targetDom = null;
  static _referrer = '';
  static _circleType = -1;
  static _circleList = [];
  static _graph = null;
  static hockDom = {};

  static scriptImages = {
    appends: [],
    removeAll() {
      var len = KY.scriptImages.appends.length;
      for (var i = 0; i < len - 1; i++) {
        var tdom = KY.scriptImages.appends[i];
        if (tdom) {
          var pNode = tdom.parentNode;
          if (pNode) {
            pNode.removeChild(tdom);
          }
        }
      }
      KY.scriptImages.appends = [KY.scriptImages.appends[len - 1]];
    },
    addImagesScript(tdom) {
      KY.scriptImages.appends.push(tdom);
    },
  };

  static bindILog = {
    clickFun: function(e) {
      e = e || window.event;
      var target = e.srcElement ? e.srcElement : e.target;
      var tagName = target.tagName.toLowerCase();
      var xpath = readXPath(target);
      var scrollTop = 0,
        scrollLeft = 0;
      if (document.documentElement && document.documentElement.scrollTop) {
        scrollTop = document.documentElement.scrollTop;
        scrollLeft = document.documentElement.scrollLeft;
      } else if (document.body) {
        scrollTop = document.body.scrollTop;
        scrollLeft = document.body.scrollLeft;
      }
      var x = e.clientX + scrollLeft;
      var y = e.clientY + scrollTop;
      var xy = x + '-' + y;
      var text = '';
      var href = '';
      if (tagName == 'img') {
        text = target.getAttribute('src');
      } else if (tagName == 'a') {
        href = target.getAttribute('href') || '';
        text = trim(target.innerHTML.replace(/<(.*)?>/g, '')); //只取标签里面所有的文案，凡是有标签的地方都换成空格
      } else {
        text = trim(target.innerHTML.replace(/<(.*)?>/g, ''));
      }

      if (target.getAttribute('data-ilog')) {
        // var ilogData = target.dataset.ilog
        // somecode....
      }
      KY.log('绑定监听事件 clickFun', xpath, text, tagName, href, xy);
      KY.sendClick(xpath, text, tagName, href, xy);
    },
  };

  static hrefArr = [];

  static hrefIndex = window.location.href;

  static _setSpaReferUrl = () => {
    KY.hrefArr.push(window.location.href);
    if (KY.hrefArr.length == 1) {
      KY._referrer = KY.hrefIndex;
    }
    if (KY.hrefArr.length > 1) {
      KY._referrer = KY.hrefArr[KY.hrefArr.length - 2];
    }
    if (KY.hrefArr.length == 0) {
      KY._referrer = document.referrer;
    }
  };

  static _spaGo = () => {
    KY._setSpaReferUrl();
    setTimeout(function() {
      KY.sendVisitLog();
    }, 500);
  };

  /**
   * 日志打印方法
   */
  static log = (...args) => {
    if (KY._debug) {
      console.log('time:' + new Date(), args);
    }
  };

  /**
   * 用id获取dom元素
   */
  static $ = id => {
    return document.getElementById(id);
  };

  /**
   * 向无痕埋点管理系统父页面发送消息（没有嵌套在iframe的情况下，父页面就是自己）
   */
  static postMessage = (actionType, data) => {
    KY.log('内页面发送 ', actionType, data);
    var postData = {
      token: KY._token,
      action_type: actionType,
      data: data,
    };
    window.parent.postMessage(postData, '*');
  };

  /**
   * 监听来自xflow父页面的postMessage事件
   */
  static messageEventListener = () => {
    window.addEventListener(
      'message',
      function(e) {
        if (e.source != window.parent) return;
        var data = e.data || {};
        var heatmapData = data.data._heatmapData;
        if (data.token != KY._token) {
          return;
        }

        if (
          data.action_type == 'check_load_sdk' ||
          data.action_type == 'check_load_sdk_heatmap'
        ) {
          //当父页面发放圈选或者热力图的action
          var checkSdk = data.action_type;
          KY._circle_template_url = data.data._circle_template_url;
          KY._remote_circle_data_url = data.data._remote_circle_data_url;
          KY._requestParam = data.data._params;
          KY.log('RECEIVE DATAS:', data.data);

          KY.postMessage('had_load_sdk', {}); //发送已load脚本的通知

          var tpl = `<style>
          .madeasm-circle-hovered,
          .madeasm-circle-covered,
          .madeasm-circle-existed {
              outline: 2px dashed #969696 !important;
              outline-offset: -2px !important;
          }
      
          #madeasm-circle-dlg {
              display: none;
      
          }
      
          .header {
              text-align: center;
              height: 40px;
              line-height: 40px;
              border-bottom: 1px solid #E5E5E5;
          }
      
          .body {}
      
          .footer {
              height: 56px;
              display: flex;
              align-items: center;
              justify-content: center;
          }
      
          button {
              border: 0;
              outline: none;
              width: 65px;
              height: 32px;
              margin-right: 20px;
              font-family: PingFangSC-Regular, sans-serif;
              border-radius: 4px;
              font-size: 14px;
              text-align: center;
          }
      
          #madeasm-circle-exist-save {
              background: #DC1E32;
              color: #fff;
          }
      
          #madeasm-circle-exist-delete {
              display: none;
              background: #FFFFFF;
              border: 1px solid #4A4A4A;
      
              color: #4A4A4A;
          }
      
          #madeasm-circle-exist-cancel {
              background: #FFFFFF;
              border: 1px solid #DC1E32;
              color: #DC1E32;
          }
      
          .inbl {
              display: inline-block;
          }
      
          .mask {
              position: fixed;
              top: 0;
              left: 0;
              background: rgba(0, 0, 0, 0.6);
              width: 100vw;
              height: 100vh;
              z-index: 10;
          }
      
          .mask+div {
              position: fixed;
              width: 320px;
             /* height: 300px; */
              top: 50%;
              left: 50%;
              background: #FFFFFF;
              box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
              border-radius: 4px;
              box-sizing: border-box;
              transform: translate(-50%, -50%);
              padding: 20px 20px 0;
              /* background: #fff; */
              z-index: 1001;
          }
      
          #madeasm-circle-pvuvgraph-block {
              background: #FFF;
              border: 1px solid #E0E0E0;
              border-radius: 2px;
              z-index: 999998;
              width: 300px;
              height: 213px;
              margin:0 auto;
          }
      
          #madeasm-heatmapb {
              display: none;
              position: absolute;
              width: 100%;
              /* height: 100vh; */
              left: 0;
              top: 0;
              z-index: 999998;
          }
      
          #madeasm-heatmapc {
              display: none;
              position: absolute;
              width: 100%;
              /* height: 100vh; */
              left: 0;
              top: 0;
              z-index: 999998;
          }
      
          #madeasm-circle-curdom {
              font-family: PingFangSC-Regular, sans-serif;
              font-size: 12px;
              text-align: center;
              line-height: 32px;
              color: #2196F3;
              background: #F2FBFF;
              border: 1px solid #2196F3;
              border-radius: 4px;
              width: 80px;
              height: 32px;
          }
      
          #madeasm-circle-curdom,
          #madeasm-circle-curpostion,
          #madeasm-circle-likedom {
              display: inline-block;
              margin-right: 10px;
          }
      
          #madeasm-circle-curpostion {
              font-family: PingFangSC-Regular, sans-serif;
              font-size: 12px;
              text-align: center;
              line-height: 32px;
              color: #FE7200;
              background: #FFF8F2;
              border: 1px solid #FE7200;
              border-radius: 4px;
              width: 80px;
              height: 32px;
          }
      
          #madeasm-circle-likedom {
              font-family: PingFangSC-Regular, sans-serif;
              font-size: 12px;
              text-align: center;
              line-height: 32px;
              color: #DC1E32;
              background: #FFF8F2;
              border: 1px solid #DC1E32;
              border-radius: 4px;
              width: 80px;
              height: 32px;
          }
      
          #madeasm-circle-curdom-block {
              margin: 13px 0;
              font-family: PingFangSC-Regular, sans-serif;
              font-size: 14px;
              color: #4A4A4A;
              padding-left: 100px;
          }
      
          .ky-input-field {
              margin-bottom: 13px;
              display: flex;
              align-items: center;
          }
      
      
      
          .ky-input-field>label {
              display: inline-block;
              width: 100px;
              font-family: PingFangSC-Regular, sans-serif;
              font-size: 14px;
              color: #333333;
              letter-spacing: 0;
              text-align: right;
          }
      
          .ky-input-field>label+div,
          .ky-input-field>label+input {
              flex: 1;
          }
      
          #madeasm-circle-name {
              font-family: PingFangSC-Regular, sans-serif;
              font-size: 14px;
              color: #000000;
              width: 80%;
              height: 32px;
              background: #FFFFFF;
              border: 1px solid #DC1E32;
              border-radius: 2px;
              padding: 0 16px;
          }
      </style>
      
      <div id="madeasm-heatmapb"></div>
      <div id="madeasm-heatmapc"></div>
      
      <div id="madeasm-circle-dlg">
          <div class="mask"></div>
          <div>
              <div class="body">
      
                  <div class="ky-input-field">
                      <label>HTML标签：</label>
                      <div id="madeasm-circle-domtype"></div>
                  </div>
      
                  <div class="ky-input-field">
                      <label>名称：</label>
                      <div>
                          <input id="madeasm-circle-name" />
                      </div>
                  </div>
      
      
      
                  <div class="ky-input-field">
                      <label>圈选类型：</label>
                      <div>
                          <div class="inbl" id="madeasm-circle-curdom">当前元素</div>
                          <div class="inbl" id="madeasm-circle-curpostion">当前位置</div>
                          <div class="inbl" id="madeasm-circle-likedom">LikeDOM</div>
                      </div>
                  </div>
      
                  <div id="madeasm-circle-curdom-block">curdom</div>
                  <div id="madeasm-circle-curpostion-block">curpostion</div>
                  <div id="madeasm-circle-likedom-block">likedom</div>
      
                  <div id="madeasm-circle-pvuvgraph-block"></div>
              </div>
              <div class="footer">
                  <button id="madeasm-circle-exist-save" onclick="window._MA.save()">
                      保存
                  </button>
      
                  <button id="madeasm-circle-exist-delete" onclick="window._MA.del()">
                      删除
                  </button>
      
                  <button id="madeasm-circle-exist-cancel" onclick="window._MA.cancel()">
                      取消
                  </button>
              </div>
              <div>
              </div>`;

          // KY.get(
          //   KY._circle_template_url,
          //   function(data) {
          //请求来自父页面的url，获取圈选模板css
          // data = data.html;

          var body = document.getElementsByTagName('BODY').item(0);

          body.innerHTML += tpl;
          KY.hockDomClick(checkSdk); //监听鼠标悬浮
          KY.queryExistCircle(checkSdk); //获取已圈选的元素
          if (!window._MA) {
            window._MA = {
              cancel: KY.cancel,
              save: KY.save,
              del: KY.del,
              showConfigDlg: KY.showConfigDlg,
            };
          }

          KY.removeIlogListener(); //因为是在iframe内，所以取消无痕埋点
          if (checkSdk == 'check_load_sdk_heatmap') {
            // 如果是处于热力图页面，调用显示热力图的方法
            KY.heatmapCover(heatmapData);
            KY.resetMapp(window, heatmapData);
          }
          // },
          // KY._requestParam || {},
          // );
          //加载echarts
          var vds = document.createElement('script');
          vds.type = 'text/javascript';
          vds.async = true;
          vds.src = '//cdn.bootcss.com/echarts/4.3.0-rc.2/echarts.min.js';
          var s = document.getElementsByTagName('script')[0];
          s.parentNode.insertBefore(vds, s);
        } else if (data.action_type == 'reload_existed_circle') {
          KY.queryExistCircle();
        } else if (data.action_type == 'show_graph_data') {
          // 显示PV,UV折线图表
          var graph = document.getElementById('madeasm-circle-pvuvgraph-block'),
            rows =
              data.data && data.data.model && data.data.model.rows
                ? data.data.model.rows
                : [];
          if (rows.length == 0) {
            //post过来的data没有pvuv数据
            if (KY._graph) {
              KY._graph.dispose();
              KY._graph = null;
            }
            graph.innerHTML = '没有PVUV数据...';
            return;
          }
          var option = {
            tooltip: {
              trigger: 'axis',
            },
            grid: {
              top: '10%',
              bottom: '10%',
              left: '10%',
              right: '10%',
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: [],
            },
            yAxis: {
              type: 'value',
              axisLabel: {
                formatter: '{value}',
              },
            },
            series: [
              {
                name: 'pv值',
                type: 'line',
                data: [],
              },
              {
                name: 'uv值',
                type: 'line',
                data: [],
              },
            ],
          };
          if (rows.length > 0) {
            var date = option.xAxis.data,
              pv = option.series[0].data,
              uv = option.series[1].data;
            rows.forEach(function(item, index, input) {
              date.push(item.bizdate);
              pv.push(item.pv);
              uv.push(item.uv);
            });
            if (KY._graph == null) {
              KY._graph = echarts.init(graph); //调用echars的初始化api
            }
            KY._graph.setOption(option); //调用echarts的参数api
            for (
              var i = 0,
                cvs = graph.getElementsByTagName('canvas'),
                len = cvs.length;
              i < len;
              i++
            ) {
              cvs[i].setAttribute('data-circle-flag', 0);
            }
            graph.style.display = 'block';
          } else {
            graph.style.display = 'block';
          }
        }
      },
      false,
    );
  };

  static _getExtraInfo = function() {
    var reStr = '';
    var extrObj = KY.extraInfo;
    // if (window._sfls && window._sfls.extraInfo) {
    //   extrObj = window._sfls['extraInfo'];
    // }
    for (var k in extrObj) {
      var v = extrObj[k];
      if (!v) {
        v = '';
      }
      reStr += '"' + k + '"' + ':"' + v + '",';
    }
    return reStr;
  };

  /**
   * 展示热力图
   */
  static heatmapCover = heatmapData => {
    var heatmapb = KY.$('madeasm-heatmapb');
    var heatmapc = KY.$('madeasm-heatmapc');
    heatmapb.style.display = 'block';
    heatmapc.style.display = 'block';
    heatmapb.style.height = document.body.scrollHeight + 'px';
    if (document.body.scrollHeight <= 800) {
      heatmapb.style.height = '800px';
    }
    heatmapb.style.width = document.body.scrollWidth + 'px';
    KY.setMapp(heatmapData);
  };

  static setMapp = heatmapData => {
    for (var i = 0; i < heatmapData.length; i++) {
      var xpathz = heatmapData[i].xpath;
      var xdom = queryXpathDoms(xpathz);
      if (xdom == null) {
        continue;
      }
      //兼容
      if (KY.hasFixed(xdom) == 1) {
        var xVal = KY.getOffsetTL(xdom).x + window.pageXOffset;
        var yVal = KY.getOffsetTL(xdom).y + window.pageYOffset;
        var xWidth = xdom.offsetWidth;
        var yHeight = xdom.offsetHeight;
      } else {
        var xVal = KY.getOffsetTL(xdom).x;
        var yVal = KY.getOffsetTL(xdom).y;
        var xWidth = xdom.offsetWidth;
        var yHeight = xdom.offsetHeight;
      }

      var buttonName = heatmapData[i].button_name;
      if (heatmapData[i].button_name == '') {
        buttonName = '此元素未设置名称';
      }
      var clickUsercnt = heatmapData[i].click_usercnt;
      var clickCnt = heatmapData[i].click_cnt;
      var clickRate = heatmapData[i].click_ration;
      KY.insertMapc(
        xVal,
        yVal,
        xWidth,
        yHeight,
        buttonName,
        clickUsercnt,
        clickCnt,
        clickRate,
      );
    }
  };
  /**
   * 检查元素是否被用了fixed的父元素包含或者本身就用了fixed定位
   */
  static hasFixed = xdom => {
    while (xdom.tagName != 'BODY') {
      if (!+[1]) {
        // ????  怀疑在某种低版本浏览器(ie)为true
        var xdomPos = xdom.currentStyle.position;
      } else {
        var xdomPos = window.getComputedStyle(xdom, null).position;
      }
      if (xdomPos == 'fixed') {
        return 1;
      }
      xdom = xdom.parentNode;
    }
    return 0;
  };
  /**
   * 获取元素相对页面的x、y坐标
   */
  static getOffsetTL = function(e) {
    var x = e.offsetLeft;
    var y = e.offsetTop;
    while ((e = e.offsetParent)) {
      x += e.offsetLeft;
      y += e.offsetTop;
    }
    return {
      x: x,
      y: y,
    };
  };
  /**
   * 重置热力图
   */
  static resetMapp = (e, heatmapData) => {
    if (e.addEventListener) {
      e.addEventListener(
        'scroll',
        function() {
          var heatmapc = KY.$('madeasm-heatmapc'); //热力图节点
          heatmapc.innerHTML = '';
          KY.setMapp(heatmapData);
          setTimeout(function() {
            heatmapc.innerHTML = '';
            KY.setMapp(heatmapData);
          }, 1000);
        },
        false,
      );
    } else {
      e.attachEvent('onscroll', function() {
        heatmapc.innerHTML = '';
        KY.setMapp(heatmapData);
        setTimeout(function() {
          var heatmapc = KY.$('madeasm-heatmapc');
          heatmapc.innerHTML = '';
          KY.setMapp(heatmapData);
        }, 1000);
      });
    }
  };
  /**
   * 柯里化的形式兼容事件处理函数
   */
  static eventHandler = (function() {
    if (document.addEventListener) {
      return function(ele, event, fn) {
        ele.addEventListener(event, function() {
          fn.call(this);
        });
      };
    } else if (document.attachEvent) {
      return function(ele, event, fn) {
        ele.addEventListener('on' + event, function() {
          fn.call(this);
        });
      };
    } else {
      return function(ele, event, fn) {
        ele['on' + event] = function() {
          fn.call(this);
        };
      };
    }
  })();

  /**
   * 插入热力点
   * @param {buttonName} 按钮名称
   * @param {clickUsercnt} 点击人数
   * @param {clickCnt} 点击次数
   * @param {clickRate} 点击率
   */
  static insertMapc = function(
    xVal,
    yVal,
    xWidth,
    yHeight,
    buttonName,
    clickUsercnt,
    clickCnt,
    clickRate,
  ) {
    var colorRate = clickRate.replace('%', '') / 100;
    var colorRageia = '#cbcbcb';
    var colorRageib = '#cbcbcb';
    var colorRageja = '#cbcbcb';
    var colorRagejb = '#cbcbcb';
    var colorRageka = '#cbcbcb';
    var colorRagekb = '#cbcbcb';
    if (colorRate >= 0 && colorRate < 0.166) {
      colorRageia = 'rgba(0,14,223,1)';
      colorRageib = 'rgba(0,14,223,0)';
      colorRageja = 'rgba(0,14,223,1)';
      colorRagejb = 'rgba(0,14,223,0)';
      colorRageka = 'rgba(0,14,223,1)';
      colorRagekb = 'rgba(0,14,223,0)';
    }
    if (colorRate >= 0.166 && colorRate < 0.332) {
      colorRageia = 'rgba(0,14,223,1)';
      colorRageib = 'rgba(0,14,223,0)';
      colorRageja = 'rgba(0,14,223,1)';
      colorRagejb = 'rgba(0,14,223,0)';
      colorRageka = 'rgba(1,184,67,1)';
      colorRagekb = 'rgba(1,184,67,0)';
    }
    if (colorRate >= 0.332 && colorRate < 0.498) {
      colorRageia = 'rgba(0,14,223,1)';
      colorRageib = 'rgba(0,14,223,0)';
      colorRageja = 'rgba(1,184,67,1)';
      colorRagejb = 'rgba(1,184,67,0)';
      colorRageka = 'rgba(60,255,0,1)';
      colorRagekb = 'rgba(60,255,0,0)';
    }
    if (colorRate >= 0.498 && colorRate < 0.664) {
      colorRageia = 'rgba(1,184,67,1)';
      colorRageib = 'rgba(1,184,67,0)';
      colorRageja = 'rgba(60,255,0,1)';
      colorRagejb = 'rgba(60,255,0,0)';
      colorRageka = 'rgba(219,255,0,1)';
      colorRagekb = 'rgba(219,255,0,0)';
    }
    if (colorRate >= 0.664 && colorRate < 0.83) {
      colorRageia = 'rgba(60,255,0,1)';
      colorRageib = 'rgba(60,255,0,0)';
      colorRageja = 'rgba(219,255,0,1)';
      colorRagejb = 'rgba(219,255,0,0)';
      colorRageka = 'rgba(255,146,0,1)';
      colorRagekb = 'rgba(255,146,0,0)';
    }
    if (colorRate >= 0.83 && colorRate <= 1) {
      colorRageia = 'rgba(219,255,0,1)';
      colorRageib = 'rgba(219,255,0,0)';
      colorRageja = 'rgba(255,146,0,1)';
      colorRagejb = 'rgba(255,146,0,0)';
      colorRageka = 'rgba(208,2,27,1)';
      colorRagekb = 'rgba(208,2,27,0)';
    }
    //某一个热力点用三个div显示（为了显示渐变色）
    var divi = document.createElement('div');
    var divj = document.createElement('div');
    var divk = document.createElement('div');
    var heatmapb = KY.$('madeasm-heatmapb');
    var heatmapc = KY.$('madeasm-heatmapc');
    divi.setAttribute(
      'style',
      'position:absolute;left:' +
        parseFloat(xVal) +
        'px;top:' +
        parseFloat(yVal) +
        'px;z-index:999991;width:' +
        parseFloat(xWidth) +
        'px;height:' +
        parseFloat(yHeight) +
        'px;background: radial-gradient(closest-side, ' +
        colorRageia +
        ', ' +
        colorRageib +
        ');cursor: pointer;opacity:0.5',
    );
    divj.setAttribute(
      'style',
      'position:absolute;left:' +
        (parseFloat(xWidth) * 0.1 + parseFloat(xVal)) +
        'px;top:' +
        (parseFloat(yHeight) * 0.1 + parseFloat(yVal)) +
        'px;z-index:999992;width:' +
        parseFloat(xWidth) * 0.8 +
        'px;height:' +
        parseFloat(yHeight) * 0.8 +
        'px;background: radial-gradient(closest-side ,' +
        colorRageja +
        ', ' +
        colorRagejb +
        ');cursor: pointer;opacity:0.6',
    );
    divk.setAttribute(
      'style',
      'position:absolute;left:' +
        (parseFloat(xWidth) * 0.2 + parseFloat(xVal)) +
        'px;top:' +
        (parseFloat(yHeight) * 0.2 + parseFloat(yVal)) +
        'px;z-index:999993;width:' +
        parseFloat(xWidth) * 0.6 +
        'px;height:' +
        parseFloat(yHeight) * 0.6 +
        'px;background: radial-gradient(closest-side ,' +
        colorRageka +
        ', ' +
        colorRagekb +
        ');cursor: pointer;opacity:1',
    );
    var divhLeft = parseFloat(xWidth) / 2 + parseFloat(xVal) + 5; //提示框的横坐标
    var divhTop = parseFloat(yHeight) / 2 + parseFloat(yVal) + 5; //提示框的纵坐标
    if (divhLeft + 207 > 1138) {
      divhLeft = parseFloat(xWidth) / 2 + parseFloat(xVal) - 5 - 207;
    }
    if (divhTop + 144 > document.body.scrollHeight) {
      divhTop = parseFloat(yHeight) / 2 + parseFloat(yVal) - 5 - 144;
    }
    var divh = KY.$('madeasm-hover');
    var divh = document.createElement('div'); //创建数据提示框
    divh.setAttribute(
      'style',
      'position:absolute;left:' +
        divhLeft +
        'px;top:' +
        divhTop +
        'px;z-index:999995;display:none;width:207px;height:144px;background: rgba(31,45,61,0.85);border-radius: 2px;',
    );
    var mh0 = document.createElement('div');
    var mh1 = document.createElement('div');
    var mh2 = document.createElement('div');
    var mh3 = document.createElement('div');
    var mh4 = document.createElement('div');
    mh0.setAttribute(
      'style',
      'position: absolute;left:20px;top:20px;width:5px;height:5px;background:#CE2E2C;border-radius:5px;',
    );
    mh1.setAttribute(
      'style',
      'width:120px;height:20px;margin:14px 0px 10px 36px;font-family:PingFangHK-Regular;font-size:14px;color:#FFFFFF;letter-spacing:0;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;',
    );
    mh2.setAttribute(
      'style',
      'width:120px;height:20px;margin:0px 0px 10px 36px;font-family:PingFangHK-Regular;font-size:14px;color:#FFFFFF;letter-spacing:0;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;',
    );
    mh3.setAttribute(
      'style',
      'width: 120px;height:20px;margin:0px 0px 10px 36px;font-family:PingFangHK-Regular;font-size:14px;color:#FFFFFF;letter-spacing:0;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;',
    );
    mh4.setAttribute(
      'style',
      'width: 120px;height:20px;margin:0px 0px 20px 36px;font-family:PingFangHK-Regular;font-size:14px;color:#FFFFFF;letter-spacing:0;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;',
    );
    divh.appendChild(mh0);
    divh.appendChild(mh1);
    divh.appendChild(mh2);
    divh.appendChild(mh3);
    divh.appendChild(mh4);
    mh1.innerHTML = buttonName;
    mh2.innerHTML = '点击人数：' + clickUsercnt;
    mh3.innerHTML = '点击次数：' + clickCnt;
    mh4.innerHTML = '点击率：' + clickRate;
    heatmapc.appendChild(divi);
    heatmapc.appendChild(divj);
    heatmapc.appendChild(divk);
    heatmapc.appendChild(divh);
    KY.mapcHoveron(divi, divh);
    KY.mapcHoverout(divi, divh);
    KY.mapcHoveron(divj, divh);
    KY.mapcHoverout(divj, divh);
    KY.mapcHoveron(divk, divh);
    KY.mapcHoverout(divk, divh);
    KY.mapcHoveron(divh, divh);
    KY.mapcHoverout(divh, divh);
  };

  /**
   * 鼠标移入divi，显示divh
   */
  static mapcHoveron = (divi, divh) => {
    if (divi.addEventListener) {
      divi.addEventListener(
        'mouseover',
        function() {
          divh.style.display = 'block';
        },
        false,
      );
    } else {
      divi.attachEvent('onmouseover', function() {
        divh.style.display = 'block';
      });
    }
  };
  /**
   * 鼠标移出divi，隐藏divh
   */
  static mapcHoverout = (divi, divh) => {
    if (divi.addEventListener) {
      divi.addEventListener(
        'mouseout',
        function() {
          divh.style.display = 'none';
        },
        false,
      );
    } else {
      divi.attachEvent('onmouseout', function() {
        divh.style.display = 'none';
      });
    }
  };

  static hockDomClick = function(checkSdks) {
    if (checkSdks == 'check_load_sdk_heatmap') {
      //如果是热力图命令则返回false，只处理圈选命令
      return false;
    }
    document.body.addEventListener(
      'mouseover',
      function(e) {
        e = e || window.event;
        var className = e.target.getAttribute('class') || '';
        if (
          className.indexOf('madeasm-circle-hovered') > -1 ||
          KY.$('madeasm-circle-dlg').style.display === 'block'
        ) {
          //已悬浮在元素上方，返回false
          return;
        }
        if (e.target.getAttribute('data-circle-flag')) {
          //已悬浮在弹出对话框内，返回false
          return;
        }
        addClass(e.target, 'madeasm-circle-hovered'); //加上悬浮样式（红框）

        /**
         * 鼠标离开元素，去掉悬浮样式
         */
        KY.hockDom.domMouseOutEvent = function(e1) {
          removeClass(e.target, 'madeasm-circle-hovered');
          e.target.removeEventListener('mouseout', KY.hockDom.domMouseOutEvent);
        };
        e.target.addEventListener(
          'mouseout',
          KY.hockDom.domMouseOutEvent,
          false,
        );

        /**
         * 点击悬浮元素，显示对话框
         */
        if (!KY.hockDom.domClickEvent) {
          KY.hockDom.domClickEvent = function(e2) {
            e2 = e2 || window.event;
            e2.preventDefault();
            if (
              KY.hockDom.eventTime &&
              new Date() * 1 - KY.hockDom.eventTime < 100
            ) {
              return;
            }
            KY.hockDom.eventTime = new Date() * 1;
            if (e2.target.getAttribute('data-circle-flag')) {
              //已打开对话框，返回
              return;
            }
            if (KY._targetDom) {
              //关闭已有对话框
              removeClass(KY._targetDom, 'madeasm-circle-covered');
            }
            KY._targetDom = e2.target;
            KY.showConfigDlg(0);
          };
        }
        e.target.addEventListener('click', KY.hockDom.domClickEvent, false);
      },
      false,
    );
  };

  /**
   * 显示配置对话框
   */
  static showConfigDlg = function(type) {
    var showFn = function(domInfo) {
      var typeText = [
        function(text) {
          return '统计文本为 “' + text + '” 的元素。 文本不同将不统计。';
        },
        function() {
          return '统计当前位置的数据，例如广告位，出现的图片经常变化，此时统计出现在该广告位的所有图片的数据。';
        },
        function() {
          return '统计同类元素，例如商品列表页面中出现多个商品，此时统计出现在该列表中的所有商品。页面中绿色高亮显示。';
        },
      ][domInfo.type];
      var typeName = ['curdom', 'curpostion', 'likedom'][domInfo.type];
      KY._circleType = domInfo.type;
      addClass(KY._targetDom, 'madeasm-circle-covered'); //添加选中样式

      var circleDlg = KY.$('madeasm-circle-dlg'); //显示对话框
      circleDlg.style.display = 'block';

      /**
       *
       */
      var domTagName = (KY._targetDom.tagName || '').toUpperCase();
      var domtypeDom = KY.$('madeasm-circle-domtype');
      domtypeDom.innerHTML = '&lt;' + domTagName + '&gt;';
      var nameDom = KY.$('madeasm-circle-name');
      if (domTagName == 'IMG') {
        var srcSplit = KY._targetDom.getAttribute('src').split('/');
        nameDom.value = domInfo.name || srcSplit[srcSplit.length - 1];
      } else {
        nameDom.value =
          domInfo.name ||
          KY._targetDom.innerHTML
            .replace(/<(.*?)>/g, '')
            .replace(/[\f\n\r\t\v\ ]/g, '');
      }
      if (domTagName == 'A' || domTagName == 'IMG') {
        KY.$('madeasm-circle-curpostion').style.display = '';
        KY.$('madeasm-circle-likedom').style.display = 'none';
      } else {
        KY.$('madeasm-circle-curpostion').style.display = 'none';
        KY.$('madeasm-circle-likedom').style.display = '';
      }
      var tdom = null;
      tdom = KY.$('madeasm-circle-curpostion');
      removeClass(tdom, 'madeasm-circle-active');
      tdom = KY.$('madeasm-circle-likedom');
      removeClass(tdom, 'madeasm-circle-active');
      tdom = KY.$('madeasm-circle-curdom');
      removeClass(tdom, 'madeasm-circle-active');
      // tdom =KY.$("madeasm-circle-" + typeName);
      // addClass(tdom, "madeasm-circle-active");
      tdom = KY.$('madeasm-circle-curpostion-block');
      tdom.style.display = 'none';
      tdom = KY.$('madeasm-circle-likedom-block');
      tdom.style.display = 'none';
      tdom = KY.$('madeasm-circle-curdom-block');
      tdom.style.display = 'none';
      // tdom =KY.$("madeasm-circle-" + typeName + "-block");
      // tdom.style.display = "block";
      if (
        KY._targetDom.getAttribute('class').indexOf('madeasm-circle-existed') >
        -1
      ) {
        //是否显示删除按钮
        KY.$('madeasm-circle-exist-delete').style.display = 'block';
      } else {
        KY.$('madeasm-circle-exist-delete').style.display = 'none';
      }
      tdom.innerHTML = typeText(nameDom.value);
      //处理手动埋点
      var dataIlog = KY._targetDom.getAttribute('data-ilog');
      var xpath = readXPath(KY._targetDom);
      KY.log('获取到XPATH,dataIlog: ', xpath, dataIlog, location.href);
      KY.postMessage('fetch_graph_data', {
        xpath: xpath,
        datailog: dataIlog,
        url: location.href,
        type: domInfo.type,
      });
    };
    var domInfo = {
      name: null,
      xpath: null,
      type: type,
    };
    /**
     * 如果是已经圈选并保存过的元素，获取之前保存的信息
     */
    if (
      KY._targetDom.getAttribute('class').indexOf('madeasm-circle-existed') > -1
    ) {
      var curDomXpath = readXPath(KY._targetDom);
      for (var i = 0; i < KY._circleList.length; i++) {
        var t = KY._circleList[i];
        if (t.xpath == curDomXpath) {
          domInfo = {
            name: t.name,
            xpath: t.xpath,
            type: t.circleType,
          };
        }
      }
      showFn(domInfo);
    } else {
      showFn(domInfo);
    }
  };
  /**
   * 取消目标点
   */
  static cancel = function() {
    removeClass(KY._targetDom, 'madeasm-circle-covered');
    document.getElementById('madeasm-circle-dlg').style.display = 'none';
    KY._targetDom = null;
  };
  /**
   * 保存当前圈选信息
   */
  static save = function() {
    var name = trim(KY.$('madeasm-circle-name').value);
    var id = KY._targetDom.getAttribute('madeasm-circle-existed-id');
    id = id ? id : null;
    var circleType = KY._circleType;
    var xpath = readXPath(KY._targetDom);
    var url = _getUrl();
    var postData = {
      name: name,
      circleType: circleType,
      xpath: xpath,
      url: url,
      id: id,
    };
    KY.log('save info : ', postData);
    KY.postMessage('save_circle', postData);
    KY._circleList.push({
      xpath: xpath,
      circleType: circleType,
      name: name,
      dom: KY._targetDom,
    });
    addClass(KY._targetDom, 'madeasm-circle-existed'); //保存后标识目标dom节点
    document.getElementById('madeasm-circle-dlg').style.display = 'none';
  };

  /**
   * 删除圈选的节点
   */
  static del = function() {
    var xpath = readXPath(KY._targetDom);
    var id = KY._targetDom.getAttribute('madeasm-circle-existed-id');
    id = id ? id : null;
    if (!id) {
      return;
    }
    var url = KY._getUrl();
    var postData = {
      xpath: xpath,
      url: url,
      id: id,
    };
    var tlist = [];
    for (var i = 0; i < KY._circleList.length; i++) {
      var one = KY._circleList[i];
      if (one.xpath != xpath) {
        tlist.push(one);
      }
    }
    KY._circleList = tlist;
    KY.log('delete info : ', postData);
    KY.postMessage('delete_circle', postData);
    removeClass(KY._targetDom, 'madeasm-circle-covered');
    removeClass(KY._targetDom, 'madeasm-circle-existed'); //去除选定样式
    KY._targetDom.setAttribute('madeasm-circle-existed-id', '');
    document.getElementById('madeasm-circle-dlg').style.display = 'none';
    KY._targetDom = null;
  };

  /**
   * 获取已圈选的区域
   */
  static queryExistCircle = function(checkSdks) {
    if (checkSdks == 'check_load_sdk_heatmap') {
      return false;
    }
    for (var i = 0; i < KY._circleList.length; i++) {
      removeClass(KY._circleList[i].dom, 'madeasm-circle-existed');
    }

    /**
     * 通过传页面url请求当前页面的已圈选元素
     */
    KY.get(
      KY._remote_circle_data_url + '?url=' + encodeURIComponent(_getUrl()),
      function(data) {
        //data相当于一个xpath数组
        KY.log('LIST_API : ', data);
        KY._circleList = [];
        for (var i = 0; i < data.length; i++) {
          var one = data[i];
          var tmpDom = queryXpathDoms(one.xpath);
          if (tmpDom) {
            tmpDom.setAttribute('madeasm-circle-existed-id', one.id);
            one.dom = tmpDom;
            KY._circleList.push(one);
            addClass(one.dom, 'madeasm-circle-existed'); //添加圈选样式
          }
        }
      },
      KY._requestParam || {},
    );
  };

  static bindIlogListener = function() {
    if (document.addEventListener) {
      document.addEventListener('click', KY.bindILog.clickFun, false);
    } else {
      document.attachEvent('onclick', KY.bindILog.clickFun);
    }
  };

  static removeIlogListener = function() {
    if (document.removeEventListener) {
      document.removeEventListener('click', KY.bindILog.clickFun, false);
    } else {
      document.detachEvent('onclick', KY.bindILog.clickFun);
    }
  };

  static sendClick = function(xpath, text, tagName, href, xy) {
    var params =
      'msg_type=circle_click' +
      '^xpath=' +
      encodeURIComponent(xpath) +
      '^text=' +
      encodeURIComponent(text).substr(0, 100) +
      '^href=' +
      encodeURIComponent(href) +
      '^xy=' +
      xy +
      '^tagName=' +
      tagName +
      '^' +
      KY.getBaseParams() +
      '^ip=' +
      KY._ip +
      '^siteId=' +
      KY.getSiteId();
    var url = KY._ilog_img_url() + '?data=' + params;

    KY.sendDataFun.send(url);
  };

  static _getReferUrl = () => {
    if (document.referrer == '') {
      return KY._referrer;
    } else {
      return document.referrer;
    }
  };

  static sendDataFun = {
    send: function(url) {
      var logImage = new Image();
      logImage.src = url + '^_=' + new Date() * 1;
      logImage.style.visibility = 'hidden';
      logImage.style.position = 'absolute';
      logImage.style.left = 0;
      logImage.style.top = 0;
      logImage.style.zIndex = -1;
      var m = document.getElementsByTagName('script')[0];
      m.parentNode.insertBefore(logImage, m); //通过添加一个带特定src的图片来做数据上报
      KY.scriptImages.addImagesScript(logImage);
      KY.scriptImages.removeAll();
    },
  };

  static sendEvent = function(obj) {
    var params =
      'msg_type=event_log' +
      '^' +
      KY.getBaseParams() +
      'event_type:"' +
      encodeURIComponent(obj.event_type) +
      '",' +
      'event_value:"' +
      encodeURIComponent(obj.event_value) +
      '",' +
      'event_description:"' +
      encodeURIComponent(obj.event_description) +
      '"' +
      '^ip=' +
      KY._ip +
      '^siteId=' +
      KY.getSiteId();
    var url = KY._ilog_img_url() + '?data=' + params;

    KY.sendDataFun.send(url);
  };

  static createMaEvent = function() {
    if (typeof window._kyEvt != 'object' || window._kyEvt.push == undefined) {
      return;
    }

    for (var i = 0; i < window._kyEvt.length; i++) {
      //清空事件队列
      var one = window._kyEvt[i];
      var l = one.length;
      if (one.join == undefined || l < 2) {
        return;
      }
      var define_event_type = '',
        define_event_value = '',
        define_event_description = '';
      if (l >= 1) {
        define_event_type = one[0];
      }
      if (l >= 2) {
        define_event_value = one[1];
      }
      if (l >= 3) {
        define_event_description = one[2];
      }
      KY.sendEvent({
        event_type: define_event_type,
        event_value: define_event_value,
        event_description: define_event_description,
      });
    }
    window._kyEvt = {
      //自定义事件，改写_kyEvt数组为对象，添加push方法，用户可以调用_kyEvt.push上报自定义数据
      push: function(args) {
        var define_event_type = '',
          define_event_value = '',
          define_event_description = '';
        if (!args.join) {
          return;
        }
        var l = args.length;
        if (l < 1) {
          KY.log('args len < 1');
          return;
        }
        if (l >= 1) {
          define_event_type = args[0];
        }
        if (l >= 2) {
          define_event_value = args[1];
        }
        if (l >= 3) {
          define_event_description = args[2];
        }
        KY.sendEvent({
          event_type: define_event_type,
          event_value: define_event_value,
          event_description: define_event_description,
        });
      },
    };
  };

  static getBaseParams = function() {
    if (KY._debug) {
      console.log(
        'browser=' +
          encodeURIComponent(_getBrowser()) +
          '^os=' +
          encodeURIComponent(_getSystem()) +
          '^url=' +
          encodeURIComponent(_getUrl()) +
          '^referUrl=' +
          encodeURIComponent(KY._getReferUrl()) +
          '^cookie=' +
          encodeURIComponent(_getCookie()) +
          '^resolution=' +
          encodeURIComponent(_getResolution()) +
          '^bizGroup=' +
          encodeURIComponent(KY._bg) +
          '^protocol=' +
          encodeURIComponent(_getProtocol()) +
          '^agent=' +
          encodeURIComponent(_getAgent()) +
          '^language=' +
          encodeURIComponent(_getLanguage()) +
          '^uid=' +
          _getUid() +
          '^visitid=' +
          encodeURIComponent(_getVisitid()) +
          '^visitTime=' +
          _getVisitTime() +
          '^jsv=' +
          KY._jsversion +
          '^extraInfo=' +
          encodeURIComponent(KY._getExtraInfo()),
      );
    }
    return (
      'browser=' +
      encodeURIComponent(_getBrowser()) +
      '^os=' +
      encodeURIComponent(_getSystem()) +
      '^url=' +
      encodeURIComponent(_getUrl()) +
      '^referUrl=' +
      encodeURIComponent(KY._getReferUrl()) +
      '^cookie=' +
      encodeURIComponent(_getCookie()) +
      '^resolution=' +
      encodeURIComponent(_getResolution()) +
      '^bizGroup=' +
      encodeURIComponent(KY._bg) +
      '^protocol=' +
      encodeURIComponent(_getProtocol()) +
      '^agent=' +
      encodeURIComponent(_getAgent()) +
      '^language=' +
      encodeURIComponent(_getLanguage()) +
      '^uid=' +
      _getUid() +
      '^visitid=' +
      encodeURIComponent(_getVisitid()) +
      '^visitTime=' +
      _getVisitTime() +
      '^jsv=' +
      KY._jsversion +
      '^extraInfo=' +
      encodeURIComponent(KY._getExtraInfo())
    );
  };

  static sendVisitLog = function() {
    var params =
      'msg_type=pageview' +
      '^browser=' +
      encodeURIComponent(_getBrowser()) +
      '^lang=' +
      encodeURIComponent(_getLanguage()) +
      '^os=' +
      encodeURIComponent(_getSystem()) +
      '^agent=' +
      encodeURIComponent(_getAgent()) +
      '^url=' +
      encodeURIComponent(_getUrl()) +
      '^cookie=' +
      encodeURIComponent(_getCookie()) +
      '^referUrl=' +
      encodeURIComponent(KY._getReferUrl()) +
      '^resolution=' +
      encodeURIComponent(_getResolution()) +
      '^uid=' +
      _getUid() +
      '^bizGroup=' +
      encodeURIComponent(KY._bg) +
      '^extraInfo=' +
      encodeURIComponent(KY._getExtraInfo()) +
      '^protocol=' +
      encodeURIComponent(_getProtocol()) +
      '^visitTime=' +
      _getVisitTime() +
      '^ip=' +
      KY._ip +
      '^jsv=' +
      KY._jsversion +
      '^siteId=' +
      KY.getSiteId() +
      '^visitid=' +
      encodeURIComponent(_getVisitid());

    KY.sendDataFun.send(KY._ilog_img_url() + '?data=' + params);
  };

  static get = function(url, fn, params) {
    url = url || '';
    if (url.indexOf('?') > -1) {
      if (url.split('?', 2)[1] == '') {
        url += 'jsv=' + KY._jsversion; //埋点版本号
      } else {
        url += '&jsv=' + KY._jsversion;
      }
    } else {
      url += '?jsv=' + KY._jsversion;
    }
    var newParams = '';
    for (var one in params) {
      newParams += '&' + one + '=' + encodeURIComponent(params[one]);
    }
    if (newParams) {
      url = url + newParams;
    }
    //url加上版本信息参数跟自定义参数（包括回调函数）
    var fnName = 'jsonpcb_' + new Date() * 1; //new Date() * 1当前时间戳
    window[fnName] = function(data) {
      fn(data);
    };
    url = url + '&fn=' + fnName;
    url = url + '&_=' + new Date() * 1;
    var script = document.createElement('script');
    script.src = url;
    var node = document.getElementsByTagName('script')[0];
    node.parentNode.insertBefore(script, node);
  };

  static setSiteId = function(siteId) {
    var domain = getDomain(true);
    var expireDate = new Date();
    expireDate.setFullYear(expireDate.getFullYear() + 10); //设置10年后过期
    // var newflag = '1';
    document.cookie =
      'siteId=' +
      siteId +
      '; domain=' +
      domain +
      '; path=/; expires=' +
      expireDate.toGMTString();
  };

  static getSiteId = function() {
    var nowCookie = document.cookie;
    var cookies = nowCookie.split(';');
    var siteId = '';
    // var newflag = '0';
    for (var i = 0; i < cookies.length; i++) {
      var oneCookie = cookies[i];
      var sp = oneCookie.split('=');
      if (trim(sp[0]) == 'siteId') {
        siteId = trim(sp[1]);
        break;
      }
    }
    if (siteId) return siteId;
    var vds = document.getElementById('ilogz');
    var siteObj = {};
    if (!vds) {
      return '';
    } else {
      vds.src
        .split('?')[1]
        .split('&')
        .forEach(function(item) {
          siteObj[item.split('=')[0]] = item.split('=')[1];
        });
      if (siteObj.id == undefined) {
        return '';
      } else {
        return siteObj.id;
      }
    }
  };

  static onHashchanged = function(e) {
    KY.referrer = e.oldURL;
    KY.sendVisitLog();
  };

  static mainFun = function() {
    if (self != top) {
      //如果当前窗口没有被嵌套iframe中，取消监听; (self:当前窗口，parent：父级窗口，top：根级窗户)
      KY.messageEventListener();
    }
    KY.getSiteId(); //获取siteId  document-->注:参数 siteId 代表站点 ID，每个网站都要申请唯一站点 ID，替换script标签src属性中的 SiteId，在 x-flow 的“站点管理”模块中可以查询到站点 ID。
    KY.getBaseParams(); //获取基础参数
    KY.bindIlogListener(); //绑定document事件，通过事件委托的方式无痕埋点
    KY.createMaEvent(); //自定义事件

    // TODO 结局单页 browserHistory不能上报问题
    (history => {
      const oldFn = history.pushState;
      history.pushState = (...args) => {
        if (typeof history.mystate === 'function') {
          history.mystate({ state: args[0] });
        }
        return oldFn.apply(history, args);
      };
    })(window.history);
    window.history.mystate = e => {
      KY._spaGo();
    };

    KY.sendVisitLog(); //访问上报基础参数
    if (window.addEventListener) {
      window.addEventListener('hashchange', KY.onHashchanged, false);
      window.addEventListener('popstate', KY._spaGo, false);
    } else {
      window.attachEvent('onhashchange', KY._spaGo);
    }
  };

  static init({ siteId, userId, _debug = false }) {
    KY._debug = _debug;
    if (siteId) KY.setSiteId(siteId);
    if (userId) {
      _setUid(userId);
    }

    if (KY.readyStateFlag) {
      return;
    }
    KY.get(
      KY._ip_Url(),
      function(data) {
        KY.readyStateFlag = true;
        //请求当前ip
        if (data.success) {
          KY._ip = data.ip;
        }
        KY.mainFun(); //主入口
      },
      {},
    );
  }
  readyStateFlag = flase;
}

export default KY;

(function(window) {

  var baseUrl = process.env.DEPLOY_ENV==='dev'?'http://*************:6060/node':'https://freight.sf-express.com/node'
  
  "use strict";
  var MA = {};
  MA._token = "__madeasmpage__";
  MA._circle_template_url = "";
  MA._ilog_img_url = baseUrl+"/img/bp.gif";
  MA._remote_circle_data_url = "";
  MA._ip = baseUrl+"/ip";
  MA._requestParam = {};
  MA._jsversion = 0;
  MA._debug = true;
  MA._targetDom = null;
  MA._referrer = "";
  MA._circleType = -1;
  MA._circleList = [];
  MA._graph = null;

  MA.utils = {}; //初始化埋点工具对象
  MA.utils.trim = function(val) {
    //添加去除空字符方法
    val = val || "";
    return val.replace(/[\f\n\r\t\v\ ]/g, "");
  };

  // ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓--工具类相关--↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
  /**
   * 添加类名方法
   */
  MA.utils.addClass = function(dom, addClassName) {
    if (!dom) {
      return;
    }
    var className = dom.getAttribute("class") || "";
    var classNames = className.split(" ");
    for (var i = 0; i < classNames.length; i++) {
      if (classNames[i] == addClassName) {
        return;
      }
    }
    className += " " + addClassName;
    dom.setAttribute("class", className);
  };
  /**
   * 删除类名方法
   */
  MA.utils.removeClass = function(dom, delclassName) {
    if (!dom) {
      return;
    }
    var className = dom.getAttribute("class") || "";
    if (className == delclassName) {
      return dom.setAttribute("class", "");
    }
    var classNames = className.split(" ");
    var newClassName = classNames.length > 1 ? classNames[0] : "";
    for (var i = 1; i < classNames.length; i++) {
      if (classNames[i] != delclassName) {
        newClassName += " " + classNames[i];
      }
    }
    dom.setAttribute("class", newClassName);
  };
  /**
   * 日志打印方法
   */
  MA.log = function() {
    if (MA._debug) {
      console.log("time:" + new Date(), arguments);
    }
  };
  /**
   * 判断是否IE浏览器
   */
  MA.utils.isIE = function() {
    return !!window.ActiveXObject || "ActiveXObject" in window;
  };
  /**
   * 用id获取dom元素
   */
  MA.$ = function(id) {
    return document.getElementById(id);
  };
  /**
   * 为请求的url加上自定义参数跟回调函数，用于跨域
   */
  MA.utils.get = function(url, fn, params) {
    url = url || "";
    if (url.indexOf("?") > -1) {
      if (url.split("?", 2)[1] == "") {
        url += "jsv=" + MA._jsversion; //埋点版本号
      } else {
        url += "&jsv=" + MA._jsversion;
      }
    } else {
      url += "?jsv=" + MA._jsversion;
    }
    var newParams = "";
    for (var one in params) {
      newParams += "&" + one + "=" + encodeURIComponent(params[one]);
    }
    if (newParams) {
      url = url + newParams;
    }
    //url加上版本信息参数跟自定义参数（包括回调函数）
    var fnName = "jsonpcb_" + new Date() * 1; //new Date() * 1当前时间戳
    window[fnName] = function(data) {
      fn(data);
    };
    url = url + "&fn=" + fnName;
    url = url + "&_=" + new Date() * 1;
    var script = document.createElement("script");
    script.src = url;
    var node = document.getElementsByTagName("script")[0];
    node.parentNode.insertBefore(script, node);
  };

  // ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑--工具类相关--↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑

  // ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓--Xpath相关--↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓

  /**
   * （重点）遍历Xpath，查找相应的dom节点
   */
  MA.utils.queryXpathDoms = function(xpath) {
    var findDom = null;
    var paths = xpath.split("/");
    var node = document;
    for (var i = 0; i < paths.length; i++) {
      //外循环遍历Xpath， 类似['html','body','div[2]','ul[1]','li[3]']
      var path = paths[i];
      if (path == "") {
        continue;
      }
      if (path == "html") {
        continue;
      }
      if (path == "body") {
        node = document.body;
        continue;
      }
      var tagName = path;
      var tagIndex = 1;
      if (path.indexOf("[")) {
        tagName = path.split("[")[0];
        tagIndex = parseInt(path.split("[")[1]);
      }
      var childNodes = node.childNodes;
      var tagCnt = 1;
      var flag = false;
      for (var j = 0; j < childNodes.length; j++) {
        //内循环从根节点往下遍历，找到与外循环数组对应的节点则将此节点设为新的根节点，然后递归
        var childNode = childNodes[j];
        if (childNode.tagName && childNode.tagName.toLowerCase() == tagName) {
          if (tagCnt == tagIndex) {
            flag = true;
            findDom = childNode;
            node = childNode;
            break;
          }
          tagCnt++;
        }
      }
      if (flag) {
        if (i == paths.length - 1) {
          break;
        }
        findDom = null;
        continue;
      } else {
        break;
      }
    }
    return findDom;
  };
  /**
   * 递归寻找父节点，返回/html/body/div[1]/ul[2]/li[3]形式的字符串,下标表示在同标签的兄弟节点中的索引
   */
  MA.utils.readXPath = function(element) {
    if (element == document.body) {
      return "/html/" + element.tagName.toLowerCase();
    }
    if (!element.parentNode) {
      //document本身
      return "/html";
    }
    var ix = 1,
      siblings = element.parentNode.childNodes;
    for (var i = 0, l = siblings.length; i < l; i++) {
      var sibling = siblings[i];
      if (sibling == element) {
        return (
          MA.utils.readXPath(element.parentNode) +
          "/" +
          element.tagName.toLowerCase() +
          "[" +
          ix +
          "]"
        );
      } else if (sibling.nodeType == 1 && sibling.tagName == element.tagName) {
        //nodeType=1为element
        ix++;
      }
    }
  };
  // ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑--Xpath相关--↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑

  // ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓--获取各种信息相关--↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓

  /**
   * 获取域名
   */
  MA.utils.getDomain = function(willCookieDomain) {
    var domain = location.hostname;
    if (!willCookieDomain) {
      return domain;
    }
    var ds = domain.split(".");
    if (ds.length >= 3) {
      domain = "";
      for (var i = 1; i < ds.length; i++) {
        domain += "." + ds[i];
      }
    }
    return domain;
  };
  /**
   * 获取时间戳（秒）
   */
  MA._getVisitTime = function() {
    return parseInt((new Date() * 1) / 1000);
  };
  /**
   * 获取浏览器
   */
  MA._getBrowser = function() {
    var explorer = window.navigator.userAgent,
      explorer;
    if (explorer.indexOf("MSIE") >= 0) {
      explorer = "ie";
    } else if (explorer.indexOf("Firefox") >= 0) {
      explorer = "Firefox";
    } else if (explorer.indexOf("Chrome") >= 0) {
      explorer = "Chrome";
    } else if (explorer.indexOf("Opera") >= 0) {
      explorer = "Opera";
    } else if (explorer.indexOf("Safari") >= 0) {
      explorer = "Safari";
    }
    return explorer;
  };
  /**
   * 获取操作系统
   */
  MA._getSystem = function() {
    var sUserAgent = navigator.userAgent;
    var isWin =
      navigator.platform == "Win32" || navigator.platform == "Windows";
    var isMac =
      navigator.platform == "Mac68K" ||
      navigator.platform == "MacPPC" ||
      navigator.platform == "Macintosh" ||
      navigator.platform == "MacIntel";
    if (isMac) return "Mac";
    var isUnix = navigator.platform == "X11" && !isWin && !isMac;
    if (isUnix) return "Unix";
    var isLinux = String(navigator.platform).indexOf("Linux") > -1;
    if (isLinux) return "Linux";
    if (isWin) {
      var isWin2K =
        sUserAgent.indexOf("Windows NT 5.0") > -1 ||
        sUserAgent.indexOf("Windows 2000") > -1;
      if (isWin2K) return "Win2000";
      var isWinXP =
        sUserAgent.indexOf("Windows NT 5.1") > -1 ||
        sUserAgent.indexOf("Windows XP") > -1;
      if (isWinXP) return "WinXP";
      var isWin2003 =
        sUserAgent.indexOf("Windows NT 5.2") > -1 ||
        sUserAgent.indexOf("Windows 2003") > -1;
      if (isWin2003) return "Win2003";
      var isWinVista =
        sUserAgent.indexOf("Windows NT 6.0") > -1 ||
        sUserAgent.indexOf("Windows Vista") > -1;
      if (isWinVista) return "WinVista";
      var isWin7 =
        sUserAgent.indexOf("Windows NT 6.1") > -1 ||
        sUserAgent.indexOf("Windows 7") > -1;
      if (isWin7) return "Win7";
    }
    return "other";
  };
  /**
   * 获取url
   */
  MA._getUrl = function() {
    return window.location.href;
  };
  /**
   * 获取来源地址
   */
  MA._getReferUrl = function() {
    if (document.referrer == "") {
      return MA._referrer;
    } else {
      return document.referrer;
    }
  };
  /**
   * 获取cookies
   */
  MA._getCookie = function() {
    return document.cookie;
  };
  /**
   * 获取分辨率
   */
  MA._getResolution = function() {
    return (window.screen.width || 0) + "x" + (window.screen.height || 0);
  };
  /**
   * document--->这个是 bizGroup.业务约定或公司名 放这儿.
   */
  MA._getBizGroup = function() {
    if (window._sfls && window._sfls.biz) {
      return window._sfls["biz"];
    }
    return "";
  };
  /**
   * 获取额外信息
   */
  MA._getExtraInfo = function() {
    var reStr = "";
    var extrObj = {};
    if (window._sfls && window._sfls.extraInfo) {
      extrObj = window._sfls["extraInfo"];
    }
    for (var k in extrObj) {
      var v = extrObj[k];
      if (!v) {
        v = "";
      }
      reStr += '"' + k + '"' + ':"' + v + '",';
    }
    return reStr;
  };
  /**
   * 获取协议
   */
  MA._getProtocol = function() {
    return document.location.protocol.split(":").join("");
  };
  MA._getAgent = function() {
    return navigator.userAgent;
  };
  /**
   * 获取用户语言
   */
  MA._getLanguage = function() {
    var type = navigator.appName,
      lang;
    if (type == "Netscape") {
      lang = navigator.language;
    } else {
      lang = navigator.userLanguage;
    }
    lang = lang.substr(0, 2);
    return lang;
  };
  /**
   * 从cookie中获取用户id，没有就创建一个uid
   */
  MA._getUid = function() {
    var _ilog_ma_trace_uid = "_i_m_t_uid";
    var nowCookie = document.cookie;
    var cookies = nowCookie.split(";");
    var visitid = "";
    var newflag = "0";
    for (var i = 0; i < cookies.length; i++) {
      var oneCookie = cookies[i];
      var sp = oneCookie.split("=");
      if (MA.utils.trim(sp[0]) == _ilog_ma_trace_uid) {
        visitid = MA.utils.trim(sp[1]);
        break;
      }
    }
    if ("" == visitid) {
      var domain = MA.utils.getDomain(true);
      var expireDate = new Date();
      expireDate.setFullYear(expireDate.getFullYear() + 10); //设置10年后过期
      newflag = "1";
      visitid =
        _ilog_ma_trace_uid +
        new Date() * 1 +
        "_" +
        Math.ceil(Math.random() * 1000);
      document.cookie =
        _ilog_ma_trace_uid +
        "=" +
        visitid +
        "; domain=" +
        domain +
        "; path=/; expires=" +
        expireDate.toGMTString();
    }
    return visitid + "^newflag=" + newflag;
  };
  /**
   * 获取访问id
   */
  MA._getVisitid = function() {
    var _ilog_ma_visit_id = "_i_m_v_id";
    var nowCookie = document.cookie;
    var cookies = nowCookie.split(";");
    var visitid = "";
    for (var i = 0; i < cookies.length; i++) {
      var oneCookie = cookies[i];
      var sp = oneCookie.split("=");
      if (MA.utils.trim(sp[0]) == _ilog_ma_visit_id) {
        visitid = MA.utils.trim(sp[1]);
        break;
      }
    }
    var domain = MA.utils.getDomain(true);
    var expireDate = new Date();
    expireDate.setMinutes(expireDate.getMinutes() + 30 * 60 * 1000); //设置30分钟过期
    if ("" == visitid) {
      visitid =
        _ilog_ma_visit_id +
        new Date() * 1 +
        "_" +
        Math.ceil(Math.random() * 1000);
    }
    document.cookie =
      _ilog_ma_visit_id +
      "=" +
      visitid +
      "; domain=" +
      domain +
      "; path=/; expires=" +
      expireDate.toGMTString();
    return visitid;
  };
  /**
   * 拼接基础参数
   */
  MA.getBaseParams = function() {
    return (
      "browser=" +
      encodeURIComponent(MA._getBrowser()) +
      "^os=" +
      encodeURIComponent(MA._getSystem()) +
      "^url=" +
      encodeURIComponent(MA._getUrl()) +
      "^referUrl=" +
      encodeURIComponent(MA._getReferUrl()) +
      "^cookie=" +
      encodeURIComponent(MA._getCookie()) +
      "^resolution=" +
      encodeURIComponent(MA._getResolution()) +
      "^bizGroup=" +
      encodeURIComponent(MA._getBizGroup()) +
      "^protocol=" +
      encodeURIComponent(MA._getProtocol()) +
      "^agent=" +
      encodeURIComponent(MA._getAgent()) +
      "^language=" +
      encodeURIComponent(MA._getLanguage()) +
      "^uid=" +
      MA._getUid() +
      "^visitid=" +
      encodeURIComponent(MA._getVisitid()) +
      "^visitTime=" +
      MA._getVisitTime() +
      "^jsv=" +
      MA._jsversion +
      "^extraInfo=" +
      encodeURIComponent(MA._getExtraInfo())
    );
  };

  // ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑--获取各种信息相关--↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑

  // ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓--父子页面交互相关--↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓

  /**
   * 向xflow父页面发送消息（没有嵌套在iframe的情况下，父页面就是自己）
   */
  MA.postMessage = function(actionType, data) {
    MA.log("内页面发送 ", actionType, data);
    var postData = {
      token: MA._token,
      action_type: actionType,
      data: data
    };
    window.parent.postMessage(postData, "*");
  };
  /**
   * 监听来自xflow父页面的postMessage事件
   */
  MA.messageEventListener = function() {
    window.addEventListener(
      "message",
      function(e) {
        if (e.source != window.parent) return;
        var data = e.data || {};
        var heatmapData = data.data._heatmapData;
        if (data.token != MA._token) {
          return;
        }

        if (
          data.action_type == "check_load_sdk" ||
          data.action_type == "check_load_sdk_heatmap"
        ) {
          //当父页面发放圈选或者热力图的action
          var checkSdk = data.action_type;
          MA._circle_template_url = data.data._circle_template_url;
          MA._remote_circle_data_url = data.data._remote_circle_data_url;
          MA._requestParam = data.data._params;
          MA.log("子页面接收到配置参数:", data.data);

          MA.postMessage("had_load_sdk", {}); //发送已load脚本的通知

          MA.utils.get(
            MA._circle_template_url,
            function(data) {
              //请求来自父页面的url，获取圈选模板css
              // data = {
              //   html:`<style>
              //     .madeasm-circle-hovered, .madeasm-circle-covered, .madeasm-circle-existed{
              //       outline:2px dashed #969696 !important;
              //       outline-offset:-2px !important;
              //     }
              //   </style>`
              // }
              data = data.html;

              var body = document.getElementsByTagName("BODY").item(0);

              body.innerHTML += data;
              MA.hockDomClick(checkSdk); //监听鼠标悬浮
              MA.queryExistCircle(checkSdk); //获取已圈选的元素
              if (!window._MA) {
                window._MA = {};
              }

              window._MA.cancel = MA.cancel;
              window._MA.save = MA.save;
              window._MA.del = MA.del;
              window._MA.showConfigDlg = MA.showConfigDlg;
              MA.removeIlogListener(); //因为是在iframe内，所以取消无痕埋点
              if (checkSdk == "check_load_sdk_heatmap") {
                // 如果是处于热力图页面，调用显示热力图的方法
                MA.heatmapCover(heatmapData);
                MA.resetMapp(window, heatmapData);
              }
            },
            MA._requestParam || {}
          );
          //加载echarts
          var vds = document.createElement("script");
          vds.type = "text/javascript";
          vds.async = true;
          vds.src = "//cdn.bootcss.com/echarts/4.3.0-rc.2/echarts.min.js";
          var s = document.getElementsByTagName("script")[0];
          s.parentNode.insertBefore(vds, s);
        } else if (data.action_type == "reload_existed_circle") {
          MA.queryExistCircle();
        } else if (data.action_type == "show_graph_data") {
          // 显示PV,UV折线图表
          var graph = document.getElementById("madeasm-circle-pvuvgraph-block"),
            rows =
              data.data && data.data.model && data.data.model.rows
                ? data.data.model.rows
                : [];
          if (rows.length == 0) {
            //post过来的data没有pvuv数据
            if (MA._graph) {
              MA._graph.dispose();
              MA._graph = null;
            }
            graph.innerHTML = "没有PVUV数据...";
            return;
          }
          var option = {
            tooltip: {
              trigger: "axis"
            },
            grid: {
              top: "10%",
              bottom: "10%",
              left: "10%",
              right: "10%"
            },
            xAxis: {
              type: "category",
              boundaryGap: false,
              data: []
            },
            yAxis: {
              type: "value",
              axisLabel: {
                formatter: "{value}"
              }
            },
            series: [
              {
                name: "pv值",
                type: "line",
                data: []
              },
              {
                name: "uv值",
                type: "line",
                data: []
              }
            ]
          };
          if (rows.length > 0) {
            var date = option.xAxis.data,
              pv = option.series[0].data,
              uv = option.series[1].data;
            rows.forEach(function(item, index, input) {
              date.push(item.bizdate);
              pv.push(item.pv);
              uv.push(item.uv);
            });
            if (MA._graph == null) {
              MA._graph = echarts.init(graph); //调用echars的初始化api
            }
            MA._graph.setOption(option); //调用echarts的参数api
            for (
              var i = 0,
                cvs = graph.getElementsByTagName("canvas"),
                len = cvs.length;
              i < len;
              i++
            ) {
              cvs[i].setAttribute("data-circle-flag", 0);
            }
            graph.style.display = "block";
          } else {
            graph.style.display = "block";
          }
        }
      },
      false
    );
  };

  // ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑--父子页面交互相关--↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑

  // ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓--热力图相关--↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓

  /**
   * 展示热力图
   */
  MA.heatmapCover = function(heatmapData) {
    var heatmapb = MA.$("madeasm-heatmapb");
    var heatmapc = MA.$("madeasm-heatmapc");
    heatmapb.style.display = "block";
    heatmapc.style.display = "block";
    heatmapb.style.height = document.body.scrollHeight + "px";
    if (document.body.scrollHeight <= 800) {
      heatmapb.style.height = "800px";
    }
    heatmapb.style.width = document.body.scrollWidth + "px";
    MA.setMapp(heatmapData);
  };
  /**
   * 设置热力图
   */
  MA.setMapp = function(heatmapData) {
    for (var i = 0; i < heatmapData.length; i++) {
      var xpathz = heatmapData[i].xpath;
      var xdom = MA.utils.queryXpathDoms(xpathz);
      if (xdom == null) {
        continue;
      }
      //兼容
      if (MA.hasFixed(xdom) == 1) {
        var xVal = MA.getOffsetTL(xdom).x + window.pageXOffset;
        var yVal = MA.getOffsetTL(xdom).y + window.pageYOffset;
        var xWidth = xdom.offsetWidth;
        var yHeight = xdom.offsetHeight;
      } else {
        var xVal = MA.getOffsetTL(xdom).x;
        var yVal = MA.getOffsetTL(xdom).y;
        var xWidth = xdom.offsetWidth;
        var yHeight = xdom.offsetHeight;
      }

      var buttonName = heatmapData[i].button_name;
      if (heatmapData[i].button_name == "") {
        buttonName = "此元素未设置名称";
      }
      var clickUsercnt = heatmapData[i].click_usercnt;
      var clickCnt = heatmapData[i].click_cnt;
      var clickRate = heatmapData[i].click_ration;
      MA.insertMapc(
        xVal,
        yVal,
        xWidth,
        yHeight,
        buttonName,
        clickUsercnt,
        clickCnt,
        clickRate
      );
    }
  };
  /**
   * 检查元素是否被用了fixed的父元素包含或者本身就用了fixed定位
   */
  MA.hasFixed = function(xdom) {
    while (xdom.tagName != "BODY") {
      if (!+[1]) {
        // ????  怀疑在某种低版本浏览器(ie)为true
        var xdomPos = xdom.currentStyle.position;
      } else {
        var xdomPos = window.getComputedStyle(xdom, null).position;
      }
      if (xdomPos == "fixed") {
        return 1;
      }
      xdom = xdom.parentNode;
    }
    return 0;
  };
  /**
   * 获取元素相对页面的x、y坐标
   */
  MA.getOffsetTL = function(e) {
    var x = e.offsetLeft;
    var y = e.offsetTop;
    while ((e = e.offsetParent)) {
      x += e.offsetLeft;
      y += e.offsetTop;
    }
    return {
      x: x,
      y: y
    };
  };
  /**
   * 重置热力图
   */
  MA.resetMapp = function(e, heatmapData) {
    if (e.addEventListener) {
      e.addEventListener(
        "scroll",
        function() {
          var heatmapc = MA.$("madeasm-heatmapc"); //热力图节点
          heatmapc.innerHTML = "";
          MA.setMapp(heatmapData);
          setTimeout(function() {
            heatmapc.innerHTML = "";
            MA.setMapp(heatmapData);
          }, 1000);
        },
        false
      );
    } else {
      e.attachEvent("onscroll", function() {
        heatmapc.innerHTML = "";
        MA.setMapp(heatmapData);
        setTimeout(function() {
          var heatmapc = MA.$("madeasm-heatmapc");
          heatmapc.innerHTML = "";
          MA.setMapp(heatmapData);
        }, 1000);
      });
    }
  };
  /**
   * 柯里化的形式兼容事件处理函数
   */
  var eventHandler = (function() {
    if (document.addEventListener) {
      return function(ele, event, fn) {
        ele.addEventListener(event, function() {
          fn.call(this);
        });
      };
    } else if (document.attachEvent) {
      return function(ele, event, fn) {
        ele.addEventListener("on" + event, function() {
          fn.call(this);
        });
      };
    } else {
      return function(ele, event, fn) {
        ele["on" + event] = function() {
          fn.call(this);
        };
      };
    }
  })();

  /**
   * 插入热力点
   * @param {buttonName} 按钮名称
   * @param {clickUsercnt} 点击人数
   * @param {clickCnt} 点击次数
   * @param {clickRate} 点击率
   */
  MA.insertMapc = function(
    xVal,
    yVal,
    xWidth,
    yHeight,
    buttonName,
    clickUsercnt,
    clickCnt,
    clickRate
  ) {
    var colorRate = clickRate.replace("%", "") / 100;
    var colorRageia = "#cbcbcb";
    var colorRageib = "#cbcbcb";
    var colorRageja = "#cbcbcb";
    var colorRagejb = "#cbcbcb";
    var colorRageka = "#cbcbcb";
    var colorRagekb = "#cbcbcb";
    if (colorRate >= 0 && colorRate < 0.166) {
      colorRageia = "rgba(0,14,223,1)";
      colorRageib = "rgba(0,14,223,0)";
      colorRageja = "rgba(0,14,223,1)";
      colorRagejb = "rgba(0,14,223,0)";
      colorRageka = "rgba(0,14,223,1)";
      colorRagekb = "rgba(0,14,223,0)";
    }
    if (colorRate >= 0.166 && colorRate < 0.332) {
      colorRageia = "rgba(0,14,223,1)";
      colorRageib = "rgba(0,14,223,0)";
      colorRageja = "rgba(0,14,223,1)";
      colorRagejb = "rgba(0,14,223,0)";
      colorRageka = "rgba(1,184,67,1)";
      colorRagekb = "rgba(1,184,67,0)";
    }
    if (colorRate >= 0.332 && colorRate < 0.498) {
      colorRageia = "rgba(0,14,223,1)";
      colorRageib = "rgba(0,14,223,0)";
      colorRageja = "rgba(1,184,67,1)";
      colorRagejb = "rgba(1,184,67,0)";
      colorRageka = "rgba(60,255,0,1)";
      colorRagekb = "rgba(60,255,0,0)";
    }
    if (colorRate >= 0.498 && colorRate < 0.664) {
      colorRageia = "rgba(1,184,67,1)";
      colorRageib = "rgba(1,184,67,0)";
      colorRageja = "rgba(60,255,0,1)";
      colorRagejb = "rgba(60,255,0,0)";
      colorRageka = "rgba(219,255,0,1)";
      colorRagekb = "rgba(219,255,0,0)";
    }
    if (colorRate >= 0.664 && colorRate < 0.83) {
      colorRageia = "rgba(60,255,0,1)";
      colorRageib = "rgba(60,255,0,0)";
      colorRageja = "rgba(219,255,0,1)";
      colorRagejb = "rgba(219,255,0,0)";
      colorRageka = "rgba(255,146,0,1)";
      colorRagekb = "rgba(255,146,0,0)";
    }
    if (colorRate >= 0.83 && colorRate <= 1) {
      colorRageia = "rgba(219,255,0,1)";
      colorRageib = "rgba(219,255,0,0)";
      colorRageja = "rgba(255,146,0,1)";
      colorRagejb = "rgba(255,146,0,0)";
      colorRageka = "rgba(208,2,27,1)";
      colorRagekb = "rgba(208,2,27,0)";
    }
    //某一个热力点用三个div显示（为了显示渐变色）
    var divi = document.createElement("div");
    var divj = document.createElement("div");
    var divk = document.createElement("div");
    var heatmapb = MA.$("madeasm-heatmapb");
    var heatmapc = MA.$("madeasm-heatmapc");
    divi.setAttribute(
      "style",
      "position:absolute;left:" +
        parseFloat(xVal) +
        "px;top:" +
        parseFloat(yVal) +
        "px;z-index:999991;width:" +
        parseFloat(xWidth) +
        "px;height:" +
        parseFloat(yHeight) +
        "px;background: radial-gradient(closest-side, " +
        colorRageia +
        ", " +
        colorRageib +
        ");cursor: pointer;opacity:0.5"
    );
    divj.setAttribute(
      "style",
      "position:absolute;left:" +
        (parseFloat(xWidth) * 0.1 + parseFloat(xVal)) +
        "px;top:" +
        (parseFloat(yHeight) * 0.1 + parseFloat(yVal)) +
        "px;z-index:999992;width:" +
        parseFloat(xWidth) * 0.8 +
        "px;height:" +
        parseFloat(yHeight) * 0.8 +
        "px;background: radial-gradient(closest-side ," +
        colorRageja +
        ", " +
        colorRagejb +
        ");cursor: pointer;opacity:0.6"
    );
    divk.setAttribute(
      "style",
      "position:absolute;left:" +
        (parseFloat(xWidth) * 0.2 + parseFloat(xVal)) +
        "px;top:" +
        (parseFloat(yHeight) * 0.2 + parseFloat(yVal)) +
        "px;z-index:999993;width:" +
        parseFloat(xWidth) * 0.6 +
        "px;height:" +
        parseFloat(yHeight) * 0.6 +
        "px;background: radial-gradient(closest-side ," +
        colorRageka +
        ", " +
        colorRagekb +
        ");cursor: pointer;opacity:1"
    );
    var divhLeft = parseFloat(xWidth) / 2 + parseFloat(xVal) + 5; //提示框的横坐标
    var divhTop = parseFloat(yHeight) / 2 + parseFloat(yVal) + 5; //提示框的纵坐标
    if (divhLeft + 207 > 1138) {
      divhLeft = parseFloat(xWidth) / 2 + parseFloat(xVal) - 5 - 207;
    }
    if (divhTop + 144 > document.body.scrollHeight) {
      divhTop = parseFloat(yHeight) / 2 + parseFloat(yVal) - 5 - 144;
    }
    var divh = MA.$("madeasm-hover");
    var divh = document.createElement("div"); //创建数据提示框
    divh.setAttribute(
      "style",
      "position:absolute;left:" +
        divhLeft +
        "px;top:" +
        divhTop +
        "px;z-index:999995;display:none;width:207px;height:144px;background: rgba(31,45,61,0.85);border-radius: 2px;"
    );
    var mh0 = document.createElement("div");
    var mh1 = document.createElement("div");
    var mh2 = document.createElement("div");
    var mh3 = document.createElement("div");
    var mh4 = document.createElement("div");
    mh0.setAttribute(
      "style",
      "position: absolute;left:20px;top:20px;width:5px;height:5px;background:#CE2E2C;border-radius:5px;"
    );
    mh1.setAttribute(
      "style",
      "width:120px;height:20px;margin:14px 0px 10px 36px;font-family:PingFangHK-Regular;font-size:14px;color:#FFFFFF;letter-spacing:0;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;"
    );
    mh2.setAttribute(
      "style",
      "width:120px;height:20px;margin:0px 0px 10px 36px;font-family:PingFangHK-Regular;font-size:14px;color:#FFFFFF;letter-spacing:0;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;"
    );
    mh3.setAttribute(
      "style",
      "width: 120px;height:20px;margin:0px 0px 10px 36px;font-family:PingFangHK-Regular;font-size:14px;color:#FFFFFF;letter-spacing:0;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;"
    );
    mh4.setAttribute(
      "style",
      "width: 120px;height:20px;margin:0px 0px 20px 36px;font-family:PingFangHK-Regular;font-size:14px;color:#FFFFFF;letter-spacing:0;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;"
    );
    divh.appendChild(mh0);
    divh.appendChild(mh1);
    divh.appendChild(mh2);
    divh.appendChild(mh3);
    divh.appendChild(mh4);
    mh1.innerHTML = buttonName;
    mh2.innerHTML = "点击人数：" + clickUsercnt;
    mh3.innerHTML = "点击次数：" + clickCnt;
    mh4.innerHTML = "点击率：" + clickRate;
    heatmapc.appendChild(divi);
    heatmapc.appendChild(divj);
    heatmapc.appendChild(divk);
    heatmapc.appendChild(divh);
    MA.mapcHoveron(divi, divh);
    MA.mapcHoverout(divi, divh);
    MA.mapcHoveron(divj, divh);
    MA.mapcHoverout(divj, divh);
    MA.mapcHoveron(divk, divh);
    MA.mapcHoverout(divk, divh);
    MA.mapcHoveron(divh, divh);
    MA.mapcHoverout(divh, divh);
  };

  /**
   * 鼠标移入divi，显示divh
   */
  MA.mapcHoveron = function(divi, divh) {
    if (divi.addEventListener) {
      divi.addEventListener(
        "mouseover",
        function() {
          divh.style.display = "block";
        },
        false
      );
    } else {
      divi.attachEvent("onmouseover", function() {
        divh.style.display = "block";
      });
    }
  };
  /**
   * 鼠标移出divi，隐藏divh
   */
  MA.mapcHoverout = function(divi, divh) {
    if (divi.addEventListener) {
      divi.addEventListener(
        "mouseout",
        function() {
          divh.style.display = "none";
        },
        false
      );
    } else {
      divi.attachEvent("onmouseout", function() {
        divh.style.display = "none";
      });
    }
  };

  // ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑--热力图相关--↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑

  // ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓--圈选功能相关--↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓

  MA.hockDom = {};
  /**
   * 在xflow中鼠标悬浮在元素上的红框效果
   */
  MA.hockDomClick = function(checkSdks) {
    if (checkSdks == "check_load_sdk_heatmap") {
      //如果是热力图命令则返回false，只处理圈选命令
      return false;
    }
    document.body.addEventListener(
      "mouseover",
      function(e) {
        e = e || window.event;
        var className = e.target.getAttribute("class") || "";
        if (
          className.indexOf("madeasm-circle-hovered") > -1 ||
          MA.$("madeasm-circle-dlg").style.display === "block"
        ) {
          //已悬浮在元素上方，返回false
          return;
        }
        if (e.target.getAttribute("data-circle-flag")) {
          //已悬浮在弹出对话框内，返回false
          return;
        }
        MA.utils.addClass(e.target, "madeasm-circle-hovered"); //加上悬浮样式（红框）

        /**
         * 鼠标离开元素，去掉悬浮样式
         */
        MA.hockDom.domMouseOutEvent = function(e1) {
          MA.utils.removeClass(e.target, "madeasm-circle-hovered");
          e.target.removeEventListener("mouseout", MA.hockDom.domMouseOutEvent);
        };
        e.target.addEventListener(
          "mouseout",
          MA.hockDom.domMouseOutEvent,
          false
        );

        /**
         * 点击悬浮元素，显示对话框
         */
        if (!MA.hockDom.domClickEvent) {
          MA.hockDom.domClickEvent = function(e2) {
            e2 = e2 || window.event;
            e2.preventDefault();
            if (
              MA.hockDom.eventTime &&
              new Date() * 1 - MA.hockDom.eventTime < 100
            ) {
              return;
            }
            MA.hockDom.eventTime = new Date() * 1;
            if (e2.target.getAttribute("data-circle-flag")) {
              //已打开对话框，返回
              return;
            }
            if (MA._targetDom) {
              //关闭已有对话框
              MA.utils.removeClass(MA._targetDom, "madeasm-circle-covered");
            }
            MA._targetDom = e2.target;
            MA.showConfigDlg(0);
          };
        }
        e.target.addEventListener("click", MA.hockDom.domClickEvent, false);
      },
      false
    );
  };
  /**
   * 显示配置对话框
   *
   *
   *
   */
  MA.showConfigDlg = function(type) {
    var showFn = function(domInfo) {
      var typeText = [
        function(text) {
          return "统计文本为 “" + text + "” 的元素。 文本不同将不统计。";
        },
        function() {
          return "统计当前位置的数据，例如广告位，出现的图片经常变化，此时统计出现在该广告位的所有图片的数据。";
        },
        function() {
          return "统计同类元素，例如商品列表页面中出现多个商品，此时统计出现在该列表中的所有商品。页面中绿色高亮显示。";
        }
      ][domInfo.type];
      var typeName = ["curdom", "curpostion", "likedom"][domInfo.type];
      MA._circleType = domInfo.type;
      MA.utils.addClass(MA._targetDom, "madeasm-circle-covered"); //添加选中样式

      var circleDlg = MA.$("madeasm-circle-dlg"); //显示对话框
      circleDlg.style.display = "block";

      /**
       *
       */
      var domTagName = (MA._targetDom.tagName || "").toUpperCase();
      var domtypeDom = MA.$("madeasm-circle-domtype");
      domtypeDom.innerHTML = "&lt;" + domTagName + "&gt;";
      var nameDom = MA.$("madeasm-circle-name");
      if (domTagName == "IMG") {
        var srcSplit = MA._targetDom.getAttribute("src").split("/");
        nameDom.value = domInfo.name || srcSplit[srcSplit.length - 1];
      } else {
        nameDom.value =
          domInfo.name ||
          MA._targetDom.innerHTML
            .replace(/<(.*?)>/g, "")
            .replace(/[\f\n\r\t\v\ ]/g, "");
      }
      if (domTagName == "A" || domTagName == "IMG") {
        MA.$("madeasm-circle-curpostion").style.display = "";
        MA.$("madeasm-circle-likedom").style.display = "none";
      } else {
        MA.$("madeasm-circle-curpostion").style.display = "none";
        MA.$("madeasm-circle-likedom").style.display = "";
      }
      var tdom = null;
      tdom = MA.$("madeasm-circle-curpostion");
      MA.utils.removeClass(tdom, "madeasm-circle-active");
      tdom = MA.$("madeasm-circle-likedom");
      MA.utils.removeClass(tdom, "madeasm-circle-active");
      tdom = MA.$("madeasm-circle-curdom");
      MA.utils.removeClass(tdom, "madeasm-circle-active");
      // tdom = MA.$("madeasm-circle-" + typeName);
      // MA.utils.addClass(tdom, "madeasm-circle-active");
      tdom = MA.$("madeasm-circle-curpostion-block");
      tdom.style.display = "none";
      tdom = MA.$("madeasm-circle-likedom-block");
      tdom.style.display = "none";
      tdom = MA.$("madeasm-circle-curdom-block");
      tdom.style.display = "none";
      // tdom = MA.$("madeasm-circle-" + typeName + "-block");
      // tdom.style.display = "block";
      if (
        MA._targetDom.getAttribute("class").indexOf("madeasm-circle-existed") >
        -1
      ) {
        //是否显示删除按钮
        MA.$("madeasm-circle-exist-delete").style.display = "block";
      } else {
        MA.$("madeasm-circle-exist-delete").style.display = "none";
      }
      tdom.innerHTML = typeText(nameDom.value);
      //处理手动埋点
      var dataIlog = MA._targetDom.getAttribute("data-ilog");
      var xpath = MA.utils.readXPath(MA._targetDom);
      MA.log("获取到XPATH,dataIlog: ", xpath, dataIlog, location.href);
      MA.postMessage("fetch_graph_data", {
        xpath: xpath,
        datailog: dataIlog,
        url: location.href,
        type: domInfo.type
      });
    };
    var domInfo = {
      name: null,
      xpath: null,
      type: type
    };
    /**
     * 如果是已经圈选并保存过的元素，获取之前保存的信息
     */
    if (
      MA._targetDom.getAttribute("class").indexOf("madeasm-circle-existed") > -1
    ) {
      var curDomXpath = MA.utils.readXPath(MA._targetDom);
      for (var i = 0; i < MA._circleList.length; i++) {
        var t = MA._circleList[i];
        if (t.xpath == curDomXpath) {
          domInfo = {
            name: t.name,
            xpath: t.xpath,
            type: t.circleType
          };
        }
      }
      showFn(domInfo);
    } else {
      showFn(domInfo);
    }
  };
  /**
   * 取消目标点
   */
  MA.cancel = function() {
    MA.utils.removeClass(MA._targetDom, "madeasm-circle-covered");
    document.getElementById("madeasm-circle-dlg").style.display = "none";
    MA._targetDom = null;
  };
  /**
   * 保存当前圈选信息
   */
  MA.save = function() {
    var name = MA.utils.trim(MA.$("madeasm-circle-name").value);
    var id = MA._targetDom.getAttribute("madeasm-circle-existed-id");
    id = id ? id : null;
    var circleType = MA._circleType;
    var xpath = MA.utils.readXPath(MA._targetDom);
    var url = MA._getUrl();
    var postData = {
      name: name,
      circleType: circleType,
      xpath: xpath,
      url: url,
      id: id
    };
    MA.log("save info : ", postData);
    MA.postMessage("save_circle", postData);
    MA._circleList.push({
      xpath: xpath,
      circleType: circleType,
      name: name,
      dom: MA._targetDom
    });
    MA.utils.addClass(MA._targetDom, "madeasm-circle-existed"); //保存后标识目标dom节点
    document.getElementById("madeasm-circle-dlg").style.display = "none";
  };
  /**
   * 删除圈选的节点
   */
  MA.del = function() {
    var xpath = MA.utils.readXPath(MA._targetDom);
    var id = MA._targetDom.getAttribute("madeasm-circle-existed-id");
    id = id ? id : null;
    if (!id) {
      return;
    }
    var url = MA._getUrl();
    var postData = {
      xpath: xpath,
      url: url,
      id: id
    };
    var tlist = [];
    for (var i = 0; i < MA._circleList.length; i++) {
      var one = MA._circleList[i];
      if (one.xpath != xpath) {
        tlist.push(one);
      }
    }
    MA._circleList = tlist;
    MA.log("delete info : ", postData);
    MA.postMessage("delete_circle", postData);
    MA.utils.removeClass(MA._targetDom, "madeasm-circle-covered");
    MA.utils.removeClass(MA._targetDom, "madeasm-circle-existed"); //去除选定样式
    MA._targetDom.setAttribute("madeasm-circle-existed-id", "");
    document.getElementById("madeasm-circle-dlg").style.display = "none";
    MA._targetDom = null;
  };
  /**
   * 获取已圈选的区域
   */
  MA.queryExistCircle = function(checkSdks) {
    if (checkSdks == "check_load_sdk_heatmap") {
      return false;
    }
    for (var i = 0; i < MA._circleList.length; i++) {
      MA.utils.removeClass(MA._circleList[i].dom, "madeasm-circle-existed");
    }
    /**
     * 通过传页面url请求当前页面的已圈选元素
     */
    MA.utils.get(
      MA._remote_circle_data_url + "?url=" + encodeURIComponent(MA._getUrl()),
      function(data) {
        //data相当于一个xpath数组
        MA.log("LIST_API : ", data);
        MA._circleList = [];
        for (var i = 0; i < data.length; i++) {
          var one = data[i];
          var tmpDom = MA.utils.queryXpathDoms(one.xpath);
          if (tmpDom) {
            tmpDom.setAttribute("madeasm-circle-existed-id", one.id);
            one.dom = tmpDom;
            MA._circleList.push(one);
            MA.utils.addClass(one.dom, "madeasm-circle-existed"); //添加圈选样式
          }
        }
      },
      MA._requestParam || {}
    );
  };

  // ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑--圈选功能相关--↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑

  // ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓--无痕埋点相关--↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓

  MA.scriptImages = {};
  MA.scriptImages.appends = [];
  MA.scriptImages.removeAll = function() {
    var len = MA.scriptImages.appends.length;
    for (var i = 0; i < len - 1; i++) {
      var tdom = MA.scriptImages.appends[i];
      if (tdom) {
        var pNode = tdom.parentNode;
        if (pNode) {
          pNode.removeChild(tdom);
        }
      }
    }
    MA.scriptImages.appends = [MA.scriptImages.appends[len - 1]];
  };
  MA.scriptImages.addImagesScript = function(tdom) {
    MA.scriptImages.appends.push(tdom);
  };
  MA.bindILog = {};
  /**
   * 监听点击事件
   */
  MA.bindILog.clickFun = function(e) {
    e = e || window.event;
    var target = e.srcElement ? e.srcElement : e.target;
    var tagName = target.tagName.toLowerCase();
    var xpath = MA.utils.readXPath(target);
    var scrollTop = 0,
      scrollLeft = 0;
    if (document.documentElement && document.documentElement.scrollTop) {
      scrollTop = document.documentElement.scrollTop;
      scrollLeft = document.documentElement.scrollLeft;
    } else if (document.body) {
      scrollTop = document.body.scrollTop;
      scrollLeft = document.body.scrollLeft;
    }
    var x = e.clientX + scrollLeft;
    var y = e.clientY + scrollTop;
    var xy = x + "-" + y;
    var text = "";
    var href = "";
    if (tagName == "img") {
      text = target.getAttribute("src");
    } else if (tagName == "a") {
      href = target.getAttribute("href") || "";
      text = MA.utils.trim(target.innerHTML.replace(/<(.*)?>/g, "")); //只取标签里面所有的文案，凡是有标签的地方都换成空格
    } else {
      text = MA.utils.trim(target.innerHTML.replace(/<(.*)?>/g, ""));
    }

    if (target.getAttribute("data-ilog")) {
      // var ilogData = target.dataset.ilog
      // somecode....
    }
    MA.log("绑定监听事件 clickFun", xpath, text, tagName, href, xy);
    MA.sendClick(xpath, text, tagName, href, xy);
  };
  /**
   * 添加事件监听，通过事件委托冒泡到document
   */
  MA.bindIlogListener = function() {
    if (document.addEventListener) {
      document.addEventListener("click", MA.bindILog.clickFun, false);
    } else {
      document.attachEvent("onclick", MA.bindILog.clickFun);
    }
  };
  /**
   * 取消事件监听
   */
  MA.removeIlogListener = function() {
    if (document.removeEventListener) {
      document.removeEventListener("click", MA.bindILog.clickFun, false);
    } else {
      document.detachEvent("onclick", MA.bindILog.clickFun);
    }
  };
  /**
   * 点击事件处理
   * @param {xpath} 被点击元素的xpath路径
   * @param {text} 被点击元素内的内容文案
   * @param {tagName} 被点击元素的标签名
   * @param {href} 被点击元素的跳转路径
   * @param {xy} 被点击元素的xy坐标
   */
  MA.sendClick = function(xpath, text, tagName, href, xy) {
    var params =
      "msg_type=circle_click" +
      "^xpath=" +
      encodeURIComponent(xpath) +
      "^text=" +
      encodeURIComponent(text).substr(0, 100) +
      "^href=" +
      encodeURIComponent(href) +
      "^xy=" +
      xy +
      "^tagName=" +
      tagName +
      "^" +
      MA.getBaseParams() +
      "^ip=" +
      MA._ip +
      "^siteId=" +
      MA.getIlogSiteId();
    var url = MA._ilog_img_url + "?data=" + params;

    MA.sendDataFun.send(url);
  };
  MA.sendDataFun = {};
  /**
   * 生成图片上报
   */
  MA.sendDataFun.send = function(url) {
    var logImage = new Image();
    logImage.src = url + "^_=" + new Date() * 1;
    logImage.style.visibility = "hidden";
    logImage.style.position = "absolute";
    logImage.style.left = 0;
    logImage.style.top = 0;
    logImage.style.zIndex = -1;
    var m = document.getElementsByTagName("script")[0];
    m.parentNode.insertBefore(logImage, m); //通过添加一个带特定src的图片来做数据上报
    MA.scriptImages.addImagesScript(logImage);
    MA.scriptImages.removeAll();
  };
  /**
   * 自定义事件上报方法
   */
  MA.sendEvent = function(obj) {
    var params =
      "msg_type=event_log" +
      "^" +
      MA.getBaseParams() +
      'event_type:"' +
      encodeURIComponent(obj.event_type) +
      '",' +
      'event_value:"' +
      encodeURIComponent(obj.event_value) +
      '",' +
      'event_description:"' +
      encodeURIComponent(obj.event_description) +
      '"' +
      "^ip=" +
      MA._ip +
      "^siteId=" +
      MA.getIlogSiteId();
    var url = MA._ilog_img_url + "?data=" + params;

    MA.sendDataFun.send(url);
  };
  /**
   * 创建自定义事件，document--->window._maEvt=[]; //开启自定义事件.一定要写这个. 这是一个开关.不然是默认不打开
   */
  MA.createMaEvent = function() {
    if (typeof window._maEvt != "object" || window._maEvt.push == undefined) {
      return;
    }

    for (var i = 0; i < window._maEvt.length; i++) {
      //清空事件队列
      var one = window._maEvt[i];
      var l = one.length;
      if (one.join == undefined || l < 2) {
        return;
      }
      var define_event_type = "",
        define_event_value = "",
        define_event_description = "";
      if (l >= 1) {
        define_event_type = one[0];
      }
      if (l >= 2) {
        define_event_value = one[1];
      }
      if (l >= 3) {
        define_event_description = one[2];
      }
      MA.sendEvent({
        event_type: define_event_type,
        event_value: define_event_value,
        event_description: define_event_description
      });
    }
    window._maEvt = {
      //自定义事件，改写_maEvt数组为对象，添加push方法，用户可以调用_maEvt.push上报自定义数据
      push: function(args) {
        var define_event_type = "",
          define_event_value = "",
          define_event_description = "";
        if (!args.join) {
          return;
        }
        var l = args.length;
        if (l < 1) {
          MA.log("args len < 1");
          return;
        }
        if (l >= 1) {
          define_event_type = args[0];
        }
        if (l >= 2) {
          define_event_value = args[1];
        }
        if (l >= 3) {
          define_event_description = args[2];
        }
        MA.sendEvent({
          event_type: define_event_type,
          event_value: define_event_value,
          event_description: define_event_description
        });
      }
    };
  };

  /**
   * 访问上报
   */
  MA.sendVisitLog = function() {
    var params =
      "msg_type=pageview" +
      "^browser=" +
      encodeURIComponent(MA._getBrowser()) +
      "^lang=" +
      encodeURIComponent(MA._getLanguage()) +
      "^os=" +
      encodeURIComponent(MA._getSystem()) +
      "^agent=" +
      encodeURIComponent(MA._getAgent()) +
      "^url=" +
      encodeURIComponent(MA._getUrl()) +
      "^cookie=" +
      encodeURIComponent(MA._getCookie()) +
      "^referUrl=" +
      encodeURIComponent(MA._getReferUrl()) +
      "^resolution=" +
      encodeURIComponent(MA._getResolution()) +
      "^uid=" +
      MA._getUid() +
      "^bizGroup=" +
      encodeURIComponent(MA._getBizGroup()) +
      "^extraInfo=" +
      encodeURIComponent(MA._getExtraInfo()) +
      "^protocol=" +
      encodeURIComponent(MA._getProtocol()) +
      "^visitTime=" +
      MA._getVisitTime() +
      "^ip=" +
      MA._ip +
      "^jsv=" +
      MA._jsversion +
      "^siteId=" +
      MA.getIlogSiteId() +
      "^visitid=" +
      encodeURIComponent(MA._getVisitid());

    MA.sendDataFun.send(MA._ilog_img_url + "?data=" + params);
  };
  /**
   * 获取siteId
   */
  MA.getIlogSiteId = function() {
    var ilogz = document.getElementById("ilogz");
    var siteObj = {};
    if (!ilogz) {
      return "";
    } else {
      ilogz.src
        .split("?")[1]
        .split("&")
        .forEach(function(item) {
          siteObj[item.split("=")[0]] = item.split("=")[1];
        });
      if (siteObj.id == undefined) {
        return "";
      } else {
        return siteObj.id;
      }
    }
  };
  /**
   * 监听hash路由变化
   */
  MA.myHashchanged = function(e) {
    MA._referrer = e.oldURL;
    MA.sendVisitLog();
  };

  // ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑--无痕埋点相关--↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑

  /**
   * 入口函数
   */
  MA.mainFun = function() {
    if (self != top) {
      //如果当前窗口没有被嵌套iframe中，取消监听; (self:当前窗口，parent：父级窗口，top：根级窗户)
      MA.messageEventListener();
    }
    MA.getIlogSiteId(); //获取siteId  document-->注:参数 siteId 代表站点 ID，每个网站都要申请唯一站点 ID，替换script标签src属性中的 SiteId，在 x-flow 的“站点管理”模块中可以查询到站点 ID。
    MA.getBaseParams(); //获取基础参数
    MA.bindIlogListener(); //绑定document事件，通过事件委托的方式无痕埋点
    MA.createMaEvent(); //自定义事件

    MA.sendVisitLog(); //访问上报基础参数
    if (window.addEventListener) {
      window.addEventListener("hashchange", MA.myHashchanged, false);
    } else {
      window.attachEvent("onhashchange", MA.myHashchanged);
    }
  };

  /**
   * 自执行函数，
   */
  MA.readyStateFlag = false;
  (function() {
    var readyToDoFun = function() {
      if (MA.readyStateFlag) {
        return;
      }
      MA.readyStateFlag = true;
      MA.utils.get(
        MA._ip,
        function(data) {
          //请求当前ip
          if (data.success) {
            MA._ip = data.ip;
          }
          MA.mainFun(); //主入口
        },
        {}
      );
    };

    //执行入口函数,然后取消监听页面加载函数
    var readyToDoFunAndRemoveLoaded;
    if (document.addEventListener) {
      readyToDoFunAndRemoveLoaded = function() {
        document.removeEventListener(
          "DOMContentLoaded",
          readyToDoFunAndRemoveLoaded,
          false
        );
        readyToDoFun();
      };
    } else if (document.attachEvent) {
      readyToDoFunAndRemoveLoaded = function() {
        if ("complete" === document.readyState) {
          document.detachEvent(
            "onreadystatechange",
            readyToDoFunAndRemoveLoaded
          );
          readyToDoFun();
        }
      };
    }
    var ieDomReadyFun = function() {
      if (MA.readyStateFlag) {
        return;
      }
      try {
        document.documentElement.doScroll("left");
      } catch (f) {
        setTimeout(ieDomReadyFun, 1);
        return;
      }
      readyToDoFun();
    };
    ieDomReadyFun();
    /**
     * 兼容性处理，当document.DOMContentLoaded或者window.onload执行的时候或者document.readyState=='complete'时都执行且只执行一次readyToDoFun函数
     */
    var domReadyFun = function() {
      if (MA.readyStateFlag) {
        return;
      }
      if ("complete" === document.readyState) {
        readyToDoFun();
      } else if (document.addEventListener) {
        document.addEventListener(
          "DOMContentLoaded",
          readyToDoFunAndRemoveLoaded,
          false
        );
        window.addEventListener("load", readyToDoFun, false);
      } else if (document.attachEvent) {
        document.attachEvent("onreadystatechange", readyToDoFunAndRemoveLoaded);
        window.attachEvent("onload", readyToDoFun);
        var isInFrameFlag = true;
        try {
          isInFrameFlag = window.frameElement == null; //window.frameElement如果页面被嵌入到iframe中，这个值不为null
        } catch (n) {}
        //document.documentElement.doScroll是为了兼容ie8以下的浏览器，详细看 https://blog.csdn.net/qq814374557/article/details/75330354
        document.documentElement.doScroll && isInFrameFlag && ieDomReadyFun();
      }
    };
    domReadyFun();
  })();
})(window || {});

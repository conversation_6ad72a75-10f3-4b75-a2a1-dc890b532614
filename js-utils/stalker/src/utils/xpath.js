/**
 * （重点）遍历Xpath，查找相应的dom节点
 */
export const queryXpathDoms = function(xpath) {
  var findDom = null;
  var paths = xpath.split('/');
  var node = document;
  for (var i = 0; i < paths.length; i++) {
    //外循环遍历Xpath， 类似['html','body','div[2]','ul[1]','li[3]']
    var path = paths[i];
    if (path == '') {
      continue;
    }
    if (path == 'html') {
      continue;
    }
    if (path == 'body') {
      node = document.body;
      continue;
    }
    var tagName = path;
    var tagIndex = 1;
    if (path.indexOf('[')) {
      tagName = path.split('[')[0];
      tagIndex = parseInt(path.split('[')[1]);
    }
    var childNodes = node.childNodes;
    var tagCnt = 1;
    var flag = false;
    for (var j = 0; j < childNodes.length; j++) {
      //内循环从根节点往下遍历，找到与外循环数组对应的节点则将此节点设为新的根节点，然后递归
      var childNode = childNodes[j];
      if (childNode.tagName && childNode.tagName.toLowerCase() == tagName) {
        if (tagCnt == tagIndex) {
          flag = true;
          findDom = childNode;
          node = childNode;
          break;
        }
        tagCnt++;
      }
    }
    if (flag) {
      if (i == paths.length - 1) {
        break;
      }
      findDom = null;
      continue;
    } else {
      break;
    }
  }
  return findDom;
};
/**
 * 递归寻找父节点，返回/html/body/div[1]/ul[2]/li[3]形式的字符串,下标表示在同标签的兄弟节点中的索引
 */
export const readXPath = function(element) {
  if (element == document.body) {
    return '/html/' + element.tagName.toLowerCase();
  }
  if (!element.parentNode) {
    //document本身
    return '/html';
  }
  var ix = 1,
    siblings = element.parentNode.childNodes;
  for (var i = 0, l = siblings.length; i < l; i++) {
    var sibling = siblings[i];
    if (sibling == element) {
      return (
        readXPath(element.parentNode) +
        '/' +
        element.tagName.toLowerCase() +
        '[' +
        ix +
        ']'
      );
    } else if (sibling.nodeType == 1 && sibling.tagName == element.tagName) {
      //nodeType=1为element
      ix++;
    }
  }
};

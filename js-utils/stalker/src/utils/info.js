import { trim } from './base';
export const getDomain = function(willCookieDomain) {
  var domain = location.hostname;
  if (!willCookieDomain) {
    return domain;
  }
  var ds = domain.split('.');
  if (ds.length >= 3) {
    domain = '';
    for (var i = 1; i < ds.length; i++) {
      domain += '.' + ds[i];
    }
  }
  return domain;
};
/**
 * 获取时间戳（秒）
 */
export const _getVisitTime = function() {
  return parseInt((new Date() * 1) / 1000);
};
/**
 * 获取浏览器
 */
export const _getBrowser = function() {
  var explorer = window.navigator.userAgent,
    explorer;
  if (explorer.indexOf('MSIE') >= 0) {
    explorer = 'ie';
  } else if (explorer.indexOf('Firefox') >= 0) {
    explorer = 'Firefox';
  } else if (explorer.indexOf('Chrome') >= 0) {
    explorer = 'Chrome';
  } else if (explorer.indexOf('Opera') >= 0) {
    explorer = 'Opera';
  } else if (explorer.indexOf('Safari') >= 0) {
    explorer = 'Safari';
  }
  return explorer;
};

/**
 * 获取操作系统
 */
export const _getSystem = function() {
  var sUserAgent = navigator.userAgent;
  var isWin = navigator.platform == 'Win32' || navigator.platform == 'Windows';
  var isMac =
    navigator.platform == 'Mac68K' ||
    navigator.platform == 'MacPPC' ||
    navigator.platform == 'Macintosh' ||
    navigator.platform == 'MacIntel';
  if (isMac) return 'Mac';
  var isUnix = navigator.platform == 'X11' && !isWin && !isMac;
  if (isUnix) return 'Unix';
  var isLinux = String(navigator.platform).indexOf('Linux') > -1;
  if (isLinux) return 'Linux';
  if (isWin) {
    var isWin2K =
      sUserAgent.indexOf('Windows NT 5.0') > -1 ||
      sUserAgent.indexOf('Windows 2000') > -1;
    if (isWin2K) return 'Win2000';
    var isWinXP =
      sUserAgent.indexOf('Windows NT 5.1') > -1 ||
      sUserAgent.indexOf('Windows XP') > -1;
    if (isWinXP) return 'WinXP';
    var isWin2003 =
      sUserAgent.indexOf('Windows NT 5.2') > -1 ||
      sUserAgent.indexOf('Windows 2003') > -1;
    if (isWin2003) return 'Win2003';
    var isWinVista =
      sUserAgent.indexOf('Windows NT 6.0') > -1 ||
      sUserAgent.indexOf('Windows Vista') > -1;
    if (isWinVista) return 'WinVista';
    var isWin7 =
      sUserAgent.indexOf('Windows NT 6.1') > -1 ||
      sUserAgent.indexOf('Windows 7') > -1;
    if (isWin7) return 'Win7';
  }
  return 'other';
};
/**
 * 获取url
 */
export const _getUrl = function() {
  return window.location.href;
};
/**
 * 获取cookies
 */
export const _getCookie = function() {
  return document.cookie;
};
/**
 * 获取分辨率`
 */
export const _getResolution = function() {
  return (window.screen.width || 0) + 'x' + (window.screen.height || 0);
};

/**
 * 获取协议
 */
export const _getProtocol = function() {
  return document.location.protocol.split(':').join('');
};
export const _getAgent = function() {
  return navigator.userAgent;
};
/**
 * 获取用户语言
 */
export const _getLanguage = function() {
  var type = navigator.appName,
    lang;
  if (type == 'Netscape') {
    lang = navigator.language;
  } else {
    lang = navigator.userLanguage;
  }
  lang = lang.substr(0, 2);
  return lang;
};

export const _setUid = function(uid) {
  var _ilog_ma_trace_uid = '_i_m_t_uid';
  var domain = getDomain(true);
  var expireDate = new Date();
  expireDate.setFullYear(expireDate.getFullYear() + 10); //设置10年后过期
  var newflag = '1';
  document.cookie =
    _ilog_ma_trace_uid +
    '=' +
    uid +
    '; domain=' +
    domain +
    '; path=/; expires=' +
    expireDate.toGMTString();
};
/**
 * 从cookie中获取用户id，没有就创建一个uid
 */
export const _getUid = function() {
  var _ilog_ma_trace_uid = '_i_m_t_uid';
  var nowCookie = document.cookie;
  var cookies = nowCookie.split(';');
  var visitid = '';
  var newflag = '0';
  for (var i = 0; i < cookies.length; i++) {
    var oneCookie = cookies[i];
    var sp = oneCookie.split('=');
    if (trim(sp[0]) == _ilog_ma_trace_uid) {
      visitid = trim(sp[1]);
      break;
    }
  }
  if ('' == visitid) {
    visitid =
      _ilog_ma_trace_uid +
      new Date() * 1 +
      '_' +
      Math.ceil(Math.random() * 1000);
    _setUid(visitid);
  }
  return visitid + '^newflag=' + newflag;
};

/**
 * 获取访问id
 */
export const _getVisitid = function() {
  var _ilog_ma_visit_id = '_i_m_v_id';
  var nowCookie = document.cookie;
  var cookies = nowCookie.split(';');
  var visitid = '';
  for (var i = 0; i < cookies.length; i++) {
    var oneCookie = cookies[i];
    var sp = oneCookie.split('=');
    if (trim(sp[0]) == _ilog_ma_visit_id) {
      visitid = trim(sp[1]);
      break;
    }
  }
  var domain = getDomain(true);
  var expireDate = new Date();
  expireDate.setMinutes(expireDate.getMinutes() + 30 * 60 * 1000); //设置30分钟过期
  if ('' == visitid) {
    visitid =
      _ilog_ma_visit_id +
      new Date() * 1 +
      '_' +
      Math.ceil(Math.random() * 1000);
  }
  document.cookie =
    _ilog_ma_visit_id +
    '=' +
    visitid +
    '; domain=' +
    domain +
    '; path=/; expires=' +
    expireDate.toGMTString();
  return visitid;
};

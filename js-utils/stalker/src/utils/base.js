export const addClass = function(dom, addClassName) {
  if (!dom) {
    return;
  }
  var className = dom.getAttribute('class') || '';
  var classNames = className.split(' ');
  for (var i = 0; i < classNames.length; i++) {
    if (classNames[i] == addClassName) {
      return;
    }
  }
  className += ' ' + addClassName;
  dom.setAttribute('class', className);
};

export const removeClass = function(dom, delclassName) {
  if (!dom) {
    return;
  }
  var className = dom.getAttribute('class') || '';
  if (className == delclassName) {
    return dom.setAttribute('class', '');
  }
  var classNames = className.split(' ');
  var newClassName = classNames.length > 1 ? classNames[0] : '';
  for (var i = 1; i < classNames.length; i++) {
    if (classNames[i] != delclassName) {
      newClassName += ' ' + classNames[i];
    }
  }
  dom.setAttribute('class', newClassName);
};

/**
 * 判断是否IE浏览器
 */
export const isIE = function() {
  return !!window.ActiveXObject || 'ActiveXObject' in window;
};

export const trim = function(val) {
  //添加去除空字符方法
  val = val || '';
  return val.replace(/[\f\n\r\t\v\ ]/g, '');
};

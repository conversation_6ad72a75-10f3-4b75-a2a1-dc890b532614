{"name": "@ky/stalker", "version": "1.6.3", "description": "无痕埋点数据上报WEB客户端", "scripts": {"build": "webpack", "test": "jest", "trypublish": "npm publish || true"}, "publishConfig": {"registry": "http://artifactory.sf-express.com/artifactory/api/npm/npm-sf-local/"}, "main": "build/stalker.js", "author": "<PERSON>", "license": "MIT", "keywords": [], "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.5", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/polyfill": "^7.4.4", "@babel/preset-env": "^7.4.5", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-preset-minify": "^0.5.0", "css-loader": "^3.0.0", "file-loader": "^4.0.0", "jest": "^24.8.0", "prettier": "^1.18.2", "prettier-webpack-plugin": "^1.2.0", "style-loader": "^1.0.0", "url-loader": "^2.0.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.4", "webpack-parallel-uglify-plugin": "^1.1.2"}, "dependencies": {"cross-env": "^6.0.3"}, "gitHead": "78366e16f33b9b71e27a360a7f909ca82426e254"}
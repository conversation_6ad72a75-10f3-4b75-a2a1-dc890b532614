// 判断当前客户端处于什么App
// 根据App家在必要资源，如果需要的话
// 返回一个异步加载完成后的新函数fdg

/* eslint-disable */
import whichApp from './whichApp';
import fdgBridge from './natives/fdg';
import fdgBridgeIos from './natives/fdg-ios';
import fsBridge from './natives/fs';
import fxgBridge from './natives/fxg';
import webBridge from './natives/web';

const app = whichApp();
console.log('app', app);

export default (function(appName) {
  switch (appName) {
    case 'fdg': {
      function fdgConstructor() {
        async function caller(method, data) {
          const native = await init();
          return native[method](data);
        }

        function init() {
          return fdgBridge;
        }
        return caller;
      }

      return fdgConstructor();
    }
    case 'fdgIos': {
      function fdgIosConstructor() {
        async function caller(method, data) {
          const native = await init();
          return native[method](data);
        }

        function init() {
          return fdgBridgeIos;
        }
        return caller;
      }

      return fdgIosConstructor();
    }
    case 'fs': {
      function fsConstructor() {
        async function caller(method, data) {
          const caller = await init();
          return caller(method, data);
        }

        function init() {
          return fsBridge;
        }
        return caller;
      }

      return fsConstructor();
    }
    case 'fxg': {
      function fxgConstructor() {
        async function caller(method, data) {
          const caller = await init();
          return caller(method, data);
        }

        function init() {
          return fxgBridge;
        }
        return caller;
      }
      return fxgConstructor();
    }
    default:
      function webConstructor() {
        async function caller(method, data) {
          const caller = await init();
          return caller(method, data);
        }

        function init() {
          return webBridge;
        }
        return caller;
      }
      return webConstructor();
  }
})(app);

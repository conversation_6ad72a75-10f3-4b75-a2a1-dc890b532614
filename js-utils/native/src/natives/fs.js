import loadScript from './loadScript'

function getFsNative() {
    return new Promise(async (resolve, reject) => {
        await loadScript('cordova.js')
        if (window.ExpressPlugin) {
            resolve(window.ExpressPlugin)
        } else {
            document.addEventListener(
                'deviceready',
                function() {
                    resolve(window.ExpressPlugin)
                },
                false
            )
        }
    })
}

const methodMapping = {
    getUserInfo: 'getUserWorkId',
    getLocation: 'getAmapLocation',
    close: 'backToWidget',
}

export default (function fxgConstrutor() {
    return function caller(method, data) {
        console.log('method', method)
        return new Promise(async (resolve, reject) => {
            console.log('getFsNative')
            const native = await getFsNative()
            console.log('native', native)

            console.log('method', method)
            native[methodMapping[method]](
                function(res) {
                    console.log('res', res)
                    resolve(res)
                },
                function(error) {
                    console.log('error', error)
                    reject(error)
                }
            )
        })
    }
})()

/* eslint-disable */

function JSBridge() {
  this.name = 'J<PERSON>ridge';
}

function setupWebViewJavascriptBridge(callback) {
  if (window.WebViewJavascriptBridge) {
    return callback(WebViewJavascriptBridge);
  }
  if (window.WVJBCallbacks) {
    return window.WVJBCallbacks.push(callback);
  }
  window.WVJBCallbacks = [callback];
  var WVJBIframe = document.createElement('iframe');
  WVJBIframe.style.display = 'none';
  WVJBIframe.src = 'https://__bridge_loaded__';
  document.documentElement.appendChild(WVJBIframe);
  setTimeout(function() {
    document.documentElement.removeChild(WVJBIframe);
  }, 0);
}

/**
 * @param null
 * JSBridge初始化
 */
/**
 * @param null
 * JSBridge 安卓初始化回调接口
 */

/**
 * @param data-> null
 * 获取用户信息   返回用户信息 token ，refreshToken 等等
 */

function callHandler(method, data = null) {
  return new Promise(resolve => {
    setupWebViewJavascriptBridge(function(bridge) {
      // debugger;
      console.log('bridge', bridge);
      bridge.callHandler(method, data, function responseCallback(responseData) {
        resolve(responseData);
      });
    });
  });
}

JSBridge.prototype.call = async function(method, data) {
  const res = await callHandler(method, data).catch(err => {
    console.log(err);
  });
  return res;
};

JSBridge.prototype.getUserInfo = async function(data) {
  const res = await this.call('fdg_getToken', data);
  return res;
};

JSBridge.prototype.getLocation = async function(data) {
  const res = await this.call('fdg_get_location', data);
  return res;
};

JSBridge.prototype.close = async function(data) {
  const res = await this.call('fdg_close_current_page', data);
  return res;
};

JSBridge.prototype.logout = async function(data) {
  const res = await this.call('logout', data);
  return res;
};

JSBridge.prototype.doNative = async function(data) {
  const res = await this.call('doNative', data);
  return res;
};

const fdgBridge = new JSBridge();

export default fdgBridge;

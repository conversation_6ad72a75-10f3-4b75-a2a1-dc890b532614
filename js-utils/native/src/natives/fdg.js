/* eslint-disable */

function J<PERSON>ridge() {
  this.name = 'J<PERSON>ridge';
  this.reset = true;
}

function setupWebViewJavascriptBridge(callback) {
  if (window.WebViewJavascriptBridge) {
    return callback(WebViewJavascriptBridge);
  }
  if (window.WVJBCallbacks) {
    return window.WVJBCallbacks.push(callback);
  }
  window.WVJBCallbacks = [callback];
  var WVJBIframe = document.createElement('iframe');
  WVJBIframe.style.display = 'none';
  WVJBIframe.src = 'https://__bridge_loaded__';
  document.documentElement.appendChild(WVJBIframe);
  setTimeout(function() {
    document.documentElement.removeChild(WVJBIframe);
  }, 0);
}

JSBridge.prototype.device = function() {
  var u = navigator.userAgent;
  var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1;
  // var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
  // return isAndroid;
  if (isAndroid) {
    return 'Android';
  } else {
    return 'iOS';
  }
};
/**
 * @param null
 * JSBridge初始化
 */
JSBridge.prototype._init = function() {
  return new Promise((resolve, reject) => {
    try {
      if (window.WebViewJavascriptBridge || window.FDGNaive) {
        window.FDGNaive = window.WebViewJavascriptBridge;
        console.log('WebViewJavascriptBridgeReady');
        resolve(window.WebViewJavascriptBridge); //eslint-disable-line
        // return callback(WebViewJavascriptBridge);
      } else {
        document.addEventListener(
          'WebViewJavascriptBridgeReady',
          function(WebViewJavascriptBridge) {
            console.log('WebViewJavascriptBridgeReady');
            window.FDGNaive = WebViewJavascriptBridge;
            resolve(WebViewJavascriptBridge);
            // return callback(WebViewJavascriptBridge);
          },
          false,
        );
      }
    } catch (error) {
      reject(error);
    }
  });
  // if (window.WVJBCallbacks) {
  //   return window.WVJBCallbacks.push(callback);
  // }
  // window.WVJBCallbacks = [callback];
  // var WVJBIframe = document.createElement('iframe');
  // WVJBIframe.style.display = 'none';
  // WVJBIframe.src = 'wvjbscheme://__BRIDGE_LOADED__';
  // document.documentElement.appendChild(WVJBIframe);
  // setTimeout(function() {
  //   document.documentElement.removeChild(WVJBIframe);
  // }, 0);
};
/**
 * @param null
 * JSBridge 安卓初始化回调接口
 */
JSBridge.prototype.__init__ = function(e) {
  console.log('__init__', e);
  const bridge = e['init'] ? e : e.bridge;
  // var isAn = this.device();
  if (this.reset) {
    this.reset = false;
    bridge.init(function(message, responseCallback) {
      console.log('JS got a message', message);
      var data = {
        'Javascript Responds': '测试中文!',
      };
      console.log('JS responding with', data);
      responseCallback(data);
    });
  }
};

/**
 * @param data-> null
 * 获取用户信息   返回用户信息 token ，refreshToken 等等
 */

function callHandler(bridge, method, data) {
  return new Promise(resolve => {
    // console.log('callHandler', bridge, bridge.bridge)

    bridge.callHandler(method, data, function(response) {
      console.log('response', response);
      if (typeof response === 'object') {
        resolve(response);
      } else {
        resolve(JSON.parse(response));
      }
    });
  });
}

function callHandlerIos(method, data = null) {
  return new Promise(resolve => {
    setupWebViewJavascriptBridge(function(bridge) {
      console.log('iosbridge', bridge);
      bridge.callHandler(method, data, function responseCallback(responseData) {
        resolve(responseData);
      });
    });
  });
}

JSBridge.prototype.call = async function(method, data) {
  if (this.device() === 'iOS') {
    const res = await callHandlerIos(method, data).catch(err => {
      console.log(err);
    });
    return res;
  } else {
    const bridge = await this._init();
    this.__init__(bridge);
    console.log('this.__init__');

    const res = await callHandler(
      bridge['init'] ? bridge : bridge.bridge,
      method,
      data,
    ).catch(err => {
      console.log(err);
    });
    console.log('res', res);
    return res;
  }
  // const bridge = await this._init();
  // this.__init__(bridge);
  // console.log('this.__init__');

  // const res = await callHandler(
  //   bridge['init'] ? bridge : bridge.bridge,
  //   method,
  //   data,
  // ).catch(err => {
  //   console.log(err);
  // });
  // console.log('res', res);
  // return res;
};

JSBridge.prototype.getUserInfo = async function(data) {
  const res = await this.call('fdg_getToken', data);
  return res;
};

JSBridge.prototype.getLocation = async function(data) {
  const res = await this.call('fdg_get_location', data);
  return res;
};

JSBridge.prototype.close = async function(data) {
  const res = await this.call('fdg_close_current_page', data);
  return res;
};

JSBridge.prototype.logout = async function(data) {
  //   debugger;
  const res = await this.call('logout', data);
  return res;
};

/**
 * 调用原生doNative方法
 * 参数：
 * {"methodName": "native_to_personal_center"}
 */
JSBridge.prototype.doNative = async function(data) {
  const res = await this.call('doNative', data);
  return res;
};

JSBridge.prototype.init = async function() {
  const res = await this._init();
  console.log('init', window.FDGNaive);
  return res;
};

const fdgBridge = new JSBridge();
fdgBridge.init();

export default fdgBridge;

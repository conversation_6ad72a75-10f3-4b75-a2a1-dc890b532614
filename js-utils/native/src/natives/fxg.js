import loadScript from './loadScript'

function getFsNative() {
    return window.nativeBridge
}

const methodMapping = {
    getUserInfo: 'sgsApp_getUserInfo',
    getLocation: 'sgsApp_getLocationInfo',
    close: 'finish',
}

export default (function fxgConstrutor() {
    return function caller(method, data) {
        console.log(method, method)
        return new Promise((resolve, reject) => {
            const native = getFsNative()
            console.log('native')
            const res = native[methodMapping[method]]()
            resolve(typeof res == 'object' ? res : JSON.parse(res))
        })
    }
})()

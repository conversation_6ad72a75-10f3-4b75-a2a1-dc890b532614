window.loadedScript = {}

export default function loadScript(url) {
    return new Promise((resolve, reject) => {
        if (window.loadedScript[url]) {
            resolve()
        }
        console.log('loadScript', loadScript)
        var scriptEl = document.createElement('script')
        scriptEl.src = url
        document.head.appendChild(scriptEl)
        scriptEl.onload = function() {
            window.loadedScript[url] = 1
            console.log('loadScriptEnd', loadScript)
            resolve()
        }
        scriptEl.onerror = function(err) {
            console.log('loadScriptError', loadScript)
            reject(err)
        }
    })
}

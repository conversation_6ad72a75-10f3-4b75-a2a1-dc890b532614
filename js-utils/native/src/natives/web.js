import getParams from '../getParams';

const methods = {
  getUserInfo: async function getUserInfo() {
    const params = getParams();
    return {
      userName: params.param || params.userName || '',
      [`hg-zone-code`]: 'TKJ7551',
    };
  },
  getLocation: async function getLocation() {
    return {};
  },
  close: async function close() {
    window.close();
    return {};
  },
  logout: async function logout() {
    alert('调用Native注销方法');
    return {};
  },
  doNative: function(data) {
    console.log(data);
    alert('调用Native doNative方法', data);
    return {};
  },
};

export default (function fxgConstrutor() {
  return function caller(method, data) {
    console.log('method', method);
    return new Promise(async (resolve, reject) => {
      const res = await methods[method](data);
      resolve(res);
    });
  };
})();

export default function whichApp() {
  const { userAgent: ua } = window.navigator;
  // if (
  //   ua.match(/(freight_ca|freight_ssmp)/g) &&
  //   (ua.indexOf('Android') > -1 || ua.indexOf('Adr') > -1)
  // ) {
  //   return 'fdg';
  // }

  // if (
  //   ua.match(/(freight_ca|freight_ssmp)/g) &&
  //   !(ua.indexOf('Android') > -1 || ua.indexOf('Adr') > -1)
  // ) {
  //   return 'fdgIos';
  // }

  if (ua.match(/(freight_ca|freight_ssmp)/g)) {
    return 'fdg';
  }

  if (ua.match(/sgsweb/g)) {
    return 'fxg';
  }
  if (process.env.PLATFORM_ENV == 'fs') {
    return 'fs';
  }
  if (window.location.href.indexOf('systemCode=sasp') > -1) {
    return 'xb';
  }
  return 'web';
}

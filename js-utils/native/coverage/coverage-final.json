{"/home/<USER>/work/js-library-boilerplate-basic/js-library-boilerplate-basic/scripts/testMock.js": {"path": "/home/<USER>/work/js-library-boilerplate-basic/js-library-boilerplate-basic/scripts/testMock.js", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 20}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1}, "f": {}, "b": {}, "_coverageSchema": "43e27e138ebf9cfc5966b082cf9a028302ed4184", "hash": "3fd6f760637291854bc0aadebc97e980d7ec9e32"}, "/home/<USER>/work/js-library-boilerplate-basic/js-library-boilerplate-basic/src/index.js": {"path": "/home/<USER>/work/js-library-boilerplate-basic/js-library-boilerplate-basic/src/index.js", "statementMap": {"0": {"start": {"line": 5, "column": 4}, "end": {"line": 5, "column": 46}}, "1": {"start": {"line": 8, "column": 13}, "end": {"line": 10, "column": 3}}, "2": {"start": {"line": 9, "column": 4}, "end": {"line": 9, "column": 40}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": 3}}, "loc": {"start": {"line": 4, "column": 16}, "end": {"line": 6, "column": 3}}, "line": 4}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 14}}, "loc": {"start": {"line": 8, "column": 19}, "end": {"line": 10, "column": 3}}, "line": 8}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 0}, "f": {"0": 1, "1": 0}, "b": {}, "_coverageSchema": "43e27e138ebf9cfc5966b082cf9a028302ed4184", "hash": "efe771acd3ded1cd17d5c56c88b9e7a9ed82caf2"}}
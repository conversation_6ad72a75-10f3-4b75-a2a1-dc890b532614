{"name": "@ky/test", "version": "1.0.22", "lockfileVersion": 1, "requires": true, "dependencies": {"ansi-regex": {"version": "5.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "dev": true}, "ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "cliui": {"version": "8.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/cliui/-/cliui-8.0.1.tgz", "integrity": "sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=", "dev": true, "requires": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}}, "color-convert": {"version": "2.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/color-name/-/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "emoji-regex": {"version": "8.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "dev": true}, "escalade": {"version": "3.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/escalade/-/escalade-3.1.1.tgz", "integrity": "sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=", "dev": true}, "get-caller-file": {"version": "2.0.5", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=", "dev": true}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "dev": true}, "new-ky-lerna": {"version": "1.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/new-ky-lerna/-/new-ky-lerna-1.0.1.tgz", "integrity": "sha1-UGqN/UwgpKPLhGgcXQCiHlq5TmQ=", "dev": true, "requires": {"yargs": "^17.7.1"}}, "require-directory": {"version": "2.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I=", "dev": true}, "string-width": {"version": "4.2.3", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/string-width/-/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "dev": true, "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "strip-ansi": {"version": "6.0.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "requires": {"ansi-regex": "^5.0.1"}}, "wrap-ansi": {"version": "7.0.0", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "dev": true, "requires": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}}, "y18n": {"version": "5.0.8", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/y18n/-/y18n-5.0.8.tgz", "integrity": "sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=", "dev": true}, "yargs": {"version": "17.7.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/yargs/-/yargs-17.7.1.tgz", "integrity": "sha1-NKd2RSAdGo/FITrOeHwiDqu9CWc=", "dev": true, "requires": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}}, "yargs-parser": {"version": "21.1.1", "resolved": "https://artifactory.sf-express.com:443/artifactory/api/npm/npm/yargs-parser/-/yargs-parser-21.1.1.tgz", "integrity": "sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=", "dev": true}}}
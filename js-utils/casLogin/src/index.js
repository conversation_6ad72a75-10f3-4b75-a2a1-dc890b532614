/*
 * @Descripttion:
 * @version:
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-12-27 15:54:07
 * @LastEditors: dong
 * @LastEditTime: 2023-04-13 16:45:44
 */
/**
 * <AUTHOR> casLogin
 */
class CAS {
  constructor(options = {}) {
    const {
      scriptUrl = '/assets/kyutils/cas-login/V1.0.0/cas-login.min.js',
    } = options;
    this.casIns = new Promise(res => {
      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = scriptUrl;
      document.getElementsByTagName('head')[0].appendChild(script);
      script.onload = () => {
        res(new window.CAS(options));
      };
    });
  }

  async login() {
    const cas = await this.casIns;
    await cas.login();
  }

  /** 不退出cas用户登出接口，当用户的业务token到期后，不需要调用caslogout接口，改为调用这个接口 */
  timeoutLogout() {
    this.casIns.then(cas => {
      cas.timeoutLogout();
    });
  }

  logout() {
    this.casIns.then(cas => {
      cas.logout();
    });
  }

  setReqHeader(key, value) {
    this.casIns.then(cas => {
      cas.setReqHeader(key, value);
    });
  }
}
export default CAS;

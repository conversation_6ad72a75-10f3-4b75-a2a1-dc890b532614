/**
 * <AUTHOR> casLogin
 */
// import { Notification } from 'element-ui';

import axios from 'axios';

axios.defaults.headers.post['Content-Type'] = 'application/json';

axios.defaults.withcredentials = true;

axios.interceptors.response.use(
  res => {
    console.log(res, 'res');
    if (res) {
      if (!res.data.success && res.data.errorCode) {
        return Promise.reject(res.data.errorCode);
      }
      return res.data.obj;
    }
  },
  error => {
    if (error.response) {
      if (
        /^(09020102|09020101|09020103|09020107)$/.test(error.response.errorCode)
      ) {
        sessionStorage.removeItem('userInfo');
      }
    }
    return Promise.reject(error);
  },
);

function setAxiosHeader(key, value) {
  axios.defaults.headers[key] = value;
}

// 从URL中获取ticket
function getQueryString(name) {
  const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`);
  const r = window.location.search.substr(1).match(reg);
  if (r != null) return r[2];
  return '';
}

function getDeviceId() {
  const s = [];
  const hexDigits = 'abcdef';
  for (let i = 0; i < 10; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 6), 1);
  }
  const uuid = s.join('') + new Date().getTime();
  return uuid;
}

function funcUrlDel(name) {
  let url = '';
  const { origin, pathname, search } = window.location;
  const baseUrl = `${origin}${pathname}?`;
  const query = search.substr(1);
  if (query.indexOf(name) > -1) {
    const obj = {};
    const arr = query.split('&');
    for (let i = 0; i < arr.length; i++) {
      arr[i] = arr[i].split('=');

      obj[arr[i][0]] = arr[i][1]; // eslint-disable-line
    }
    delete obj[name];
    if (sessionStorage['url_search']) {
      const search = JSON.parse(sessionStorage['url_search']);
      Object.assign(obj, search);
    }
    url =
      baseUrl +
      JSON.stringify(obj)
        .replace(/[\"\{\}]/g, '') // eslint-disable-line
        .replace(/\:/g, '=') // eslint-disable-line
        .replace(/\,/g, '&'); // eslint-disable-line
  }
  return url;
}

function saveExtraQuery() {
  if (sessionStorage['url_search']) {
    return;
  }
  const { search } = window.location;
  const query = search.substr(1);
  if (query.length) {
    const arr = query.split('&');
    const baseKeyArr = ['language', 'country', 'variant', 'ticket'];
    const obj = {};
    arr.forEach(item => {
      const temp = item.split('=');
      if (!baseKeyArr.includes(temp[0])) {
        obj[temp[0]] = temp[1];
      }
    });
    sessionStorage['url_search'] = JSON.stringify(obj);
  }
}

// const isOutside = (() => /wfop/gi.test(window.location.host))();

let triggerTime;
const throttle = func => {
  if (triggerTime) {
    clearTimeout(triggerTime);
  }
  triggerTime = setTimeout(() => {
    func();
  }, 500);
};
const EVENTS_ARRAY = [
  'keypress',
  'keydown',
  'click',
  'contextmenu',
  'dblclick',
  'mousemove',
  'scroll',
  'touchmove',
  'touchstart',
];
// const { protocol } = window.location;
const protocol = 'https:';
class CASLOGIN {
  constructor(options = {}) {
    const {
      url = '',
      appKey = 'FOP-KACS-CORE',
      appSecret = 'FOP-KACS-CORE',
      ENV = 'SIT',
      callback = null,
      casloginURL = `${protocol}//cas.sit.sf-express.com/cas/login`,
      caslogoutURL = `${protocol}//cas.sit.sf-express.com/cas/logout`,
      timeoutcaslogoutURL = `${protocol}//cas.sit.sf-express.com/cas/onservicesessiontimeout`,
      loginTimeout = null,
    } = options;
    Object.assign(this, {
      url,
      appKey,
      appSecret,
      ENV,
      callback,
      casloginURL,
      caslogoutURL,
      timeoutcaslogoutURL,
      loginTimeout,
    });
  }

  // 设置登录URL
  casURL() {
    if (this.ENV === 'SIT') {
      this.casloginURL = `${protocol}//cas.sit.sf-express.com/cas/login`;
      this.caslogoutURL = `${protocol}//cas.sit.sf-express.com/cas/logout`;
      this.timeoutcaslogoutURL = `${protocol}//cas.sit.sf-express.com/cas/onservicesessiontimeout`;
    } else if (this.ENV === 'PRD') {
      this.casloginURL = `${protocol}//cas.sf-express.com/cas/login`;
      this.caslogoutURL = `${protocol}//cas.sf-express.com/cas/logout`;
      this.timeoutcaslogoutURL = `${protocol}//cas.sf-express.com/cas/onservicesessiontimeout`;
    } else {
      // Notification.error({
      //   title: '异常',
      //   message: 'ENV环境参数值只能为SIT或PRD!',
      // });
    }
  }

  // 跳转登录页面
  toLoginUrl() {
    const url = sessionStorage.url || window.location.href;
    window.location.href = `${this.casloginURL}?service=${url}`;
  }

  isTicket() {
    return new Promise((resolve, reject) => {
      if (!sessionStorage.ticket) {
        this.toLoginUrl();
      } else {
        if (!localStorage.deviceId) {
          localStorage.deviceId = getDeviceId();
        }
        const ticketUrl = `${this.url}/apis-auth/login/cas`;
        return axios({
          url: ticketUrl,
          method: 'POST',
          data: {
            ticket: sessionStorage.ticket,
            service: sessionStorage.url,
            appKey: this.appKey,
            appSecret: this.appSecret,
            deviceId: localStorage.deviceId,
          },
        })
          .then(res => {
            resolve(res);
          })
          .catch(err => {
            reject(err);
          });
      }
    });
  }

  checkToken() {
    return new Promise((resolve, reject) => {
      const checkTokenUrl = `${this.url}/apis-auth/login/check_token`;
      return axios({
        url: checkTokenUrl,
        method: 'GET',
      })
        .then(res => {
          resolve(res);
        })
        .catch(err => {
          reject(err);
        });
    });
  }

  casLogout() {
    this.logout();
  }

  async logout() {
    document.cookie = 'lastOpTime=;path=/;'; // 退出清空lastOpTime cookie
    const logoutUrl = `${this.url}/apis-auth/login/logout`;
    try {
      await axios({
        url: logoutUrl,
        method: 'GET',
        timeout: 3000,
      });
    } catch (error) {
      console.log(error);
    }
    const url = sessionStorage.url || window.location.href;
    sessionStorage.clear();
    window.location.href = `${this.caslogoutURL}?service=${url}`;
  }

  /** 不退出cas用户登出接口，当用户的业务token到期后，不需要调用caslogout接口，改为调用这个接口 */
  timeoutLogout() {
    const url = sessionStorage.url || window.location.href;
    sessionStorage.clear();
    window.location.href = `${this.timeoutcaslogoutURL}?service=${url}`;
  }

  casLogin() {
    this.login();
  }

  async login() {
    this.casURL();
    const [url] = window.location.href.split('?');
    // 获取查询query
    saveExtraQuery();
    sessionStorage.url = url; // 保存URL
    sessionStorage.ticket = getQueryString('ticket'); // 获取票据并保存

    try {
      const res = await this.checkToken();
      const { userId, userName } = res;
      document.cookie = `lastOpTime=${new Date().getTime()};path=/;`;
      sessionStorage.userid = userId;
      sessionStorage.username = userName;
      if (typeof this.callback === 'function' && this.callback) {
        this.callback();
      }
    } catch (err) {
      console.log(err);
      if (['09020102', '09020101'].includes(err)) {
        try {
          const res = await this.isTicket();
          const { userId, userName } = res;
          document.cookie = `lastOpTime=${new Date().getTime()};path=/;`;
          window.history.replaceState(null, null, funcUrlDel('ticket'));
          sessionStorage.userid = userId;
          sessionStorage.username = userName;
        } catch (errCode) {
          if (
            ['09020102', '09020101', '09020103', '09020107'].includes(errCode)
          ) {
            sessionStorage.removeItem('ticket');
            return this.logout();
          }
        }
      }
    }
    return this.initConf();
  }

  // 初始化cas配置
  initConf = () =>
    new Promise((res, reject) => {
      const handleEvent = () => {
        throttle(() => {
          const isOut = this.isOutOfDate();
          if (isOut) {
            this.logout();
            handleListener('removeEventListener');
            reject();
          } else {
            document.cookie = `lastOpTime=${new Date().getTime()};path=/;`;
          }
        });
      };
      const handleListener = handle => {
        EVENTS_ARRAY.forEach(event => {
          document.documentElement[handle](event, handleEvent, false);
        });
      };
      if (this.loginTimeout) {
        handleListener('addEventListener');
        res();
      } else {
        const confScript = document.createElement('script');
        confScript.onload = () => {
          const isOut = this.isOutOfDate();
          if (isOut) {
            this.logout();
            reject();
          } else {
            handleListener('addEventListener');
            res();
          }
        };
        confScript.onerror = () => {
          handleListener('addEventListener');
          res(); // 这里还是要resolve,加载错误会有默认值
        };
        confScript.src = `/casconf.js?timestamp=${new Date().getTime()}`;
        document.body.appendChild(confScript);
      }
    });

  isOutOfDate() {
    // 优先使用项目中配置
    const timeOut = this.loginTimeout || window.loginTimeout || 30 * 60;
    if (timeOut) {
      const cookies = document.cookie;
      const now = new Date().getTime();
      const lastOpTime = cookies.match(/lastOpTime=([0-9]+)/);
      if (lastOpTime && lastOpTime[1] && now - lastOpTime[1] < timeOut * 1000) {
        return false;
      }
      return true;
    }
    return false;
  }

  setReqHeader(key, value) {
    setAxiosHeader(key, value);
  }
}
export default CASLOGIN;

/*
 * @Descripttion:
 * @version:
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-12-27 13:50:54
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-01-04 11:06:33
 */
const path = require('path');
const webpack = require('webpack');
const PrettierPlugin = require('prettier-webpack-plugin');
const ParallelUglifyPlugin = require('webpack-parallel-uglify-plugin');

module.exports = {
  mode: 'production',
  // entry: './src/index.js',
  entry: {
    index: './src/index.js',
    'cas-login.min': './src/cas.js',
  },
  output: {
    // filename: 'index.js',
    filename: '[name].js',
    path: path.resolve(__dirname, 'build'),
    // library: 'CasLogin',
    library: 'CAS',
    libraryTarget: 'umd',
    libraryExport: 'default',
  },
  module: {
    rules: [
      {
        test: /\.m?js$/,
        exclude: /(node_modules|bower_components)/,
        use: ['babel-loader'],
      },
    ],
  },
  plugins: [
    // new PrettierPlugin(),
    new webpack.DefinePlugin({
      'process.env.DEPLOY_ENV': JSON.stringify(process.env.DEPLOY_ENV),
    }),
    // new webpack.BannerPlugin(banner),
    new ParallelUglifyPlugin({
      cacheDir: '.cache/',
      uglifyJS: {
        output: {
          comments: false,
        },
        compress: {},
        warnings: false,
      },
    }),
  ],
};

{"name": "@ky/caslogin", "version": "3.2.8", "lockfileVersion": 1, "requires": true, "dependencies": {"@babel/code-frame": {"version": "7.12.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/code-frame/download/@babel/code-frame-7.12.11.tgz", "integrity": "sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8=", "dev": true, "requires": {"@babel/highlight": "^7.10.4"}}, "@babel/compat-data": {"version": "7.15.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/compat-data/-/compat-data-7.15.0.tgz", "integrity": "sha1-Lbr4uFM0eWyvuw9Xk6kKL8AQsXY=", "dev": true}, "@babel/core": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/core/-/core-7.15.8.tgz", "integrity": "sha1-GVufK//pldLGwVnnL+UltBFOjBA=", "dev": true, "requires": {"@babel/code-frame": "^7.15.8", "@babel/generator": "^7.15.8", "@babel/helper-compilation-targets": "^7.15.4", "@babel/helper-module-transforms": "^7.15.8", "@babel/helpers": "^7.15.4", "@babel/parser": "^7.15.8", "@babel/template": "^7.15.4", "@babel/traverse": "^7.15.4", "@babel/types": "^7.15.6", "convert-source-map": "^1.7.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.1.2", "semver": "^6.3.0", "source-map": "^0.5.0"}, "dependencies": {"@babel/code-frame": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/code-frame/-/code-frame-7.15.8.tgz", "integrity": "sha1-RZkMR62tsAwDZ3uqiSIffMI9JQM=", "dev": true, "requires": {"@babel/highlight": "^7.14.5"}}, "@babel/generator": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/generator/-/generator-7.15.8.tgz", "integrity": "sha1-+la+a1lpUs6yMQSM+E7kmaGcDNE=", "dev": true, "requires": {"@babel/types": "^7.15.6", "jsesc": "^2.5.1", "source-map": "^0.5.0"}}, "@babel/helper-function-name": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-function-name/-/helper-function-name-7.15.4.tgz", "integrity": "sha1-hFdE2vxDgaSl+2r6bD02+Yp4frw=", "dev": true, "requires": {"@babel/helper-get-function-arity": "^7.15.4", "@babel/template": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/helper-get-function-arity": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-get-function-arity/-/helper-get-function-arity-7.15.4.tgz", "integrity": "sha1-CYgYk0oTf854tTaj4BWGS+Hih5s=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-split-export-declaration": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.15.4.tgz", "integrity": "sha1-rsq5Lc2+9qEKo7YqsgSwhfd24lc=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/highlight": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/highlight/-/highlight-7.14.5.tgz", "integrity": "sha1-aGGlLwOWZAUAH2qlNKAaJNmejNk=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.5", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/parser/-/parser-7.15.8.tgz", "integrity": "sha1-e6zcvnG9w/+TbVEMFdzqfPC5kBY=", "dev": true}, "@babel/template": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/template/-/template-7.15.4.tgz", "integrity": "sha1-UYmNNdzz+qZwxO5q/P1RfuE58ZQ=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/traverse": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/traverse/-/traverse-7.15.4.tgz", "integrity": "sha1-/4UQNnoUS/v/VS2eGOKPPiiJwi0=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/generator": "^7.15.4", "@babel/helper-function-name": "^7.15.4", "@babel/helper-hoist-variables": "^7.15.4", "@babel/helper-split-export-declaration": "^7.15.4", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4", "debug": "^4.1.0", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.15.6.tgz", "integrity": "sha1-mavcSCGLKIHAWN0KerBbmcm+dY8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.9", "to-fast-properties": "^2.0.0"}}, "debug": {"version": "4.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/-/debug-4.3.2.tgz", "integrity": "sha1-8KScGKyHeeMdSgxgKd+3aHPHQos=", "dev": true, "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "dev": true}, "semver": {"version": "6.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "dev": true}}}, "@babel/generator": {"version": "7.12.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/generator/download/@babel/generator-7.12.11.tgz", "integrity": "sha1-mKffe4w1jJo3qweiQFaFMBaro68=", "dev": true, "requires": {"@babel/types": "^7.12.11", "jsesc": "^2.5.1", "source-map": "^0.5.0"}}, "@babel/helper-annotate-as-pure": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.15.4.tgz", "integrity": "sha1-PQ5DsAxeSf22xX5CFgGnpljV+DU=", "dev": true, "requires": {"@babel/types": "^7.15.4"}, "dependencies": {"@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/types": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.15.6.tgz", "integrity": "sha1-mavcSCGLKIHAWN0KerBbmcm+dY8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.9", "to-fast-properties": "^2.0.0"}}}}, "@babel/helper-builder-binary-assignment-operator-visitor": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.15.4.tgz", "integrity": "sha1-Ia2BX2CbhO4OMFhnbDPPbRZwUl8=", "dev": true, "requires": {"@babel/helper-explode-assignable-expression": "^7.15.4", "@babel/types": "^7.15.4"}, "dependencies": {"@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/types": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.15.6.tgz", "integrity": "sha1-mavcSCGLKIHAWN0KerBbmcm+dY8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.9", "to-fast-properties": "^2.0.0"}}}}, "@babel/helper-compilation-targets": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-compilation-targets/-/helper-compilation-targets-7.15.4.tgz", "integrity": "sha1-z22U8w++/BORI+J91rAvZa7tt7k=", "dev": true, "requires": {"@babel/compat-data": "^7.15.0", "@babel/helper-validator-option": "^7.14.5", "browserslist": "^4.16.6", "semver": "^6.3.0"}, "dependencies": {"semver": {"version": "6.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "dev": true}}}, "@babel/helper-create-class-features-plugin": {"version": "7.12.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.12.1.tgz", "integrity": "sha1-PEWZj0Me3UqSFMXx060USKYTf24=", "dev": true, "requires": {"@babel/helper-function-name": "^7.10.4", "@babel/helper-member-expression-to-functions": "^7.12.1", "@babel/helper-optimise-call-expression": "^7.10.4", "@babel/helper-replace-supers": "^7.12.1", "@babel/helper-split-export-declaration": "^7.10.4"}}, "@babel/helper-create-regexp-features-plugin": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.14.5.tgz", "integrity": "sha1-x9WsXpz2IcJgV3Ivt6ikxYiTWMQ=", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.14.5", "regexpu-core": "^4.7.1"}}, "@babel/helper-define-polyfill-provider": {"version": "0.2.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.2.3.tgz", "integrity": "sha1-BSXt7FCUZTooJojTTYRuTHXpwLY=", "dev": true, "requires": {"@babel/helper-compilation-targets": "^7.13.0", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-plugin-utils": "^7.13.0", "@babel/traverse": "^7.13.0", "debug": "^4.1.1", "lodash.debounce": "^4.0.8", "resolve": "^1.14.2", "semver": "^6.1.2"}, "dependencies": {"@babel/code-frame": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/code-frame/-/code-frame-7.15.8.tgz", "integrity": "sha1-RZkMR62tsAwDZ3uqiSIffMI9JQM=", "dev": true, "requires": {"@babel/highlight": "^7.14.5"}}, "@babel/generator": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/generator/-/generator-7.15.8.tgz", "integrity": "sha1-+la+a1lpUs6yMQSM+E7kmaGcDNE=", "dev": true, "requires": {"@babel/types": "^7.15.6", "jsesc": "^2.5.1", "source-map": "^0.5.0"}}, "@babel/helper-function-name": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-function-name/-/helper-function-name-7.15.4.tgz", "integrity": "sha1-hFdE2vxDgaSl+2r6bD02+Yp4frw=", "dev": true, "requires": {"@babel/helper-get-function-arity": "^7.15.4", "@babel/template": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/helper-get-function-arity": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-get-function-arity/-/helper-get-function-arity-7.15.4.tgz", "integrity": "sha1-CYgYk0oTf854tTaj4BWGS+Hih5s=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}, "@babel/helper-split-export-declaration": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.15.4.tgz", "integrity": "sha1-rsq5Lc2+9qEKo7YqsgSwhfd24lc=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/highlight": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/highlight/-/highlight-7.14.5.tgz", "integrity": "sha1-aGGlLwOWZAUAH2qlNKAaJNmejNk=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.5", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/parser/-/parser-7.15.8.tgz", "integrity": "sha1-e6zcvnG9w/+TbVEMFdzqfPC5kBY=", "dev": true}, "@babel/template": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/template/-/template-7.15.4.tgz", "integrity": "sha1-UYmNNdzz+qZwxO5q/P1RfuE58ZQ=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/traverse": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/traverse/-/traverse-7.15.4.tgz", "integrity": "sha1-/4UQNnoUS/v/VS2eGOKPPiiJwi0=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/generator": "^7.15.4", "@babel/helper-function-name": "^7.15.4", "@babel/helper-hoist-variables": "^7.15.4", "@babel/helper-split-export-declaration": "^7.15.4", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4", "debug": "^4.1.0", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.15.6.tgz", "integrity": "sha1-mavcSCGLKIHAWN0KerBbmcm+dY8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.9", "to-fast-properties": "^2.0.0"}}, "debug": {"version": "4.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/-/debug-4.3.2.tgz", "integrity": "sha1-8KScGKyHeeMdSgxgKd+3aHPHQos=", "dev": true, "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "dev": true}, "semver": {"version": "6.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "dev": true}}}, "@babel/helper-environment-visitor": {"version": "7.16.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-environment-visitor/-/helper-environment-visitor-7.16.5.tgz", "integrity": "sha1-9qfzizxtiwfIj66gg8RsCe9UUbg=", "dev": true, "requires": {"@babel/types": "^7.16.0"}, "dependencies": {"@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/types": {"version": "7.16.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.16.0.tgz", "integrity": "sha1-2zsxOAT5aq3Qt3bEgj4SetZyibo=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.15.7", "to-fast-properties": "^2.0.0"}}}}, "@babel/helper-explode-assignable-expression": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.15.4.tgz", "integrity": "sha1-+a7J0hnycer5K59WFZjKayaCYAw=", "dev": true, "requires": {"@babel/types": "^7.15.4"}, "dependencies": {"@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/types": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.15.6.tgz", "integrity": "sha1-mavcSCGLKIHAWN0KerBbmcm+dY8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.9", "to-fast-properties": "^2.0.0"}}}}, "@babel/helper-function-name": {"version": "7.12.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-function-name/download/@babel/helper-function-name-7.12.11.tgz", "integrity": "sha1-H9dziu5dz1PD7P8k8dqcUR7Ee0I=", "dev": true, "requires": {"@babel/helper-get-function-arity": "^7.12.10", "@babel/template": "^7.12.7", "@babel/types": "^7.12.11"}}, "@babel/helper-get-function-arity": {"version": "7.12.10", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.12.10.tgz", "integrity": "sha1-sViBejFltfqiBHgl36YZcN3MFs8=", "dev": true, "requires": {"@babel/types": "^7.12.10"}}, "@babel/helper-hoist-variables": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-hoist-variables/-/helper-hoist-variables-7.15.4.tgz", "integrity": "sha1-CZk6MlnA6Rj5nRBCYd/fwDPxeN8=", "dev": true, "requires": {"@babel/types": "^7.15.4"}, "dependencies": {"@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/types": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.15.6.tgz", "integrity": "sha1-mavcSCGLKIHAWN0KerBbmcm+dY8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.9", "to-fast-properties": "^2.0.0"}}}}, "@babel/helper-member-expression-to-functions": {"version": "7.12.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.12.7.tgz", "integrity": "sha1-qne9A5bsgRTl4weH76eFmdh0qFU=", "dev": true, "requires": {"@babel/types": "^7.12.7"}}, "@babel/helper-module-imports": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-module-imports/-/helper-module-imports-7.15.4.tgz", "integrity": "sha1-4YAH0jBjLeoZtHhTuYRHbntOED8=", "dev": true, "requires": {"@babel/types": "^7.15.4"}, "dependencies": {"@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/types": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.15.6.tgz", "integrity": "sha1-mavcSCGLKIHAWN0KerBbmcm+dY8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.9", "to-fast-properties": "^2.0.0"}}}}, "@babel/helper-module-transforms": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-module-transforms/-/helper-module-transforms-7.15.8.tgz", "integrity": "sha1-2MDnWoelLjdKjyX4VRdHhqCUmLI=", "dev": true, "requires": {"@babel/helper-module-imports": "^7.15.4", "@babel/helper-replace-supers": "^7.15.4", "@babel/helper-simple-access": "^7.15.4", "@babel/helper-split-export-declaration": "^7.15.4", "@babel/helper-validator-identifier": "^7.15.7", "@babel/template": "^7.15.4", "@babel/traverse": "^7.15.4", "@babel/types": "^7.15.6"}, "dependencies": {"@babel/code-frame": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/code-frame/-/code-frame-7.15.8.tgz", "integrity": "sha1-RZkMR62tsAwDZ3uqiSIffMI9JQM=", "dev": true, "requires": {"@babel/highlight": "^7.14.5"}}, "@babel/generator": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/generator/-/generator-7.15.8.tgz", "integrity": "sha1-+la+a1lpUs6yMQSM+E7kmaGcDNE=", "dev": true, "requires": {"@babel/types": "^7.15.6", "jsesc": "^2.5.1", "source-map": "^0.5.0"}}, "@babel/helper-function-name": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-function-name/-/helper-function-name-7.15.4.tgz", "integrity": "sha1-hFdE2vxDgaSl+2r6bD02+Yp4frw=", "dev": true, "requires": {"@babel/helper-get-function-arity": "^7.15.4", "@babel/template": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/helper-get-function-arity": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-get-function-arity/-/helper-get-function-arity-7.15.4.tgz", "integrity": "sha1-CYgYk0oTf854tTaj4BWGS+Hih5s=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-member-expression-to-functions": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.15.4.tgz", "integrity": "sha1-v9NNybupgkpGWLAxfsL9VxpR5u8=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-optimise-call-expression": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.15.4.tgz", "integrity": "sha1-8xClEho7nMUtmrGRIr1ymCLe4XE=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-replace-supers": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-replace-supers/-/helper-replace-supers-7.15.4.tgz", "integrity": "sha1-UqirJrqRjH9t7ihiiwcHGse3NHo=", "dev": true, "requires": {"@babel/helper-member-expression-to-functions": "^7.15.4", "@babel/helper-optimise-call-expression": "^7.15.4", "@babel/traverse": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/helper-split-export-declaration": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.15.4.tgz", "integrity": "sha1-rsq5Lc2+9qEKo7YqsgSwhfd24lc=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/highlight": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/highlight/-/highlight-7.14.5.tgz", "integrity": "sha1-aGGlLwOWZAUAH2qlNKAaJNmejNk=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.5", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/parser/-/parser-7.15.8.tgz", "integrity": "sha1-e6zcvnG9w/+TbVEMFdzqfPC5kBY=", "dev": true}, "@babel/template": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/template/-/template-7.15.4.tgz", "integrity": "sha1-UYmNNdzz+qZwxO5q/P1RfuE58ZQ=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/traverse": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/traverse/-/traverse-7.15.4.tgz", "integrity": "sha1-/4UQNnoUS/v/VS2eGOKPPiiJwi0=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/generator": "^7.15.4", "@babel/helper-function-name": "^7.15.4", "@babel/helper-hoist-variables": "^7.15.4", "@babel/helper-split-export-declaration": "^7.15.4", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4", "debug": "^4.1.0", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.15.6.tgz", "integrity": "sha1-mavcSCGLKIHAWN0KerBbmcm+dY8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.9", "to-fast-properties": "^2.0.0"}}, "debug": {"version": "4.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/-/debug-4.3.2.tgz", "integrity": "sha1-8KScGKyHeeMdSgxgKd+3aHPHQos=", "dev": true, "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "dev": true}}}, "@babel/helper-optimise-call-expression": {"version": "7.12.10", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.12.10.tgz", "integrity": "sha1-lMpOMG7hGn3W6fQoI+Ksa0mIHi0=", "dev": true, "requires": {"@babel/types": "^7.12.10"}}, "@babel/helper-plugin-utils": {"version": "7.10.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.10.4.tgz", "integrity": "sha1-L3WoMSadT2d95JmG3/WZJ1M883U=", "dev": true}, "@babel/helper-remap-async-to-generator": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.15.4.tgz", "integrity": "sha1-JjfAcx5MkPv1isWLULK1oZL8lw8=", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.15.4", "@babel/helper-wrap-function": "^7.15.4", "@babel/types": "^7.15.4"}, "dependencies": {"@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/types": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.15.6.tgz", "integrity": "sha1-mavcSCGLKIHAWN0KerBbmcm+dY8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.9", "to-fast-properties": "^2.0.0"}}}}, "@babel/helper-replace-supers": {"version": "7.12.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.12.11.tgz", "integrity": "sha1-6lEWWPxmx5CPkjEG3YjgjRmX1g0=", "dev": true, "requires": {"@babel/helper-member-expression-to-functions": "^7.12.7", "@babel/helper-optimise-call-expression": "^7.12.10", "@babel/traverse": "^7.12.10", "@babel/types": "^7.12.11"}}, "@babel/helper-simple-access": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-simple-access/-/helper-simple-access-7.15.4.tgz", "integrity": "sha1-rDaJBavx3o6XgUNLY12PhnS8wTs=", "dev": true, "requires": {"@babel/types": "^7.15.4"}, "dependencies": {"@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/types": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.15.6.tgz", "integrity": "sha1-mavcSCGLKIHAWN0KerBbmcm+dY8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.9", "to-fast-properties": "^2.0.0"}}}}, "@babel/helper-skip-transparent-expression-wrappers": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.15.4.tgz", "integrity": "sha1-cH29uh9K0Po0+RFPyBl67H1dous=", "dev": true, "requires": {"@babel/types": "^7.15.4"}, "dependencies": {"@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/types": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.15.6.tgz", "integrity": "sha1-mavcSCGLKIHAWN0KerBbmcm+dY8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.9", "to-fast-properties": "^2.0.0"}}}}, "@babel/helper-split-export-declaration": {"version": "7.12.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.12.11.tgz", "integrity": "sha1-G0zEJEWGQ8R9NwIiI9oz126kYDo=", "dev": true, "requires": {"@babel/types": "^7.12.11"}}, "@babel/helper-validator-identifier": {"version": "7.12.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.12.11.tgz", "integrity": "sha1-yaHwIZF9y1zPDU5FPjmQIpgfye0=", "dev": true}, "@babel/helper-validator-option": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-option/-/helper-validator-option-7.14.5.tgz", "integrity": "sha1-bnKh//GNXfy4eOHmLxoCHEty1aM=", "dev": true}, "@babel/helper-wrap-function": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-wrap-function/-/helper-wrap-function-7.15.4.tgz", "integrity": "sha1-b3VLJEbPrz1hJSPmq415wnw6Pec=", "dev": true, "requires": {"@babel/helper-function-name": "^7.15.4", "@babel/template": "^7.15.4", "@babel/traverse": "^7.15.4", "@babel/types": "^7.15.4"}, "dependencies": {"@babel/code-frame": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/code-frame/-/code-frame-7.15.8.tgz", "integrity": "sha1-RZkMR62tsAwDZ3uqiSIffMI9JQM=", "dev": true, "requires": {"@babel/highlight": "^7.14.5"}}, "@babel/generator": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/generator/-/generator-7.15.8.tgz", "integrity": "sha1-+la+a1lpUs6yMQSM+E7kmaGcDNE=", "dev": true, "requires": {"@babel/types": "^7.15.6", "jsesc": "^2.5.1", "source-map": "^0.5.0"}}, "@babel/helper-function-name": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-function-name/-/helper-function-name-7.15.4.tgz", "integrity": "sha1-hFdE2vxDgaSl+2r6bD02+Yp4frw=", "dev": true, "requires": {"@babel/helper-get-function-arity": "^7.15.4", "@babel/template": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/helper-get-function-arity": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-get-function-arity/-/helper-get-function-arity-7.15.4.tgz", "integrity": "sha1-CYgYk0oTf854tTaj4BWGS+Hih5s=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-split-export-declaration": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.15.4.tgz", "integrity": "sha1-rsq5Lc2+9qEKo7YqsgSwhfd24lc=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/highlight": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/highlight/-/highlight-7.14.5.tgz", "integrity": "sha1-aGGlLwOWZAUAH2qlNKAaJNmejNk=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.5", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/parser/-/parser-7.15.8.tgz", "integrity": "sha1-e6zcvnG9w/+TbVEMFdzqfPC5kBY=", "dev": true}, "@babel/template": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/template/-/template-7.15.4.tgz", "integrity": "sha1-UYmNNdzz+qZwxO5q/P1RfuE58ZQ=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/traverse": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/traverse/-/traverse-7.15.4.tgz", "integrity": "sha1-/4UQNnoUS/v/VS2eGOKPPiiJwi0=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/generator": "^7.15.4", "@babel/helper-function-name": "^7.15.4", "@babel/helper-hoist-variables": "^7.15.4", "@babel/helper-split-export-declaration": "^7.15.4", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4", "debug": "^4.1.0", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.15.6.tgz", "integrity": "sha1-mavcSCGLKIHAWN0KerBbmcm+dY8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.9", "to-fast-properties": "^2.0.0"}}, "debug": {"version": "4.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/-/debug-4.3.2.tgz", "integrity": "sha1-8KScGKyHeeMdSgxgKd+3aHPHQos=", "dev": true, "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "dev": true}}}, "@babel/helpers": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helpers/-/helpers-7.15.4.tgz", "integrity": "sha1-X0DwIFCjAnEho89I1JfAXFVer0M=", "dev": true, "requires": {"@babel/template": "^7.15.4", "@babel/traverse": "^7.15.4", "@babel/types": "^7.15.4"}, "dependencies": {"@babel/code-frame": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/code-frame/-/code-frame-7.15.8.tgz", "integrity": "sha1-RZkMR62tsAwDZ3uqiSIffMI9JQM=", "dev": true, "requires": {"@babel/highlight": "^7.14.5"}}, "@babel/generator": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/generator/-/generator-7.15.8.tgz", "integrity": "sha1-+la+a1lpUs6yMQSM+E7kmaGcDNE=", "dev": true, "requires": {"@babel/types": "^7.15.6", "jsesc": "^2.5.1", "source-map": "^0.5.0"}}, "@babel/helper-function-name": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-function-name/-/helper-function-name-7.15.4.tgz", "integrity": "sha1-hFdE2vxDgaSl+2r6bD02+Yp4frw=", "dev": true, "requires": {"@babel/helper-get-function-arity": "^7.15.4", "@babel/template": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/helper-get-function-arity": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-get-function-arity/-/helper-get-function-arity-7.15.4.tgz", "integrity": "sha1-CYgYk0oTf854tTaj4BWGS+Hih5s=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-split-export-declaration": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.15.4.tgz", "integrity": "sha1-rsq5Lc2+9qEKo7YqsgSwhfd24lc=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/highlight": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/highlight/-/highlight-7.14.5.tgz", "integrity": "sha1-aGGlLwOWZAUAH2qlNKAaJNmejNk=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.5", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/parser/-/parser-7.15.8.tgz", "integrity": "sha1-e6zcvnG9w/+TbVEMFdzqfPC5kBY=", "dev": true}, "@babel/template": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/template/-/template-7.15.4.tgz", "integrity": "sha1-UYmNNdzz+qZwxO5q/P1RfuE58ZQ=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/traverse": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/traverse/-/traverse-7.15.4.tgz", "integrity": "sha1-/4UQNnoUS/v/VS2eGOKPPiiJwi0=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/generator": "^7.15.4", "@babel/helper-function-name": "^7.15.4", "@babel/helper-hoist-variables": "^7.15.4", "@babel/helper-split-export-declaration": "^7.15.4", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4", "debug": "^4.1.0", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.15.6.tgz", "integrity": "sha1-mavcSCGLKIHAWN0KerBbmcm+dY8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.9", "to-fast-properties": "^2.0.0"}}, "debug": {"version": "4.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/-/debug-4.3.2.tgz", "integrity": "sha1-8KScGKyHeeMdSgxgKd+3aHPHQos=", "dev": true, "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "dev": true}}}, "@babel/highlight": {"version": "7.10.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/highlight/download/@babel/highlight-7.10.4.tgz", "integrity": "sha1-fRvf1ldTU4+r5sOFls23bZrGAUM=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.10.4", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.12.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/parser/download/@babel/parser-7.12.11.tgz", "integrity": "sha1-nONZW810vFxGaQXobFNbiyUBHnk=", "dev": true}, "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.15.4.tgz", "integrity": "sha1-296rsegPYi2fC1g++ymZYF4KVn4=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.15.4", "@babel/plugin-proposal-optional-chaining": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-proposal-async-generator-functions": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.15.8.tgz", "integrity": "sha1-oxAPeF+rQ1eYfEIjqxsCtZkEhAM=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-remap-async-to-generator": "^7.15.4", "@babel/plugin-syntax-async-generators": "^7.8.4"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-proposal-class-properties": {"version": "7.12.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.12.1.tgz", "integrity": "sha1-oIL/VB8qKaSCEGW4rdk0bAwW5d4=", "dev": true, "requires": {"@babel/helper-create-class-features-plugin": "^7.12.1", "@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-proposal-class-static-block": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-class-static-block/-/plugin-proposal-class-static-block-7.15.4.tgz", "integrity": "sha1-PnymEoRTwInotHepn5cMY/wcuNc=", "dev": true, "requires": {"@babel/helper-create-class-features-plugin": "^7.15.4", "@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "dependencies": {"@babel/code-frame": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/code-frame/-/code-frame-7.15.8.tgz", "integrity": "sha1-RZkMR62tsAwDZ3uqiSIffMI9JQM=", "dev": true, "requires": {"@babel/highlight": "^7.14.5"}}, "@babel/generator": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/generator/-/generator-7.15.8.tgz", "integrity": "sha1-+la+a1lpUs6yMQSM+E7kmaGcDNE=", "dev": true, "requires": {"@babel/types": "^7.15.6", "jsesc": "^2.5.1", "source-map": "^0.5.0"}}, "@babel/helper-create-class-features-plugin": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.15.4.tgz", "integrity": "sha1-f5d8F70SpfujY8sZvqCQOUvzfS4=", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.15.4", "@babel/helper-function-name": "^7.15.4", "@babel/helper-member-expression-to-functions": "^7.15.4", "@babel/helper-optimise-call-expression": "^7.15.4", "@babel/helper-replace-supers": "^7.15.4", "@babel/helper-split-export-declaration": "^7.15.4"}}, "@babel/helper-function-name": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-function-name/-/helper-function-name-7.15.4.tgz", "integrity": "sha1-hFdE2vxDgaSl+2r6bD02+Yp4frw=", "dev": true, "requires": {"@babel/helper-get-function-arity": "^7.15.4", "@babel/template": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/helper-get-function-arity": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-get-function-arity/-/helper-get-function-arity-7.15.4.tgz", "integrity": "sha1-CYgYk0oTf854tTaj4BWGS+Hih5s=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-member-expression-to-functions": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.15.4.tgz", "integrity": "sha1-v9NNybupgkpGWLAxfsL9VxpR5u8=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-optimise-call-expression": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.15.4.tgz", "integrity": "sha1-8xClEho7nMUtmrGRIr1ymCLe4XE=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}, "@babel/helper-replace-supers": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-replace-supers/-/helper-replace-supers-7.15.4.tgz", "integrity": "sha1-UqirJrqRjH9t7ihiiwcHGse3NHo=", "dev": true, "requires": {"@babel/helper-member-expression-to-functions": "^7.15.4", "@babel/helper-optimise-call-expression": "^7.15.4", "@babel/traverse": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/helper-split-export-declaration": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.15.4.tgz", "integrity": "sha1-rsq5Lc2+9qEKo7YqsgSwhfd24lc=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/highlight": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/highlight/-/highlight-7.14.5.tgz", "integrity": "sha1-aGGlLwOWZAUAH2qlNKAaJNmejNk=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.5", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/parser/-/parser-7.15.8.tgz", "integrity": "sha1-e6zcvnG9w/+TbVEMFdzqfPC5kBY=", "dev": true}, "@babel/template": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/template/-/template-7.15.4.tgz", "integrity": "sha1-UYmNNdzz+qZwxO5q/P1RfuE58ZQ=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/traverse": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/traverse/-/traverse-7.15.4.tgz", "integrity": "sha1-/4UQNnoUS/v/VS2eGOKPPiiJwi0=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/generator": "^7.15.4", "@babel/helper-function-name": "^7.15.4", "@babel/helper-hoist-variables": "^7.15.4", "@babel/helper-split-export-declaration": "^7.15.4", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4", "debug": "^4.1.0", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.15.6.tgz", "integrity": "sha1-mavcSCGLKIHAWN0KerBbmcm+dY8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.9", "to-fast-properties": "^2.0.0"}}, "debug": {"version": "4.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/-/debug-4.3.2.tgz", "integrity": "sha1-8KScGKyHeeMdSgxgKd+3aHPHQos=", "dev": true, "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "dev": true}}}, "@babel/plugin-proposal-dynamic-import": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-dynamic-import/-/plugin-proposal-dynamic-import-7.14.5.tgz", "integrity": "sha1-DGYX30YcDB+P/ztHzVl3I2AQHSw=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-proposal-export-namespace-from": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-export-namespace-from/-/plugin-proposal-export-namespace-from-7.14.5.tgz", "integrity": "sha1-260kQxDObM0IMHIWfYzqg6Uvr3Y=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-export-namespace-from": "^7.8.3"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-proposal-json-strings": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.14.5.tgz", "integrity": "sha1-ON5g2zYug6PYyUSshY3fnwwiOes=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-json-strings": "^7.8.3"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-proposal-logical-assignment-operators": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-logical-assignment-operators/-/plugin-proposal-logical-assignment-operators-7.14.5.tgz", "integrity": "sha1-bmIpwqmbAqspFfglceDMZGpAxzg=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-proposal-nullish-coalescing-operator": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.14.5.tgz", "integrity": "sha1-7jhYnOAOLMWbKZ7D6kBvzToP2vY=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-proposal-numeric-separator": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.14.5.tgz", "integrity": "sha1-g2Mb8z2aUd8YTCECoGmsDFjAXxg=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-proposal-object-rest-spread": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.15.6.tgz", "integrity": "sha1-72gFDIcD0Hslr0AsuWz380po7RE=", "dev": true, "requires": {"@babel/compat-data": "^7.15.0", "@babel/helper-compilation-targets": "^7.15.4", "@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-transform-parameters": "^7.15.4"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-proposal-optional-catch-binding": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.14.5.tgz", "integrity": "sha1-k53W7d7/Omf997PwRLU0cmJZjDw=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-proposal-optional-chaining": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.14.5.tgz", "integrity": "sha1-+oNlHmCjYOPxN5fu8AuNUZaVtgM=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.14.5", "@babel/plugin-syntax-optional-chaining": "^7.8.3"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-proposal-private-methods": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-private-methods/-/plugin-proposal-private-methods-7.14.5.tgz", "integrity": "sha1-N0RklZlrKUXzD1vltg1eKqT1eS0=", "dev": true, "requires": {"@babel/helper-create-class-features-plugin": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/code-frame": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/code-frame/-/code-frame-7.15.8.tgz", "integrity": "sha1-RZkMR62tsAwDZ3uqiSIffMI9JQM=", "dev": true, "requires": {"@babel/highlight": "^7.14.5"}}, "@babel/generator": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/generator/-/generator-7.15.8.tgz", "integrity": "sha1-+la+a1lpUs6yMQSM+E7kmaGcDNE=", "dev": true, "requires": {"@babel/types": "^7.15.6", "jsesc": "^2.5.1", "source-map": "^0.5.0"}}, "@babel/helper-create-class-features-plugin": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.15.4.tgz", "integrity": "sha1-f5d8F70SpfujY8sZvqCQOUvzfS4=", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.15.4", "@babel/helper-function-name": "^7.15.4", "@babel/helper-member-expression-to-functions": "^7.15.4", "@babel/helper-optimise-call-expression": "^7.15.4", "@babel/helper-replace-supers": "^7.15.4", "@babel/helper-split-export-declaration": "^7.15.4"}}, "@babel/helper-function-name": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-function-name/-/helper-function-name-7.15.4.tgz", "integrity": "sha1-hFdE2vxDgaSl+2r6bD02+Yp4frw=", "dev": true, "requires": {"@babel/helper-get-function-arity": "^7.15.4", "@babel/template": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/helper-get-function-arity": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-get-function-arity/-/helper-get-function-arity-7.15.4.tgz", "integrity": "sha1-CYgYk0oTf854tTaj4BWGS+Hih5s=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-member-expression-to-functions": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.15.4.tgz", "integrity": "sha1-v9NNybupgkpGWLAxfsL9VxpR5u8=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-optimise-call-expression": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.15.4.tgz", "integrity": "sha1-8xClEho7nMUtmrGRIr1ymCLe4XE=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}, "@babel/helper-replace-supers": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-replace-supers/-/helper-replace-supers-7.15.4.tgz", "integrity": "sha1-UqirJrqRjH9t7ihiiwcHGse3NHo=", "dev": true, "requires": {"@babel/helper-member-expression-to-functions": "^7.15.4", "@babel/helper-optimise-call-expression": "^7.15.4", "@babel/traverse": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/helper-split-export-declaration": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.15.4.tgz", "integrity": "sha1-rsq5Lc2+9qEKo7YqsgSwhfd24lc=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/highlight": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/highlight/-/highlight-7.14.5.tgz", "integrity": "sha1-aGGlLwOWZAUAH2qlNKAaJNmejNk=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.5", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/parser/-/parser-7.15.8.tgz", "integrity": "sha1-e6zcvnG9w/+TbVEMFdzqfPC5kBY=", "dev": true}, "@babel/template": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/template/-/template-7.15.4.tgz", "integrity": "sha1-UYmNNdzz+qZwxO5q/P1RfuE58ZQ=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/traverse": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/traverse/-/traverse-7.15.4.tgz", "integrity": "sha1-/4UQNnoUS/v/VS2eGOKPPiiJwi0=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/generator": "^7.15.4", "@babel/helper-function-name": "^7.15.4", "@babel/helper-hoist-variables": "^7.15.4", "@babel/helper-split-export-declaration": "^7.15.4", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4", "debug": "^4.1.0", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.15.6.tgz", "integrity": "sha1-mavcSCGLKIHAWN0KerBbmcm+dY8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.9", "to-fast-properties": "^2.0.0"}}, "debug": {"version": "4.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/-/debug-4.3.2.tgz", "integrity": "sha1-8KScGKyHeeMdSgxgKd+3aHPHQos=", "dev": true, "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "dev": true}}}, "@babel/plugin-proposal-private-property-in-object": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.15.4.tgz", "integrity": "sha1-VcXjtNAmH9RP5jfj9iTPsPSE4+U=", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.15.4", "@babel/helper-create-class-features-plugin": "^7.15.4", "@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "dependencies": {"@babel/code-frame": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/code-frame/-/code-frame-7.15.8.tgz", "integrity": "sha1-RZkMR62tsAwDZ3uqiSIffMI9JQM=", "dev": true, "requires": {"@babel/highlight": "^7.14.5"}}, "@babel/generator": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/generator/-/generator-7.15.8.tgz", "integrity": "sha1-+la+a1lpUs6yMQSM+E7kmaGcDNE=", "dev": true, "requires": {"@babel/types": "^7.15.6", "jsesc": "^2.5.1", "source-map": "^0.5.0"}}, "@babel/helper-create-class-features-plugin": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.15.4.tgz", "integrity": "sha1-f5d8F70SpfujY8sZvqCQOUvzfS4=", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.15.4", "@babel/helper-function-name": "^7.15.4", "@babel/helper-member-expression-to-functions": "^7.15.4", "@babel/helper-optimise-call-expression": "^7.15.4", "@babel/helper-replace-supers": "^7.15.4", "@babel/helper-split-export-declaration": "^7.15.4"}}, "@babel/helper-function-name": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-function-name/-/helper-function-name-7.15.4.tgz", "integrity": "sha1-hFdE2vxDgaSl+2r6bD02+Yp4frw=", "dev": true, "requires": {"@babel/helper-get-function-arity": "^7.15.4", "@babel/template": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/helper-get-function-arity": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-get-function-arity/-/helper-get-function-arity-7.15.4.tgz", "integrity": "sha1-CYgYk0oTf854tTaj4BWGS+Hih5s=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-member-expression-to-functions": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.15.4.tgz", "integrity": "sha1-v9NNybupgkpGWLAxfsL9VxpR5u8=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-optimise-call-expression": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.15.4.tgz", "integrity": "sha1-8xClEho7nMUtmrGRIr1ymCLe4XE=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}, "@babel/helper-replace-supers": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-replace-supers/-/helper-replace-supers-7.15.4.tgz", "integrity": "sha1-UqirJrqRjH9t7ihiiwcHGse3NHo=", "dev": true, "requires": {"@babel/helper-member-expression-to-functions": "^7.15.4", "@babel/helper-optimise-call-expression": "^7.15.4", "@babel/traverse": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/helper-split-export-declaration": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.15.4.tgz", "integrity": "sha1-rsq5Lc2+9qEKo7YqsgSwhfd24lc=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/highlight": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/highlight/-/highlight-7.14.5.tgz", "integrity": "sha1-aGGlLwOWZAUAH2qlNKAaJNmejNk=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.5", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/parser/-/parser-7.15.8.tgz", "integrity": "sha1-e6zcvnG9w/+TbVEMFdzqfPC5kBY=", "dev": true}, "@babel/template": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/template/-/template-7.15.4.tgz", "integrity": "sha1-UYmNNdzz+qZwxO5q/P1RfuE58ZQ=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/traverse": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/traverse/-/traverse-7.15.4.tgz", "integrity": "sha1-/4UQNnoUS/v/VS2eGOKPPiiJwi0=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/generator": "^7.15.4", "@babel/helper-function-name": "^7.15.4", "@babel/helper-hoist-variables": "^7.15.4", "@babel/helper-split-export-declaration": "^7.15.4", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4", "debug": "^4.1.0", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.15.6.tgz", "integrity": "sha1-mavcSCGLKIHAWN0KerBbmcm+dY8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.9", "to-fast-properties": "^2.0.0"}}, "debug": {"version": "4.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/-/debug-4.3.2.tgz", "integrity": "sha1-8KScGKyHeeMdSgxgKd+3aHPHQos=", "dev": true, "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "dev": true}}}, "@babel/plugin-proposal-unicode-property-regex": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.14.5.tgz", "integrity": "sha1-D5XuDnV6XWR/N42qDsp+k/qou+g=", "dev": true, "requires": {"@babel/helper-create-regexp-features-plugin": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-syntax-async-generators": {"version": "7.8.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz", "integrity": "sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-class-properties": {"version": "7.12.13", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz", "integrity": "sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.12.13"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-syntax-class-static-block": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz", "integrity": "sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-syntax-dynamic-import": {"version": "7.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz", "integrity": "sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-export-namespace-from": {"version": "7.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz", "integrity": "sha1-AolkqbqA28CUyRXEh618TnpmRlo=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.3"}}, "@babel/plugin-syntax-json-strings": {"version": "7.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz", "integrity": "sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-logical-assignment-operators": {"version": "7.10.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "integrity": "sha1-ypHvRjA1MESLkGZSusLp/plB9pk=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "integrity": "sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz", "integrity": "sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz", "integrity": "sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz", "integrity": "sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz", "integrity": "sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-private-property-in-object": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz", "integrity": "sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-syntax-top-level-await": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz", "integrity": "sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-arrow-functions": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.14.5.tgz", "integrity": "sha1-9xh9lYinaN0IC/TJ/+EX6mL3hio=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-async-to-generator": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.14.5.tgz", "integrity": "sha1-cseJCE2PIJSsuUVjOUPvhEPTnmc=", "dev": true, "requires": {"@babel/helper-module-imports": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-remap-async-to-generator": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-block-scoped-functions": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.14.5.tgz", "integrity": "sha1-5IZB2ZnUvBV6Z+8zautUvET9OtQ=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-block-scoping": {"version": "7.15.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.15.3.tgz", "integrity": "sha1-lMgabi/CMLzObvU3rJah5NKzr68=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-classes": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-classes/-/plugin-transform-classes-7.15.4.tgz", "integrity": "sha1-UK7heq9/MyrkTjvOTC4QU01dO/E=", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.15.4", "@babel/helper-function-name": "^7.15.4", "@babel/helper-optimise-call-expression": "^7.15.4", "@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-replace-supers": "^7.15.4", "@babel/helper-split-export-declaration": "^7.15.4", "globals": "^11.1.0"}, "dependencies": {"@babel/code-frame": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/code-frame/-/code-frame-7.15.8.tgz", "integrity": "sha1-RZkMR62tsAwDZ3uqiSIffMI9JQM=", "dev": true, "requires": {"@babel/highlight": "^7.14.5"}}, "@babel/generator": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/generator/-/generator-7.15.8.tgz", "integrity": "sha1-+la+a1lpUs6yMQSM+E7kmaGcDNE=", "dev": true, "requires": {"@babel/types": "^7.15.6", "jsesc": "^2.5.1", "source-map": "^0.5.0"}}, "@babel/helper-function-name": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-function-name/-/helper-function-name-7.15.4.tgz", "integrity": "sha1-hFdE2vxDgaSl+2r6bD02+Yp4frw=", "dev": true, "requires": {"@babel/helper-get-function-arity": "^7.15.4", "@babel/template": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/helper-get-function-arity": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-get-function-arity/-/helper-get-function-arity-7.15.4.tgz", "integrity": "sha1-CYgYk0oTf854tTaj4BWGS+Hih5s=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-member-expression-to-functions": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.15.4.tgz", "integrity": "sha1-v9NNybupgkpGWLAxfsL9VxpR5u8=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-optimise-call-expression": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.15.4.tgz", "integrity": "sha1-8xClEho7nMUtmrGRIr1ymCLe4XE=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}, "@babel/helper-replace-supers": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-replace-supers/-/helper-replace-supers-7.15.4.tgz", "integrity": "sha1-UqirJrqRjH9t7ihiiwcHGse3NHo=", "dev": true, "requires": {"@babel/helper-member-expression-to-functions": "^7.15.4", "@babel/helper-optimise-call-expression": "^7.15.4", "@babel/traverse": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/helper-split-export-declaration": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.15.4.tgz", "integrity": "sha1-rsq5Lc2+9qEKo7YqsgSwhfd24lc=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/highlight": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/highlight/-/highlight-7.14.5.tgz", "integrity": "sha1-aGGlLwOWZAUAH2qlNKAaJNmejNk=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.5", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/parser/-/parser-7.15.8.tgz", "integrity": "sha1-e6zcvnG9w/+TbVEMFdzqfPC5kBY=", "dev": true}, "@babel/template": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/template/-/template-7.15.4.tgz", "integrity": "sha1-UYmNNdzz+qZwxO5q/P1RfuE58ZQ=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/traverse": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/traverse/-/traverse-7.15.4.tgz", "integrity": "sha1-/4UQNnoUS/v/VS2eGOKPPiiJwi0=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/generator": "^7.15.4", "@babel/helper-function-name": "^7.15.4", "@babel/helper-hoist-variables": "^7.15.4", "@babel/helper-split-export-declaration": "^7.15.4", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4", "debug": "^4.1.0", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.15.6.tgz", "integrity": "sha1-mavcSCGLKIHAWN0KerBbmcm+dY8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.9", "to-fast-properties": "^2.0.0"}}, "debug": {"version": "4.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/-/debug-4.3.2.tgz", "integrity": "sha1-8KScGKyHeeMdSgxgKd+3aHPHQos=", "dev": true, "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "dev": true}}}, "@babel/plugin-transform-computed-properties": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.14.5.tgz", "integrity": "sha1-G514mHQg0RIj1BGVRhzEO5dLIE8=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-destructuring": {"version": "7.14.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.14.7.tgz", "integrity": "sha1-CtWO034j4iCE0QnxhSYINeVVdXY=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-dotall-regex": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.14.5.tgz", "integrity": "sha1-L2v3bka9+AQ7Tn4WzyRTJim6DHo=", "dev": true, "requires": {"@babel/helper-create-regexp-features-plugin": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-duplicate-keys": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.14.5.tgz", "integrity": "sha1-NlpIRIgb3xUB46nwJw5/D5EXeVQ=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-exponentiation-operator": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.14.5.tgz", "integrity": "sha1-UVS43Wo9/m2Qkj1hckvT3uuQtJM=", "dev": true, "requires": {"@babel/helper-builder-binary-assignment-operator-visitor": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-for-of": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.15.4.tgz", "integrity": "sha1-J<PERSON>YszicYz7KXFfQW511SY/s2qMI=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-function-name": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.14.5.tgz", "integrity": "sha1-6Bxl7LkAdG1/MYAva+0fUtkV1vI=", "dev": true, "requires": {"@babel/helper-function-name": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/code-frame": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/code-frame/-/code-frame-7.15.8.tgz", "integrity": "sha1-RZkMR62tsAwDZ3uqiSIffMI9JQM=", "dev": true, "requires": {"@babel/highlight": "^7.14.5"}}, "@babel/helper-function-name": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-function-name/-/helper-function-name-7.15.4.tgz", "integrity": "sha1-hFdE2vxDgaSl+2r6bD02+Yp4frw=", "dev": true, "requires": {"@babel/helper-get-function-arity": "^7.15.4", "@babel/template": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/helper-get-function-arity": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-get-function-arity/-/helper-get-function-arity-7.15.4.tgz", "integrity": "sha1-CYgYk0oTf854tTaj4BWGS+Hih5s=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}, "@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/highlight": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/highlight/-/highlight-7.14.5.tgz", "integrity": "sha1-aGGlLwOWZAUAH2qlNKAaJNmejNk=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.5", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/parser/-/parser-7.15.8.tgz", "integrity": "sha1-e6zcvnG9w/+TbVEMFdzqfPC5kBY=", "dev": true}, "@babel/template": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/template/-/template-7.15.4.tgz", "integrity": "sha1-UYmNNdzz+qZwxO5q/P1RfuE58ZQ=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/types": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.15.6.tgz", "integrity": "sha1-mavcSCGLKIHAWN0KerBbmcm+dY8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.9", "to-fast-properties": "^2.0.0"}}}}, "@babel/plugin-transform-literals": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-literals/-/plugin-transform-literals-7.14.5.tgz", "integrity": "sha1-QdBsf/XU0J489Fh70+zzkwxzD3g=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-member-expression-literals": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.14.5.tgz", "integrity": "sha1-s5zVISor8jWmF9Mg7CtIvMCRuKc=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-modules-amd": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.14.5.tgz", "integrity": "sha1-T9nOfjQRy4uDhISAtwQdgwBIWPc=", "dev": true, "requires": {"@babel/helper-module-transforms": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5", "babel-plugin-dynamic-import-node": "^2.3.3"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-modules-commonjs": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.15.4.tgz", "integrity": "sha1-ggEQEkDqu1p2wI72GylU92e2tME=", "dev": true, "requires": {"@babel/helper-module-transforms": "^7.15.4", "@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-simple-access": "^7.15.4", "babel-plugin-dynamic-import-node": "^2.3.3"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-modules-systemjs": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.15.4.tgz", "integrity": "sha1-tCiQxzSaeMgncZ8dLQzTjH0mgTI=", "dev": true, "requires": {"@babel/helper-hoist-variables": "^7.15.4", "@babel/helper-module-transforms": "^7.15.4", "@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-validator-identifier": "^7.14.9", "babel-plugin-dynamic-import-node": "^2.3.3"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}, "@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}}}, "@babel/plugin-transform-modules-umd": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.14.5.tgz", "integrity": "sha1-+2Yt/uaXzOJ0p82lJRkKeQlqpuA=", "dev": true, "requires": {"@babel/helper-module-transforms": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-named-capturing-groups-regex": {"version": "7.14.9", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.14.9.tgz", "integrity": "sha1-xo9cXRLS66ujdi5XwsT2NHpG57I=", "dev": true, "requires": {"@babel/helper-create-regexp-features-plugin": "^7.14.5"}}, "@babel/plugin-transform-new-target": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.14.5.tgz", "integrity": "sha1-Mb2ui5JdyEB26/zSqZQBQ67X2/g=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-object-super": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.14.5.tgz", "integrity": "sha1-0LX66snphZehYanPeMUn7ZNM3EU=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-replace-supers": "^7.14.5"}, "dependencies": {"@babel/code-frame": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/code-frame/-/code-frame-7.15.8.tgz", "integrity": "sha1-RZkMR62tsAwDZ3uqiSIffMI9JQM=", "dev": true, "requires": {"@babel/highlight": "^7.14.5"}}, "@babel/generator": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/generator/-/generator-7.15.8.tgz", "integrity": "sha1-+la+a1lpUs6yMQSM+E7kmaGcDNE=", "dev": true, "requires": {"@babel/types": "^7.15.6", "jsesc": "^2.5.1", "source-map": "^0.5.0"}}, "@babel/helper-function-name": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-function-name/-/helper-function-name-7.15.4.tgz", "integrity": "sha1-hFdE2vxDgaSl+2r6bD02+Yp4frw=", "dev": true, "requires": {"@babel/helper-get-function-arity": "^7.15.4", "@babel/template": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/helper-get-function-arity": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-get-function-arity/-/helper-get-function-arity-7.15.4.tgz", "integrity": "sha1-CYgYk0oTf854tTaj4BWGS+Hih5s=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-member-expression-to-functions": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.15.4.tgz", "integrity": "sha1-v9NNybupgkpGWLAxfsL9VxpR5u8=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-optimise-call-expression": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.15.4.tgz", "integrity": "sha1-8xClEho7nMUtmrGRIr1ymCLe4XE=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}, "@babel/helper-replace-supers": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-replace-supers/-/helper-replace-supers-7.15.4.tgz", "integrity": "sha1-UqirJrqRjH9t7ihiiwcHGse3NHo=", "dev": true, "requires": {"@babel/helper-member-expression-to-functions": "^7.15.4", "@babel/helper-optimise-call-expression": "^7.15.4", "@babel/traverse": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/helper-split-export-declaration": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.15.4.tgz", "integrity": "sha1-rsq5Lc2+9qEKo7YqsgSwhfd24lc=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/highlight": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/highlight/-/highlight-7.14.5.tgz", "integrity": "sha1-aGGlLwOWZAUAH2qlNKAaJNmejNk=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.5", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/parser/-/parser-7.15.8.tgz", "integrity": "sha1-e6zcvnG9w/+TbVEMFdzqfPC5kBY=", "dev": true}, "@babel/template": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/template/-/template-7.15.4.tgz", "integrity": "sha1-UYmNNdzz+qZwxO5q/P1RfuE58ZQ=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/traverse": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/traverse/-/traverse-7.15.4.tgz", "integrity": "sha1-/4UQNnoUS/v/VS2eGOKPPiiJwi0=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/generator": "^7.15.4", "@babel/helper-function-name": "^7.15.4", "@babel/helper-hoist-variables": "^7.15.4", "@babel/helper-split-export-declaration": "^7.15.4", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4", "debug": "^4.1.0", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.15.6.tgz", "integrity": "sha1-mavcSCGLKIHAWN0KerBbmcm+dY8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.9", "to-fast-properties": "^2.0.0"}}, "debug": {"version": "4.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/-/debug-4.3.2.tgz", "integrity": "sha1-8KScGKyHeeMdSgxgKd+3aHPHQos=", "dev": true, "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "dev": true}}}, "@babel/plugin-transform-parameters": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.15.4.tgz", "integrity": "sha1-XyKFzDFgv0jIUCQycWtIUE0p7WI=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-property-literals": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.14.5.tgz", "integrity": "sha1-DduqH4PbNgbxzfSEb6HftHNFizQ=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-regenerator": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.14.5.tgz", "integrity": "sha1-lnb9VwftKPUicnxbPAqoVERAsE8=", "dev": true, "requires": {"regenerator-transform": "^0.14.2"}}, "@babel/plugin-transform-reserved-words": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.14.5.tgz", "integrity": "sha1-xEWJtmHP2++NQwDcx0ad/6kvgwQ=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-runtime": {"version": "7.16.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.16.5.tgz", "integrity": "sha1-DMPwHWnymdWkLNnsQ7kup6d3uNs=", "dev": true, "requires": {"@babel/helper-module-imports": "^7.16.0", "@babel/helper-plugin-utils": "^7.16.5", "babel-plugin-polyfill-corejs2": "^0.3.0", "babel-plugin-polyfill-corejs3": "^0.4.0", "babel-plugin-polyfill-regenerator": "^0.3.0", "semver": "^6.3.0"}, "dependencies": {"@babel/code-frame": {"version": "7.16.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/code-frame/-/code-frame-7.16.0.tgz", "integrity": "sha1-DfyAMJvuyEEeZecGRhxAiwu5tDE=", "dev": true, "requires": {"@babel/highlight": "^7.16.0"}}, "@babel/generator": {"version": "7.16.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/generator/-/generator-7.16.5.tgz", "integrity": "sha1-JuEZLrj3jgo6yvPu3jxvyW0ivt8=", "dev": true, "requires": {"@babel/types": "^7.16.0", "jsesc": "^2.5.1", "source-map": "^0.5.0"}}, "@babel/helper-define-polyfill-provider": {"version": "0.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.3.0.tgz", "integrity": "sha1-xbEM9LMk/4QBQLsH4FuFZK8q6XE=", "dev": true, "requires": {"@babel/helper-compilation-targets": "^7.13.0", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-plugin-utils": "^7.13.0", "@babel/traverse": "^7.13.0", "debug": "^4.1.1", "lodash.debounce": "^4.0.8", "resolve": "^1.14.2", "semver": "^6.1.2"}}, "@babel/helper-function-name": {"version": "7.16.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-function-name/-/helper-function-name-7.16.0.tgz", "integrity": "sha1-t90Hl9ALv+5PB+nE6lsOMMi7FIE=", "dev": true, "requires": {"@babel/helper-get-function-arity": "^7.16.0", "@babel/template": "^7.16.0", "@babel/types": "^7.16.0"}}, "@babel/helper-get-function-arity": {"version": "7.16.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-get-function-arity/-/helper-get-function-arity-7.16.0.tgz", "integrity": "sha1-AIjHSGspqctdlIsaHeRttm4InPo=", "dev": true, "requires": {"@babel/types": "^7.16.0"}}, "@babel/helper-hoist-variables": {"version": "7.16.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-hoist-variables/-/helper-hoist-variables-7.16.0.tgz", "integrity": "sha1-TJAjwvHe9+KP9G/B2802o5vqqBo=", "dev": true, "requires": {"@babel/types": "^7.16.0"}}, "@babel/helper-module-imports": {"version": "7.16.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-module-imports/-/helper-module-imports-7.16.0.tgz", "integrity": "sha1-kFOOYLZy7PG0SPX09UM9N+eaPsM=", "dev": true, "requires": {"@babel/types": "^7.16.0"}}, "@babel/helper-plugin-utils": {"version": "7.16.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.16.5.tgz", "integrity": "sha1-r+N6RfOfzkSj1Qp5WBKepbGlwHQ=", "dev": true}, "@babel/helper-split-export-declaration": {"version": "7.16.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.16.0.tgz", "integrity": "sha1-KWcvQ2Y+k23zcKrrIr7ds7rsdDg=", "dev": true, "requires": {"@babel/types": "^7.16.0"}}, "@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/highlight": {"version": "7.16.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/highlight/-/highlight-7.16.0.tgz", "integrity": "sha1-bOsysspLj182H7f9gh4/3fShclo=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.15.7", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.16.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/parser/-/parser-7.16.6.tgz", "integrity": "sha1-jxlIKBk+j6eRZvNKS05S8+dpoxQ=", "dev": true}, "@babel/template": {"version": "7.16.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/template/-/template-7.16.0.tgz", "integrity": "sha1-0Wo16/TNdOICCDNW+rId2JNj3dY=", "dev": true, "requires": {"@babel/code-frame": "^7.16.0", "@babel/parser": "^7.16.0", "@babel/types": "^7.16.0"}}, "@babel/traverse": {"version": "7.16.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/traverse/-/traverse-7.16.5.tgz", "integrity": "sha1-19QAqCKccUpZuHYk/Gew8fvUsrM=", "dev": true, "requires": {"@babel/code-frame": "^7.16.0", "@babel/generator": "^7.16.5", "@babel/helper-environment-visitor": "^7.16.5", "@babel/helper-function-name": "^7.16.0", "@babel/helper-hoist-variables": "^7.16.0", "@babel/helper-split-export-declaration": "^7.16.0", "@babel/parser": "^7.16.5", "@babel/types": "^7.16.0", "debug": "^4.1.0", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.16.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.16.0.tgz", "integrity": "sha1-2zsxOAT5aq3Qt3bEgj4SetZyibo=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.15.7", "to-fast-properties": "^2.0.0"}}, "babel-plugin-polyfill-corejs2": {"version": "0.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.3.0.tgz", "integrity": "sha1-QHCC0NNVulZa8kEm+2y46RFSUf0=", "dev": true, "requires": {"@babel/compat-data": "^7.13.11", "@babel/helper-define-polyfill-provider": "^0.3.0", "semver": "^6.1.1"}}, "babel-plugin-polyfill-corejs3": {"version": "0.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.4.0.tgz", "integrity": "sha1-C1cfTPPWf5EVEvXASEKnuOgmMIc=", "dev": true, "requires": {"@babel/helper-define-polyfill-provider": "^0.3.0", "core-js-compat": "^3.18.0"}}, "babel-plugin-polyfill-regenerator": {"version": "0.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.3.0.tgz", "integrity": "sha1-nrvNcYbhoz4hxeIMrk55g5SVM74=", "dev": true, "requires": {"@babel/helper-define-polyfill-provider": "^0.3.0"}}, "debug": {"version": "4.3.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/-/debug-4.3.3.tgz", "integrity": "sha1-BCZuC3CpjURi5uKI44JZITMytmQ=", "dev": true, "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "dev": true}, "semver": {"version": "6.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "dev": true}}}, "@babel/plugin-transform-shorthand-properties": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.14.5.tgz", "integrity": "sha1-l/E4VfFAkzjYyty6ymcK154JGlg=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-spread": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-spread/-/plugin-transform-spread-7.15.8.tgz", "integrity": "sha1-edWqJ/aNcARJstoHaR36MtL21Gg=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.15.4"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-sticky-regex": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.14.5.tgz", "integrity": "sha1-W2F1Qmdei3dhKUOB88KMYz9Arrk=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-template-literals": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.14.5.tgz", "integrity": "sha1-pfK8Izk32EU4hdxza92Nn/q/PZM=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-typeof-symbol": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.14.5.tgz", "integrity": "sha1-Oa8nOemJor0pG/a1PxaYFCPUV9Q=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-unicode-escapes": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.14.5.tgz", "integrity": "sha1-nUvSpoHjxdes9PV/qeURddkdDGs=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/plugin-transform-unicode-regex": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.14.5.tgz", "integrity": "sha1-TNCbbIQl3YElXHzrP7GDbnQUOC4=", "dev": true, "requires": {"@babel/helper-create-regexp-features-plugin": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5"}, "dependencies": {"@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}}}, "@babel/preset-env": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/preset-env/-/preset-env-7.15.8.tgz", "integrity": "sha1-9SfOW8sSHNGZ9rUCvyPkILP/jbo=", "dev": true, "requires": {"@babel/compat-data": "^7.15.0", "@babel/helper-compilation-targets": "^7.15.4", "@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-validator-option": "^7.14.5", "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "^7.15.4", "@babel/plugin-proposal-async-generator-functions": "^7.15.8", "@babel/plugin-proposal-class-properties": "^7.14.5", "@babel/plugin-proposal-class-static-block": "^7.15.4", "@babel/plugin-proposal-dynamic-import": "^7.14.5", "@babel/plugin-proposal-export-namespace-from": "^7.14.5", "@babel/plugin-proposal-json-strings": "^7.14.5", "@babel/plugin-proposal-logical-assignment-operators": "^7.14.5", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.14.5", "@babel/plugin-proposal-numeric-separator": "^7.14.5", "@babel/plugin-proposal-object-rest-spread": "^7.15.6", "@babel/plugin-proposal-optional-catch-binding": "^7.14.5", "@babel/plugin-proposal-optional-chaining": "^7.14.5", "@babel/plugin-proposal-private-methods": "^7.14.5", "@babel/plugin-proposal-private-property-in-object": "^7.15.4", "@babel/plugin-proposal-unicode-property-regex": "^7.14.5", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-export-namespace-from": "^7.8.3", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-top-level-await": "^7.14.5", "@babel/plugin-transform-arrow-functions": "^7.14.5", "@babel/plugin-transform-async-to-generator": "^7.14.5", "@babel/plugin-transform-block-scoped-functions": "^7.14.5", "@babel/plugin-transform-block-scoping": "^7.15.3", "@babel/plugin-transform-classes": "^7.15.4", "@babel/plugin-transform-computed-properties": "^7.14.5", "@babel/plugin-transform-destructuring": "^7.14.7", "@babel/plugin-transform-dotall-regex": "^7.14.5", "@babel/plugin-transform-duplicate-keys": "^7.14.5", "@babel/plugin-transform-exponentiation-operator": "^7.14.5", "@babel/plugin-transform-for-of": "^7.15.4", "@babel/plugin-transform-function-name": "^7.14.5", "@babel/plugin-transform-literals": "^7.14.5", "@babel/plugin-transform-member-expression-literals": "^7.14.5", "@babel/plugin-transform-modules-amd": "^7.14.5", "@babel/plugin-transform-modules-commonjs": "^7.15.4", "@babel/plugin-transform-modules-systemjs": "^7.15.4", "@babel/plugin-transform-modules-umd": "^7.14.5", "@babel/plugin-transform-named-capturing-groups-regex": "^7.14.9", "@babel/plugin-transform-new-target": "^7.14.5", "@babel/plugin-transform-object-super": "^7.14.5", "@babel/plugin-transform-parameters": "^7.15.4", "@babel/plugin-transform-property-literals": "^7.14.5", "@babel/plugin-transform-regenerator": "^7.14.5", "@babel/plugin-transform-reserved-words": "^7.14.5", "@babel/plugin-transform-shorthand-properties": "^7.14.5", "@babel/plugin-transform-spread": "^7.15.8", "@babel/plugin-transform-sticky-regex": "^7.14.5", "@babel/plugin-transform-template-literals": "^7.14.5", "@babel/plugin-transform-typeof-symbol": "^7.14.5", "@babel/plugin-transform-unicode-escapes": "^7.14.5", "@babel/plugin-transform-unicode-regex": "^7.14.5", "@babel/preset-modules": "^0.1.4", "@babel/types": "^7.15.6", "babel-plugin-polyfill-corejs2": "^0.2.2", "babel-plugin-polyfill-corejs3": "^0.2.5", "babel-plugin-polyfill-regenerator": "^0.2.2", "core-js-compat": "^3.16.0", "semver": "^6.3.0"}, "dependencies": {"@babel/code-frame": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/code-frame/-/code-frame-7.15.8.tgz", "integrity": "sha1-RZkMR62tsAwDZ3uqiSIffMI9JQM=", "dev": true, "requires": {"@babel/highlight": "^7.14.5"}}, "@babel/generator": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/generator/-/generator-7.15.8.tgz", "integrity": "sha1-+la+a1lpUs6yMQSM+E7kmaGcDNE=", "dev": true, "requires": {"@babel/types": "^7.15.6", "jsesc": "^2.5.1", "source-map": "^0.5.0"}}, "@babel/helper-create-class-features-plugin": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.15.4.tgz", "integrity": "sha1-f5d8F70SpfujY8sZvqCQOUvzfS4=", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.15.4", "@babel/helper-function-name": "^7.15.4", "@babel/helper-member-expression-to-functions": "^7.15.4", "@babel/helper-optimise-call-expression": "^7.15.4", "@babel/helper-replace-supers": "^7.15.4", "@babel/helper-split-export-declaration": "^7.15.4"}}, "@babel/helper-function-name": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-function-name/-/helper-function-name-7.15.4.tgz", "integrity": "sha1-hFdE2vxDgaSl+2r6bD02+Yp4frw=", "dev": true, "requires": {"@babel/helper-get-function-arity": "^7.15.4", "@babel/template": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/helper-get-function-arity": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-get-function-arity/-/helper-get-function-arity-7.15.4.tgz", "integrity": "sha1-CYgYk0oTf854tTaj4BWGS+Hih5s=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-member-expression-to-functions": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.15.4.tgz", "integrity": "sha1-v9NNybupgkpGWLAxfsL9VxpR5u8=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-optimise-call-expression": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.15.4.tgz", "integrity": "sha1-8xClEho7nMUtmrGRIr1ymCLe4XE=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}, "@babel/helper-replace-supers": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-replace-supers/-/helper-replace-supers-7.15.4.tgz", "integrity": "sha1-UqirJrqRjH9t7ihiiwcHGse3NHo=", "dev": true, "requires": {"@babel/helper-member-expression-to-functions": "^7.15.4", "@babel/helper-optimise-call-expression": "^7.15.4", "@babel/traverse": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/helper-split-export-declaration": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.15.4.tgz", "integrity": "sha1-rsq5Lc2+9qEKo7YqsgSwhfd24lc=", "dev": true, "requires": {"@babel/types": "^7.15.4"}}, "@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "dev": true}, "@babel/highlight": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/highlight/-/highlight-7.14.5.tgz", "integrity": "sha1-aGGlLwOWZAUAH2qlNKAaJNmejNk=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.5", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.15.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/parser/-/parser-7.15.8.tgz", "integrity": "sha1-e6zcvnG9w/+TbVEMFdzqfPC5kBY=", "dev": true}, "@babel/plugin-proposal-class-properties": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.14.5.tgz", "integrity": "sha1-QNHuFAxbHjGjUPT17tlFCWVZtC4=", "dev": true, "requires": {"@babel/helper-create-class-features-plugin": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/template": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/template/-/template-7.15.4.tgz", "integrity": "sha1-UYmNNdzz+qZwxO5q/P1RfuE58ZQ=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4"}}, "@babel/traverse": {"version": "7.15.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/traverse/-/traverse-7.15.4.tgz", "integrity": "sha1-/4UQNnoUS/v/VS2eGOKPPiiJwi0=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/generator": "^7.15.4", "@babel/helper-function-name": "^7.15.4", "@babel/helper-hoist-variables": "^7.15.4", "@babel/helper-split-export-declaration": "^7.15.4", "@babel/parser": "^7.15.4", "@babel/types": "^7.15.4", "debug": "^4.1.0", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.15.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.15.6.tgz", "integrity": "sha1-mavcSCGLKIHAWN0KerBbmcm+dY8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.9", "to-fast-properties": "^2.0.0"}}, "debug": {"version": "4.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/-/debug-4.3.2.tgz", "integrity": "sha1-8KScGKyHeeMdSgxgKd+3aHPHQos=", "dev": true, "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "dev": true}, "semver": {"version": "6.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "dev": true}}}, "@babel/preset-modules": {"version": "0.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/preset-modules/-/preset-modules-0.1.4.tgz", "integrity": "sha1-Ni8raMZihClw/bXiVP/I/BwuQV4=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4", "@babel/plugin-transform-dotall-regex": "^7.4.4", "@babel/types": "^7.4.4", "esutils": "^2.0.2"}}, "@babel/runtime": {"version": "7.16.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/runtime/-/runtime-7.16.5.tgz", "integrity": "sha1-fz40v4vbut8D+7ex6g2SlWnJSHo=", "requires": {"regenerator-runtime": "^0.13.4"}}, "@babel/template": {"version": "7.12.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/template/download/@babel/template-7.12.7.tgz", "integrity": "sha1-yBcjNpYBjjn7tsSR0vtoTgXtQ7w=", "dev": true, "requires": {"@babel/code-frame": "^7.10.4", "@babel/parser": "^7.12.7", "@babel/types": "^7.12.7"}}, "@babel/traverse": {"version": "7.12.12", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/traverse/download/@babel/traverse-7.12.12.tgz", "integrity": "sha1-0M2HiScE7djaAC1nS8gRzmR0M3Y=", "dev": true, "requires": {"@babel/code-frame": "^7.12.11", "@babel/generator": "^7.12.11", "@babel/helper-function-name": "^7.12.11", "@babel/helper-split-export-declaration": "^7.12.11", "@babel/parser": "^7.12.11", "@babel/types": "^7.12.12", "debug": "^4.1.0", "globals": "^11.1.0", "lodash": "^4.17.19"}, "dependencies": {"debug": {"version": "4.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/download/debug-4.3.1.tgz", "integrity": "sha1-8NIpxQXgxtjEmsVT0bE9wYP2su4=", "dev": true, "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/download/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "dev": true}}}, "@babel/types": {"version": "7.12.12", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/download/@babel/types-7.12.12.tgz", "integrity": "sha1-Rgim7DE6u9h6+lUATTc60EqWwpk=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.12.11", "lodash": "^4.17.19", "to-fast-properties": "^2.0.0"}}, "@cnakazawa/watch": {"version": "1.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@cnakazawa/watch/download/@cnakazawa/watch-1.0.4.tgz", "integrity": "sha1-+GSuhQBND8q29QvpFBxNo2jRZWo=", "dev": true, "requires": {"exec-sh": "^0.3.2", "minimist": "^1.2.0"}}, "@jest/console": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/console/download/@jest/console-24.9.0.tgz", "integrity": "sha1-ebG8Bvt0qM+wHL3t+UVYSxuXB/A=", "dev": true, "requires": {"@jest/source-map": "^24.9.0", "chalk": "^2.0.1", "slash": "^2.0.0"}}, "@jest/core": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/core/download/@jest/core-24.9.0.tgz", "integrity": "sha1-LOzNC5MYH5xIUOdPKprUPTUTacQ=", "dev": true, "requires": {"@jest/console": "^24.7.1", "@jest/reporters": "^24.9.0", "@jest/test-result": "^24.9.0", "@jest/transform": "^24.9.0", "@jest/types": "^24.9.0", "ansi-escapes": "^3.0.0", "chalk": "^2.0.1", "exit": "^0.1.2", "graceful-fs": "^4.1.15", "jest-changed-files": "^24.9.0", "jest-config": "^24.9.0", "jest-haste-map": "^24.9.0", "jest-message-util": "^24.9.0", "jest-regex-util": "^24.3.0", "jest-resolve": "^24.9.0", "jest-resolve-dependencies": "^24.9.0", "jest-runner": "^24.9.0", "jest-runtime": "^24.9.0", "jest-snapshot": "^24.9.0", "jest-util": "^24.9.0", "jest-validate": "^24.9.0", "jest-watcher": "^24.9.0", "micromatch": "^3.1.10", "p-each-series": "^1.0.0", "realpath-native": "^1.1.0", "rimraf": "^2.5.4", "slash": "^2.0.0", "strip-ansi": "^5.0.0"}}, "@jest/environment": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/environment/download/@jest/environment-24.9.0.tgz", "integrity": "sha1-IeOvotZcBYbL1svv4gi6+t5Eqxg=", "dev": true, "requires": {"@jest/fake-timers": "^24.9.0", "@jest/transform": "^24.9.0", "@jest/types": "^24.9.0", "jest-mock": "^24.9.0"}}, "@jest/fake-timers": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/fake-timers/download/@jest/fake-timers-24.9.0.tgz", "integrity": "sha1-uj5r8O7NCaY2BJiWQ00wZjZUDJM=", "dev": true, "requires": {"@jest/types": "^24.9.0", "jest-message-util": "^24.9.0", "jest-mock": "^24.9.0"}}, "@jest/reporters": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/reporters/download/@jest/reporters-24.9.0.tgz", "integrity": "sha1-hmYO/44rlmHQQqjpigKLjWMaW0M=", "dev": true, "requires": {"@jest/environment": "^24.9.0", "@jest/test-result": "^24.9.0", "@jest/transform": "^24.9.0", "@jest/types": "^24.9.0", "chalk": "^2.0.1", "exit": "^0.1.2", "glob": "^7.1.2", "istanbul-lib-coverage": "^2.0.2", "istanbul-lib-instrument": "^3.0.1", "istanbul-lib-report": "^2.0.4", "istanbul-lib-source-maps": "^3.0.1", "istanbul-reports": "^2.2.6", "jest-haste-map": "^24.9.0", "jest-resolve": "^24.9.0", "jest-runtime": "^24.9.0", "jest-util": "^24.9.0", "jest-worker": "^24.6.0", "node-notifier": "^5.4.2", "slash": "^2.0.0", "source-map": "^0.6.0", "string-length": "^2.0.0"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "@jest/source-map": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/source-map/download/@jest/source-map-24.9.0.tgz", "integrity": "sha1-DiY6lEML5LQdpoPMwea//ioZFxQ=", "dev": true, "requires": {"callsites": "^3.0.0", "graceful-fs": "^4.1.15", "source-map": "^0.6.0"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "@jest/test-result": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/test-result/download/@jest/test-result-24.9.0.tgz", "integrity": "sha1-EXluiqnb+I6gJXV7MVJZWtBroMo=", "dev": true, "requires": {"@jest/console": "^24.9.0", "@jest/types": "^24.9.0", "@types/istanbul-lib-coverage": "^2.0.0"}}, "@jest/test-sequencer": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/test-sequencer/download/@jest/test-sequencer-24.9.0.tgz", "integrity": "sha1-+PM081tiWk8vNV8v5+YDba0uazE=", "dev": true, "requires": {"@jest/test-result": "^24.9.0", "jest-haste-map": "^24.9.0", "jest-runner": "^24.9.0", "jest-runtime": "^24.9.0"}}, "@jest/transform": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/transform/download/@jest/transform-24.9.0.tgz", "integrity": "sha1-SuJ2iyllU/rasJ6ewRlUPJCxbFY=", "dev": true, "requires": {"@babel/core": "^7.1.0", "@jest/types": "^24.9.0", "babel-plugin-istanbul": "^5.1.0", "chalk": "^2.0.1", "convert-source-map": "^1.4.0", "fast-json-stable-stringify": "^2.0.0", "graceful-fs": "^4.1.15", "jest-haste-map": "^24.9.0", "jest-regex-util": "^24.9.0", "jest-util": "^24.9.0", "micromatch": "^3.1.10", "pirates": "^4.0.1", "realpath-native": "^1.1.0", "slash": "^2.0.0", "source-map": "^0.6.1", "write-file-atomic": "2.4.1"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "@jest/types": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@jest/types/download/@jest/types-24.9.0.tgz", "integrity": "sha1-Y8smy3UA0Gnlo4lEGnxqtekJ/Fk=", "dev": true, "requires": {"@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^1.1.1", "@types/yargs": "^13.0.0"}}, "@types/babel__core": {"version": "7.1.12", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/babel__core/download/@types/babel__core-7.1.12.tgz", "integrity": "sha1-TY6eUesmVVKn5PH/IhmrYTO9+y0=", "dev": true, "requires": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "@types/babel__generator": {"version": "7.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/babel__generator/download/@types/babel__generator-7.6.2.tgz", "integrity": "sha1-89cReOGHhY98ReMDgPjxt0FaEtg=", "dev": true, "requires": {"@babel/types": "^7.0.0"}}, "@types/babel__template": {"version": "7.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/babel__template/download/@types/babel__template-7.4.0.tgz", "integrity": "sha1-DIiN1ws+6e67bk8gDoCdoAdiYr4=", "dev": true, "requires": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "@types/babel__traverse": {"version": "7.11.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/babel__traverse/download/@types/babel__traverse-7.11.0.tgz", "integrity": "sha1-uaHvpjUgG6m8hQMjqHk+4tNsBKA=", "dev": true, "requires": {"@babel/types": "^7.3.0"}}, "@types/istanbul-lib-coverage": {"version": "2.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.3.tgz", "integrity": "sha1-S6jdtyAiH0MuRDvV+RF/0iz9R2I=", "dev": true}, "@types/istanbul-lib-report": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/istanbul-lib-report/download/@types/istanbul-lib-report-3.0.0.tgz", "integrity": "sha1-wUwk8Y6oGQwRjudWK3/5mjZVJoY=", "dev": true, "requires": {"@types/istanbul-lib-coverage": "*"}}, "@types/istanbul-reports": {"version": "1.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/istanbul-reports/download/@types/istanbul-reports-1.1.2.tgz", "integrity": "sha1-6HXMaJ5HvOVJ7IHz315vbxHPrrI=", "dev": true, "requires": {"@types/istanbul-lib-coverage": "*", "@types/istanbul-lib-report": "*"}}, "@types/json-schema": {"version": "7.0.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/json-schema/download/@types/json-schema-7.0.6.tgz", "integrity": "sha1-9MfsQ+gbMZqYFRFQMXCfJph4kfA=", "dev": true}, "@types/stack-utils": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/stack-utils/download/@types/stack-utils-1.0.1.tgz", "integrity": "sha1-CoUdO9lkmPolwzq3J47TvWXwbD4=", "dev": true}, "@types/yargs": {"version": "13.0.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/yargs/download/@types/yargs-13.0.11.tgz", "integrity": "sha1-3vLwyT5L3yxh1+NImbF+NL4o07E=", "dev": true, "requires": {"@types/yargs-parser": "*"}}, "@types/yargs-parser": {"version": "20.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/yargs-parser/download/@types/yargs-parser-20.2.0.tgz", "integrity": "sha1-3T5mmboyN/A0jNCF5GmHgCBIQvk=", "dev": true}, "@webassemblyjs/ast": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/ast/download/@webassemblyjs/ast-1.9.0.tgz", "integrity": "sha1-vYUGBLQEJFmlpBzX0zjL7Wle2WQ=", "dev": true, "requires": {"@webassemblyjs/helper-module-context": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/wast-parser": "1.9.0"}}, "@webassemblyjs/floating-point-hex-parser": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.9.0.tgz", "integrity": "sha1-PD07Jxvd/ITesA9xNEQ4MR1S/7Q=", "dev": true}, "@webassemblyjs/helper-api-error": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.9.0.tgz", "integrity": "sha1-ID9nbjM7lsnaLuqzzO8zxFkotqI=", "dev": true}, "@webassemblyjs/helper-buffer": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.9.0.tgz", "integrity": "sha1-oUQtJpxf6yP8vJ73WdrDVH8p3gA=", "dev": true}, "@webassemblyjs/helper-code-frame": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/helper-code-frame/download/@webassemblyjs/helper-code-frame-1.9.0.tgz", "integrity": "sha1-ZH+Iks0gQ6gqwMjF51w28dkVnyc=", "dev": true, "requires": {"@webassemblyjs/wast-printer": "1.9.0"}}, "@webassemblyjs/helper-fsm": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/helper-fsm/download/@webassemblyjs/helper-fsm-1.9.0.tgz", "integrity": "sha1-wFJWtxJEIUZx9LCOwQitY7cO3bg=", "dev": true}, "@webassemblyjs/helper-module-context": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/helper-module-context/download/@webassemblyjs/helper-module-context-1.9.0.tgz", "integrity": "sha1-JdiIS3aDmHGgimxvgGw5ee9xLwc=", "dev": true, "requires": {"@webassemblyjs/ast": "1.9.0"}}, "@webassemblyjs/helper-wasm-bytecode": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.9.0.tgz", "integrity": "sha1-T+2L6sm4wU+MWLcNEk1UndH+V5A=", "dev": true}, "@webassemblyjs/helper-wasm-section": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.9.0.tgz", "integrity": "sha1-WkE41aYpK6GLBMWuSXF+QWeWU0Y=", "dev": true, "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-buffer": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/wasm-gen": "1.9.0"}}, "@webassemblyjs/ieee754": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.9.0.tgz", "integrity": "sha1-Fceg+6roP7JhQ7us9tbfFwKtOeQ=", "dev": true, "requires": {"@xtuc/ieee754": "^1.2.0"}}, "@webassemblyjs/leb128": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.9.0.tgz", "integrity": "sha1-8Zygt2ptxVYjoJz/p2noOPoeHJU=", "dev": true, "requires": {"@xtuc/long": "4.2.2"}}, "@webassemblyjs/utf8": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.9.0.tgz", "integrity": "sha1-BNM7Y2945qaBMifoJAL3Y3tiKas=", "dev": true}, "@webassemblyjs/wasm-edit": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.9.0.tgz", "integrity": "sha1-P+bXnT8PkiGDqoYALELdJWz+6c8=", "dev": true, "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-buffer": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/helper-wasm-section": "1.9.0", "@webassemblyjs/wasm-gen": "1.9.0", "@webassemblyjs/wasm-opt": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0", "@webassemblyjs/wast-printer": "1.9.0"}}, "@webassemblyjs/wasm-gen": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.9.0.tgz", "integrity": "sha1-ULxw7Gje2OJ2OwGhQYv0NJGnpJw=", "dev": true, "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/ieee754": "1.9.0", "@webassemblyjs/leb128": "1.9.0", "@webassemblyjs/utf8": "1.9.0"}}, "@webassemblyjs/wasm-opt": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.9.0.tgz", "integrity": "sha1-IhEYHlsxMmRDzIES658LkChyGmE=", "dev": true, "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-buffer": "1.9.0", "@webassemblyjs/wasm-gen": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0"}}, "@webassemblyjs/wasm-parser": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.9.0.tgz", "integrity": "sha1-nUjkSCbfSmWYKUqmyHRp1kL/9l4=", "dev": true, "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-api-error": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/ieee754": "1.9.0", "@webassemblyjs/leb128": "1.9.0", "@webassemblyjs/utf8": "1.9.0"}}, "@webassemblyjs/wast-parser": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/wast-parser/download/@webassemblyjs/wast-parser-1.9.0.tgz", "integrity": "sha1-MDERXXmsW9JhVWzsw/qQo+9FGRQ=", "dev": true, "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/floating-point-hex-parser": "1.9.0", "@webassemblyjs/helper-api-error": "1.9.0", "@webassemblyjs/helper-code-frame": "1.9.0", "@webassemblyjs/helper-fsm": "1.9.0", "@xtuc/long": "4.2.2"}}, "@webassemblyjs/wast-printer": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.9.0.tgz", "integrity": "sha1-STXVTIX+9jewDOn1I3dFHQDUeJk=", "dev": true, "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/wast-parser": "1.9.0", "@xtuc/long": "4.2.2"}}, "@xtuc/ieee754": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz", "integrity": "sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=", "dev": true}, "@xtuc/long": {"version": "4.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@xtuc/long/download/@xtuc/long-4.2.2.tgz", "integrity": "sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=", "dev": true}, "abab": {"version": "2.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/abab/download/abab-2.0.5.tgz", "integrity": "sha1-wLZ4+zLWD8EhnHhNaoJv44Wut5o=", "dev": true}, "acorn": {"version": "5.7.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/acorn/download/acorn-5.7.4.tgz", "integrity": "sha1-Po2KmUfQWZoXltECJddDL0pKz14=", "dev": true}, "acorn-globals": {"version": "4.3.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/acorn-globals/download/acorn-globals-4.3.4.tgz", "integrity": "sha1-n6GSat3BHJcwjE5m163Q1Awycuc=", "dev": true, "requires": {"acorn": "^6.0.1", "acorn-walk": "^6.0.1"}, "dependencies": {"acorn": {"version": "6.4.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/acorn/download/acorn-6.4.2.tgz", "integrity": "sha1-NYZv1xBSjpLeEM8GAWSY5H454eY=", "dev": true}}}, "acorn-walk": {"version": "6.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/acorn-walk/download/acorn-walk-6.2.0.tgz", "integrity": "sha1-Ejy487hMIXHx9/slJhWxx4prGow=", "dev": true}, "ajv": {"version": "6.12.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ajv/download/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "dev": true, "requires": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ajv-errors": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ajv-errors/download/ajv-errors-1.0.1.tgz", "integrity": "sha1-81mGrOuRr63sQQL72FAUlQzvpk0=", "dev": true}, "ajv-keywords": {"version": "3.5.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ajv-keywords/download/ajv-keywords-3.5.2.tgz", "integrity": "sha1-MfKdpatuANHC0yms97WSlhTVAU0=", "dev": true}, "ansi-escapes": {"version": "3.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-escapes/download/ansi-escapes-3.2.0.tgz", "integrity": "sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=", "dev": true}, "ansi-regex": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-regex/download/ansi-regex-4.1.0.tgz", "integrity": "sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc=", "dev": true}, "ansi-styles": {"version": "3.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "anymatch": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/anymatch/download/anymatch-2.0.0.tgz", "integrity": "sha1-vLJLTzeTTZqnrBe0ra+J58du8us=", "dev": true, "requires": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}, "dependencies": {"normalize-path": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/normalize-path/download/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "dev": true, "requires": {"remove-trailing-separator": "^1.0.1"}}}}, "aproba": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/aproba/download/aproba-1.2.0.tgz", "integrity": "sha1-aALmJk79GMeQobDVF/DyYnvyyUo=", "dev": true}, "arr-diff": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/arr-diff/download/arr-diff-4.0.0.tgz", "integrity": "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=", "dev": true}, "arr-flatten": {"version": "1.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/arr-flatten/download/arr-flatten-1.1.0.tgz", "integrity": "sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=", "dev": true}, "arr-union": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/arr-union/download/arr-union-3.1.0.tgz", "integrity": "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=", "dev": true}, "array-equal": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/array-equal/download/array-equal-1.0.0.tgz", "integrity": "sha1-jCpe8kcv2ep0KwTHenUJO6J1fJM=", "dev": true}, "array-unique": {"version": "0.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/array-unique/download/array-unique-0.3.2.tgz", "integrity": "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=", "dev": true}, "asn1": {"version": "0.2.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/asn1/download/asn1-0.2.4.tgz", "integrity": "sha1-jSR136tVO7M+d7VOWeiAu4ziMTY=", "dev": true, "requires": {"safer-buffer": "~2.1.0"}}, "asn1.js": {"version": "5.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/asn1.js/download/asn1.js-5.4.1.tgz", "integrity": "sha1-EamAuE67kXgc41sP3C7ilON4Pwc=", "dev": true, "requires": {"bn.js": "^4.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0", "safer-buffer": "^2.1.0"}, "dependencies": {"bn.js": {"version": "4.11.9", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bn.js/download/bn.js-4.11.9.tgz", "integrity": "sha1-JtVWgpRY+dHoH8SJUkk9C6NQeCg=", "dev": true}}}, "assert": {"version": "1.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/assert/download/assert-1.5.0.tgz", "integrity": "sha1-VcEJqvbgrv2z3EtxJAxwv1dLGOs=", "dev": true, "requires": {"object-assign": "^4.1.1", "util": "0.10.3"}, "dependencies": {"inherits": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/inherits/download/inherits-2.0.1.tgz", "integrity": "sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE=", "dev": true}, "util": {"version": "0.10.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/util/download/util-0.10.3.tgz", "integrity": "sha1-evsa/lCAUkZInj23/g7TeTNqwPk=", "dev": true, "requires": {"inherits": "2.0.1"}}}}, "assert-plus": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/assert-plus/download/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=", "dev": true}, "assign-symbols": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/assign-symbols/download/assign-symbols-1.0.0.tgz", "integrity": "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=", "dev": true}, "astral-regex": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/astral-regex/download/astral-regex-1.0.0.tgz", "integrity": "sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=", "dev": true}, "async-each": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/async-each/download/async-each-1.0.3.tgz", "integrity": "sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8=", "dev": true, "optional": true}, "async-limiter": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/async-limiter/download/async-limiter-1.0.1.tgz", "integrity": "sha1-3TeelPDbgxCwgpH51kwyCXZmF/0=", "dev": true}, "asynckit": {"version": "0.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/asynckit/download/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k=", "dev": true}, "atob": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/atob/download/atob-2.1.2.tgz", "integrity": "sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=", "dev": true}, "aws-sign2": {"version": "0.7.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/aws-sign2/download/aws-sign2-0.7.0.tgz", "integrity": "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=", "dev": true}, "aws4": {"version": "1.11.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/aws4/download/aws4-1.11.0.tgz", "integrity": "sha1-1h9G2DslGSUOJ4Ta9bCUeai0HFk=", "dev": true}, "axios": {"version": "0.21.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/axios/download/axios-0.21.1.tgz", "integrity": "sha1-IlY0gZYvTWvemnbVFu8OXTwJsrg=", "requires": {"follow-redirects": "^1.10.0"}}, "babel-code-frame": {"version": "6.26.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-code-frame/download/babel-code-frame-6.26.0.tgz", "integrity": "sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=", "dev": true, "requires": {"chalk": "^1.1.3", "esutils": "^2.0.2", "js-tokens": "^3.0.2"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-regex/download/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true}, "ansi-styles": {"version": "2.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/download/ansi-styles-2.2.1.tgz", "integrity": "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=", "dev": true}, "chalk": {"version": "1.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-1.1.3.tgz", "integrity": "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=", "dev": true, "requires": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}}, "js-tokens": {"version": "3.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/js-tokens/download/js-tokens-3.0.2.tgz", "integrity": "sha1-mGbfOVECEw449/mWvOtlRDIJwls=", "dev": true}, "strip-ansi": {"version": "3.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/strip-ansi/download/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "requires": {"ansi-regex": "^2.0.0"}}, "supports-color": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-2.0.0.tgz", "integrity": "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=", "dev": true}}}, "babel-helper-evaluate-path": {"version": "0.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-helper-evaluate-path/download/babel-helper-evaluate-path-0.5.0.tgz", "integrity": "sha1-pi+pxOZP9+pc6pNTF07wI6kApnw=", "dev": true}, "babel-helper-flip-expressions": {"version": "0.4.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-helper-flip-expressions/download/babel-helper-flip-expressions-0.4.3.tgz", "integrity": "sha1-NpZzahKKwYvCUlS19AoizrPB0/0=", "dev": true}, "babel-helper-is-nodes-equiv": {"version": "0.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-helper-is-nodes-equiv/download/babel-helper-is-nodes-equiv-0.0.1.tgz", "integrity": "sha1-NOmzALFHnd2Y7HfqC76TQt/jloQ=", "dev": true}, "babel-helper-is-void-0": {"version": "0.4.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-helper-is-void-0/download/babel-helper-is-void-0-0.4.3.tgz", "integrity": "sha1-fZwBtFYee5Xb2g9u7kj1tg5nMT4=", "dev": true}, "babel-helper-mark-eval-scopes": {"version": "0.4.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-helper-mark-eval-scopes/download/babel-helper-mark-eval-scopes-0.4.3.tgz", "integrity": "sha1-0kSjvvmESHJgP/tG4izorN9VFWI=", "dev": true}, "babel-helper-remove-or-void": {"version": "0.4.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-helper-remove-or-void/download/babel-helper-remove-or-void-0.4.3.tgz", "integrity": "sha1-pPA7QAd6D/6I5F0HAQ3uJB/1rmA=", "dev": true}, "babel-helper-to-multiple-sequence-expressions": {"version": "0.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-helper-to-multiple-sequence-expressions/download/babel-helper-to-multiple-sequence-expressions-0.5.0.tgz", "integrity": "sha1-o/kk41YYgtQvz0iQeqmPeXmkWI0=", "dev": true}, "babel-jest": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-jest/download/babel-jest-24.9.0.tgz", "integrity": "sha1-P8Mny4RnuJ0U17xw4xUQSng8zVQ=", "dev": true, "requires": {"@jest/transform": "^24.9.0", "@jest/types": "^24.9.0", "@types/babel__core": "^7.1.0", "babel-plugin-istanbul": "^5.1.0", "babel-preset-jest": "^24.9.0", "chalk": "^2.4.2", "slash": "^2.0.0"}}, "babel-loader": {"version": "8.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-loader/download/babel-loader-8.2.2.tgz", "integrity": "sha1-k2POhMEMmkDmx1N0jhRBtgyKC4E=", "dev": true, "requires": {"find-cache-dir": "^3.3.1", "loader-utils": "^1.4.0", "make-dir": "^3.1.0", "schema-utils": "^2.6.5"}, "dependencies": {"make-dir": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/make-dir/download/make-dir-3.1.0.tgz", "integrity": "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=", "dev": true, "requires": {"semver": "^6.0.0"}}, "semver": {"version": "6.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/download/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "dev": true}}}, "babel-plugin-dynamic-import-node": {"version": "2.3.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.3.tgz", "integrity": "sha1-hP2hnJduxcbe/vV/lCez3vZuF6M=", "dev": true, "requires": {"object.assign": "^4.1.0"}}, "babel-plugin-istanbul": {"version": "5.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-istanbul/download/babel-plugin-istanbul-5.2.0.tgz", "integrity": "sha1-30reg9iXqS3wacTZolzyZxKTyFQ=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "find-up": "^3.0.0", "istanbul-lib-instrument": "^3.3.0", "test-exclude": "^5.2.3"}, "dependencies": {"find-up": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/find-up/download/find-up-3.0.0.tgz", "integrity": "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=", "dev": true, "requires": {"locate-path": "^3.0.0"}}, "locate-path": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/locate-path/download/locate-path-3.0.0.tgz", "integrity": "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=", "dev": true, "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "p-locate": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-locate/download/p-locate-3.0.0.tgz", "integrity": "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=", "dev": true, "requires": {"p-limit": "^2.0.0"}}, "path-exists": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-exists/download/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "dev": true}}}, "babel-plugin-jest-hoist": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-24.9.0.tgz", "integrity": "sha1-T4NwketAfgFEfIhDy+xUbQAC11Y=", "dev": true, "requires": {"@types/babel__traverse": "^7.0.6"}}, "babel-plugin-minify-builtins": {"version": "0.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-minify-builtins/download/babel-plugin-minify-builtins-0.5.0.tgz", "integrity": "sha1-MeuC7RoNDv3DExL5O25HQc6Cw2s=", "dev": true}, "babel-plugin-minify-constant-folding": {"version": "0.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-minify-constant-folding/download/babel-plugin-minify-constant-folding-0.5.0.tgz", "integrity": "sha1-+EvI2/alYeXjUP+VriFrCtVRW24=", "dev": true, "requires": {"babel-helper-evaluate-path": "^0.5.0"}}, "babel-plugin-minify-dead-code-elimination": {"version": "0.5.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-minify-dead-code-elimination/download/babel-plugin-minify-dead-code-elimination-0.5.1.tgz", "integrity": "sha1-Ggxo5EvjDeSXbKaf/FNeCL4TaD8=", "dev": true, "requires": {"babel-helper-evaluate-path": "^0.5.0", "babel-helper-mark-eval-scopes": "^0.4.3", "babel-helper-remove-or-void": "^0.4.3", "lodash": "^4.17.11"}}, "babel-plugin-minify-flip-comparisons": {"version": "0.4.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-minify-flip-comparisons/download/babel-plugin-minify-flip-comparisons-0.4.3.tgz", "integrity": "sha1-AMqHDLjxO0XAOLPB68DyJyk8llo=", "dev": true, "requires": {"babel-helper-is-void-0": "^0.4.3"}}, "babel-plugin-minify-guarded-expressions": {"version": "0.4.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-minify-guarded-expressions/download/babel-plugin-minify-guarded-expressions-0.4.4.tgz", "integrity": "sha1-gYlg9kzAiu6dbHW+xtqXTE1iETU=", "dev": true, "requires": {"babel-helper-evaluate-path": "^0.5.0", "babel-helper-flip-expressions": "^0.4.3"}}, "babel-plugin-minify-infinity": {"version": "0.4.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-minify-infinity/download/babel-plugin-minify-infinity-0.4.3.tgz", "integrity": "sha1-37h2obCKBldjhO8/kuZTumB7Oco=", "dev": true}, "babel-plugin-minify-mangle-names": {"version": "0.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-minify-mangle-names/download/babel-plugin-minify-mangle-names-0.5.0.tgz", "integrity": "sha1-vN21B8kdLJnhOL1rF6GcPCceP9M=", "dev": true, "requires": {"babel-helper-mark-eval-scopes": "^0.4.3"}}, "babel-plugin-minify-numeric-literals": {"version": "0.4.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-minify-numeric-literals/download/babel-plugin-minify-numeric-literals-0.4.3.tgz", "integrity": "sha1-jk/VYcefeAEob/YOjF/Z3u6TwLw=", "dev": true}, "babel-plugin-minify-replace": {"version": "0.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-minify-replace/download/babel-plugin-minify-replace-0.5.0.tgz", "integrity": "sha1-0+LJlGyQlsBw78lnYc4ojsXD9xw=", "dev": true}, "babel-plugin-minify-simplify": {"version": "0.5.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-minify-simplify/download/babel-plugin-minify-simplify-0.5.1.tgz", "integrity": "sha1-8hYTyLla80UKLKcVAv29kXk8jWo=", "dev": true, "requires": {"babel-helper-evaluate-path": "^0.5.0", "babel-helper-flip-expressions": "^0.4.3", "babel-helper-is-nodes-equiv": "^0.0.1", "babel-helper-to-multiple-sequence-expressions": "^0.5.0"}}, "babel-plugin-minify-type-constructors": {"version": "0.4.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-minify-type-constructors/download/babel-plugin-minify-type-constructors-0.4.3.tgz", "integrity": "sha1-G8bxW4f3qxCF1CszC3F2V6IVZQA=", "dev": true, "requires": {"babel-helper-is-void-0": "^0.4.3"}}, "babel-plugin-polyfill-corejs2": {"version": "0.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.2.2.tgz", "integrity": "sha1-6R<PERSON>Hheb9lPlLYYp5VOVpMFO/Uyc=", "dev": true, "requires": {"@babel/compat-data": "^7.13.11", "@babel/helper-define-polyfill-provider": "^0.2.2", "semver": "^6.1.1"}, "dependencies": {"semver": {"version": "6.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "dev": true}}}, "babel-plugin-polyfill-corejs3": {"version": "0.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.2.5.tgz", "integrity": "sha1-J3mEahahZSJEriaLHpBq2hB/r5I=", "dev": true, "requires": {"@babel/helper-define-polyfill-provider": "^0.2.2", "core-js-compat": "^3.16.2"}}, "babel-plugin-polyfill-regenerator": {"version": "0.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.2.2.tgz", "integrity": "sha1-sxDI1kKsraNIwfo7Pmzg6FG+4Hc=", "dev": true, "requires": {"@babel/helper-define-polyfill-provider": "^0.2.2"}}, "babel-plugin-transform-inline-consecutive-adds": {"version": "0.4.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-transform-inline-consecutive-adds/download/babel-plugin-transform-inline-consecutive-adds-0.4.3.tgz", "integrity": "sha1-Mj1Ho+pjqDp6w8gRro5pQfrysNE=", "dev": true}, "babel-plugin-transform-member-expression-literals": {"version": "6.9.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-transform-member-expression-literals/download/babel-plugin-transform-member-expression-literals-6.9.4.tgz", "integrity": "sha1-NwOcmgwzE6OUlfqsL/OmtbnQOL8=", "dev": true}, "babel-plugin-transform-merge-sibling-variables": {"version": "6.9.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-transform-merge-sibling-variables/download/babel-plugin-transform-merge-sibling-variables-6.9.4.tgz", "integrity": "sha1-hbQi/DN3tEnJ0c3kQIcgNTJAHa4=", "dev": true}, "babel-plugin-transform-minify-booleans": {"version": "6.9.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-transform-minify-booleans/download/babel-plugin-transform-minify-booleans-6.9.4.tgz", "integrity": "sha1-rLs+VqNVXdI5KOS1gtKFFi3SsZg=", "dev": true}, "babel-plugin-transform-property-literals": {"version": "6.9.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-transform-property-literals/download/babel-plugin-transform-property-literals-6.9.4.tgz", "integrity": "sha1-mMHSHiVXNlc/k+zlRFn2ziSYXTk=", "dev": true, "requires": {"esutils": "^2.0.2"}}, "babel-plugin-transform-regexp-constructors": {"version": "0.4.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-transform-regexp-constructors/download/babel-plugin-transform-regexp-constructors-0.4.3.tgz", "integrity": "sha1-WLd3W2OvzzMyj66aX4j71PsLSWU=", "dev": true}, "babel-plugin-transform-remove-console": {"version": "6.9.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-transform-remove-console/download/babel-plugin-transform-remove-console-6.9.4.tgz", "integrity": "sha1-uYA2DAZzhOJLNXpYjYB9PINSd4A=", "dev": true}, "babel-plugin-transform-remove-debugger": {"version": "6.9.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-transform-remove-debugger/download/babel-plugin-transform-remove-debugger-6.9.4.tgz", "integrity": "sha1-QrcnYxyXl44estGZp67IShgznvI=", "dev": true}, "babel-plugin-transform-remove-undefined": {"version": "0.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-transform-remove-undefined/download/babel-plugin-transform-remove-undefined-0.5.0.tgz", "integrity": "sha1-gCCLMSJXZsYwyX+i0oiVIFbqIt0=", "dev": true, "requires": {"babel-helper-evaluate-path": "^0.5.0"}}, "babel-plugin-transform-simplify-comparison-operators": {"version": "6.9.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-transform-simplify-comparison-operators/download/babel-plugin-transform-simplify-comparison-operators-6.9.4.tgz", "integrity": "sha1-9ir+CWyrDh9ootdT/fKDiIRxzrk=", "dev": true}, "babel-plugin-transform-undefined-to-void": {"version": "6.9.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-transform-undefined-to-void/download/babel-plugin-transform-undefined-to-void-6.9.4.tgz", "integrity": "sha1-viQcqBQEAwZ4t0hxcyK4nQyP4oA=", "dev": true}, "babel-preset-jest": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-preset-jest/download/babel-preset-jest-24.9.0.tgz", "integrity": "sha1-GStSHiIX+x0fZ89z9wwzZlCtPNw=", "dev": true, "requires": {"@babel/plugin-syntax-object-rest-spread": "^7.0.0", "babel-plugin-jest-hoist": "^24.9.0"}}, "babel-preset-minify": {"version": "0.5.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-preset-minify/download/babel-preset-minify-0.5.1.tgz", "integrity": "sha1-JfXQvONuyBi+gDONDllBBuIeqp8=", "dev": true, "requires": {"babel-plugin-minify-builtins": "^0.5.0", "babel-plugin-minify-constant-folding": "^0.5.0", "babel-plugin-minify-dead-code-elimination": "^0.5.1", "babel-plugin-minify-flip-comparisons": "^0.4.3", "babel-plugin-minify-guarded-expressions": "^0.4.4", "babel-plugin-minify-infinity": "^0.4.3", "babel-plugin-minify-mangle-names": "^0.5.0", "babel-plugin-minify-numeric-literals": "^0.4.3", "babel-plugin-minify-replace": "^0.5.0", "babel-plugin-minify-simplify": "^0.5.1", "babel-plugin-minify-type-constructors": "^0.4.3", "babel-plugin-transform-inline-consecutive-adds": "^0.4.3", "babel-plugin-transform-member-expression-literals": "^6.9.4", "babel-plugin-transform-merge-sibling-variables": "^6.9.4", "babel-plugin-transform-minify-booleans": "^6.9.4", "babel-plugin-transform-property-literals": "^6.9.4", "babel-plugin-transform-regexp-constructors": "^0.4.3", "babel-plugin-transform-remove-console": "^6.9.4", "babel-plugin-transform-remove-debugger": "^6.9.4", "babel-plugin-transform-remove-undefined": "^0.5.0", "babel-plugin-transform-simplify-comparison-operators": "^6.9.4", "babel-plugin-transform-undefined-to-void": "^6.9.4", "lodash": "^4.17.11"}}, "balanced-match": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/balanced-match/download/balanced-match-1.0.0.tgz", "integrity": "sha1-ibTRmasr7kneFk6gK4nORi1xt2c=", "dev": true}, "base": {"version": "0.11.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/base/download/base-0.11.2.tgz", "integrity": "sha1-e95c7RRbbVUakNuH+DxVi060io8=", "dev": true, "requires": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/download/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-descriptor/download/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "base64-js": {"version": "1.5.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/base64-js/download/base64-js-1.5.1.tgz", "integrity": "sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=", "dev": true}, "bcrypt-pbkdf": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=", "dev": true, "requires": {"tweetnacl": "^0.14.3"}}, "big.js": {"version": "5.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/big.js/download/big.js-5.2.2.tgz", "integrity": "sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=", "dev": true}, "binary-extensions": {"version": "1.13.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/binary-extensions/download/binary-extensions-1.13.1.tgz", "integrity": "sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=", "dev": true, "optional": true}, "bindings": {"version": "1.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bindings/download/bindings-1.5.0.tgz", "integrity": "sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=", "dev": true, "optional": true, "requires": {"file-uri-to-path": "1.0.0"}}, "bluebird": {"version": "3.7.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bluebird/download/bluebird-3.7.2.tgz", "integrity": "sha1-nyKcFb4nJFT/qXOs4NvueaGww28=", "dev": true}, "bn.js": {"version": "5.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bn.js/download/bn.js-5.1.3.tgz", "integrity": "sha1-vsoAVAj2Quvr6oCwQrTRjSrA7ms=", "dev": true}, "brace-expansion": {"version": "1.1.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/brace-expansion/download/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "2.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/braces/download/braces-2.3.2.tgz", "integrity": "sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=", "dev": true, "requires": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "brorand": {"version": "1.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/brorand/download/brorand-1.1.0.tgz", "integrity": "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=", "dev": true}, "browser-process-hrtime": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/browser-process-hrtime/download/browser-process-hrtime-1.0.0.tgz", "integrity": "sha1-PJtLfXgsgSHlbxAQbYTA0P/JRiY=", "dev": true}, "browser-resolve": {"version": "1.11.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/browser-resolve/download/browser-resolve-1.11.3.tgz", "integrity": "sha1-m3y7PQ9RDky4a9vXlhJNKLWJCvY=", "dev": true, "requires": {"resolve": "1.1.7"}, "dependencies": {"resolve": {"version": "1.1.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/resolve/download/resolve-1.1.7.tgz", "integrity": "sha1-IDEU2CrSxe2ejgQRs5ModeiJ6Xs=", "dev": true}}}, "browserify-aes": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/browserify-aes/download/browserify-aes-1.2.0.tgz", "integrity": "sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=", "dev": true, "requires": {"buffer-xor": "^1.0.3", "cipher-base": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.3", "inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "browserify-cipher": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/browserify-cipher/download/browserify-cipher-1.0.1.tgz", "integrity": "sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=", "dev": true, "requires": {"browserify-aes": "^1.0.4", "browserify-des": "^1.0.0", "evp_bytestokey": "^1.0.0"}}, "browserify-des": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/browserify-des/download/browserify-des-1.0.2.tgz", "integrity": "sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=", "dev": true, "requires": {"cipher-base": "^1.0.1", "des.js": "^1.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "browserify-rsa": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/browserify-rsa/download/browserify-rsa-4.1.0.tgz", "integrity": "sha1-sv0Gtbda4pf3zi3GUfkY9b4VjI0=", "dev": true, "requires": {"bn.js": "^5.0.0", "randombytes": "^2.0.1"}}, "browserify-sign": {"version": "4.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/browserify-sign/download/browserify-sign-4.2.1.tgz", "integrity": "sha1-6vSt1G3VS+O7OzbAzxWrvrp5VsM=", "dev": true, "requires": {"bn.js": "^5.1.1", "browserify-rsa": "^4.0.1", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "elliptic": "^6.5.3", "inherits": "^2.0.4", "parse-asn1": "^5.1.5", "readable-stream": "^3.6.0", "safe-buffer": "^5.2.0"}, "dependencies": {"readable-stream": {"version": "3.6.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/readable-stream/download/readable-stream-3.6.0.tgz", "integrity": "sha1-M3u9o63AcGvT4CRCaihtS0sskZg=", "dev": true, "requires": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}}, "safe-buffer": {"version": "5.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/safe-buffer/download/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "dev": true}}}, "browserify-zlib": {"version": "0.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/browserify-zlib/download/browserify-zlib-0.2.0.tgz", "integrity": "sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=", "dev": true, "requires": {"pako": "~1.0.5"}}, "browserslist": {"version": "4.17.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/browserslist/-/browserslist-4.17.4.tgz", "integrity": "sha1-cuJQivKkA67ApJhH7zG9gjxX6tQ=", "dev": true, "requires": {"caniuse-lite": "^1.0.30001265", "electron-to-chromium": "^1.3.867", "escalade": "^3.1.1", "node-releases": "^2.0.0", "picocolors": "^1.0.0"}}, "bser": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bser/download/bser-2.1.1.tgz", "integrity": "sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=", "dev": true, "requires": {"node-int64": "^0.4.0"}}, "buffer": {"version": "4.9.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/buffer/download/buffer-4.9.2.tgz", "integrity": "sha1-Iw6tNEACmIZEhBqwJEr4xEu+Pvg=", "dev": true, "requires": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}}, "buffer-from": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/buffer-from/download/buffer-from-1.1.1.tgz", "integrity": "sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8=", "dev": true}, "buffer-xor": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/buffer-xor/download/buffer-xor-1.0.3.tgz", "integrity": "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=", "dev": true}, "builtin-status-codes": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz", "integrity": "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=", "dev": true}, "cacache": {"version": "12.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cacache/download/cacache-12.0.4.tgz", "integrity": "sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw=", "dev": true, "requires": {"bluebird": "^3.5.5", "chownr": "^1.1.1", "figgy-pudding": "^3.5.1", "glob": "^7.1.4", "graceful-fs": "^4.1.15", "infer-owner": "^1.0.3", "lru-cache": "^5.1.1", "mississippi": "^3.0.0", "mkdirp": "^0.5.1", "move-concurrently": "^1.0.1", "promise-inflight": "^1.0.1", "rimraf": "^2.6.3", "ssri": "^6.0.1", "unique-filename": "^1.1.1", "y18n": "^4.0.0"}}, "cache-base": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cache-base/download/cache-base-1.0.1.tgz", "integrity": "sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=", "dev": true, "requires": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}}, "call-bind": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/call-bind/download/call-bind-1.0.0.tgz", "integrity": "sha1-JBJwVLs/m9y0sfuCQYGGBy93uM4=", "dev": true, "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.0"}}, "callsites": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/callsites/download/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=", "dev": true}, "camelcase": {"version": "5.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/camelcase/download/camelcase-5.3.1.tgz", "integrity": "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=", "dev": true}, "caniuse-lite": {"version": "1.0.30001269", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/caniuse-lite/-/caniuse-lite-1.0.30001269.tgz", "integrity": "sha1-OnG+4D32JzZEGPn9Ma38eqHMLVY=", "dev": true}, "capture-exit": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/capture-exit/download/capture-exit-2.0.0.tgz", "integrity": "sha1-+5U7+uvreB9iiYI52rtCbQilCaQ=", "dev": true, "requires": {"rsvp": "^4.8.4"}}, "caseless": {"version": "0.12.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/caseless/download/caseless-0.12.0.tgz", "integrity": "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=", "dev": true}, "chalk": {"version": "2.4.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/download/chalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "chokidar": {"version": "3.4.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chokidar/download/chokidar-3.4.3.tgz", "integrity": "sha1-wd84IxRI5FykrFiObHlXO6alfVs=", "dev": true, "optional": true, "requires": {"anymatch": "~3.1.1", "braces": "~3.0.2", "fsevents": "~2.1.2", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.5.0"}, "dependencies": {"anymatch": {"version": "3.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/anymatch/download/anymatch-3.1.1.tgz", "integrity": "sha1-xV7PAhheJGklk5kxDBc84xIzsUI=", "dev": true, "optional": true, "requires": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}}, "binary-extensions": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/binary-extensions/download/binary-extensions-2.1.0.tgz", "integrity": "sha1-MPpAyef+B9vIlWeM0ocCTeokHdk=", "dev": true, "optional": true}, "braces": {"version": "3.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/braces/download/braces-3.0.2.tgz", "integrity": "sha1-NFThpGLujVmeI23zNs2epPiv4Qc=", "dev": true, "optional": true, "requires": {"fill-range": "^7.0.1"}}, "fill-range": {"version": "7.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fill-range/download/fill-range-7.0.1.tgz", "integrity": "sha1-GRmmp8df44ssfHflGYU12prN2kA=", "dev": true, "optional": true, "requires": {"to-regex-range": "^5.0.1"}}, "glob-parent": {"version": "5.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/glob-parent/download/glob-parent-5.1.1.tgz", "integrity": "sha1-tsHvQXxOVmPqSY8cRa+saRa7wik=", "dev": true, "optional": true, "requires": {"is-glob": "^4.0.1"}}, "is-binary-path": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-binary-path/download/is-binary-path-2.1.0.tgz", "integrity": "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=", "dev": true, "optional": true, "requires": {"binary-extensions": "^2.0.0"}}, "is-number": {"version": "7.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-number/download/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "dev": true, "optional": true}, "readdirp": {"version": "3.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/readdirp/download/readdirp-3.5.0.tgz", "integrity": "sha1-m6dMAZsV02UnjS6Ru4xI17TULJ4=", "dev": true, "optional": true, "requires": {"picomatch": "^2.2.1"}}, "to-regex-range": {"version": "5.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/to-regex-range/download/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "dev": true, "optional": true, "requires": {"is-number": "^7.0.0"}}}}, "chownr": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chownr/download/chownr-1.1.4.tgz", "integrity": "sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=", "dev": true}, "chrome-trace-event": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chrome-trace-event/download/chrome-trace-event-1.0.2.tgz", "integrity": "sha1-I0CQ7pfH1K0aLEvq4nUF3v/GCKQ=", "dev": true, "requires": {"tslib": "^1.9.0"}}, "ci-info": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ci-info/download/ci-info-2.0.0.tgz", "integrity": "sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y=", "dev": true}, "cipher-base": {"version": "1.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cipher-base/download/cipher-base-1.0.4.tgz", "integrity": "sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94=", "dev": true, "requires": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "class-utils": {"version": "0.3.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/class-utils/download/class-utils-0.3.6.tgz", "integrity": "sha1-+TNprouafOAv1B+q0MqDAzGQxGM=", "dev": true, "requires": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/download/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}}}, "cliui": {"version": "5.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cliui/download/cliui-5.0.0.tgz", "integrity": "sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=", "dev": true, "requires": {"string-width": "^3.1.0", "strip-ansi": "^5.2.0", "wrap-ansi": "^5.1.0"}}, "co": {"version": "4.6.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/co/download/co-4.6.0.tgz", "integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=", "dev": true}, "collection-visit": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/collection-visit/download/collection-visit-1.0.0.tgz", "integrity": "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=", "dev": true, "requires": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}}, "color-convert": {"version": "1.9.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/download/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "dev": true, "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/download/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=", "dev": true}, "combined-stream": {"version": "1.0.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/combined-stream/download/combined-stream-1.0.8.tgz", "integrity": "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=", "dev": true, "requires": {"delayed-stream": "~1.0.0"}}, "commondir": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/commondir/download/commondir-1.0.1.tgz", "integrity": "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=", "dev": true}, "component-emitter": {"version": "1.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/component-emitter/download/component-emitter-1.3.0.tgz", "integrity": "sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=", "dev": true}, "concat-map": {"version": "0.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/concat-map/download/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true}, "concat-stream": {"version": "1.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/concat-stream/download/concat-stream-1.6.2.tgz", "integrity": "sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=", "dev": true, "requires": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "console-browserify": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/console-browserify/download/console-browserify-1.2.0.tgz", "integrity": "sha1-ZwY871fOts9Jk6KrOlWECujEkzY=", "dev": true}, "constants-browserify": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/constants-browserify/download/constants-browserify-1.0.0.tgz", "integrity": "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=", "dev": true}, "convert-source-map": {"version": "1.7.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/convert-source-map/download/convert-source-map-1.7.0.tgz", "integrity": "sha1-F6LLiC1/d9NJBYXizmxSRCSjpEI=", "dev": true, "requires": {"safe-buffer": "~5.1.1"}}, "copy-concurrently": {"version": "1.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/copy-concurrently/download/copy-concurrently-1.0.5.tgz", "integrity": "sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=", "dev": true, "requires": {"aproba": "^1.1.1", "fs-write-stream-atomic": "^1.0.8", "iferr": "^0.1.5", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.0"}}, "copy-descriptor": {"version": "0.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/copy-descriptor/download/copy-descriptor-0.1.1.tgz", "integrity": "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=", "dev": true}, "core-js": {"version": "3.18.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/core-js/-/core-js-3.18.3.tgz", "integrity": "sha1-hqC7otjsPfhg/vzAeo0Rl3nwFQk="}, "core-js-compat": {"version": "3.18.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/core-js-compat/-/core-js-compat-3.18.3.tgz", "integrity": "sha1-4OfoerxV77VH5/oZFp5F+p3yemc=", "dev": true, "requires": {"browserslist": "^4.17.3", "semver": "7.0.0"}, "dependencies": {"semver": {"version": "7.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/-/semver-7.0.0.tgz", "integrity": "sha1-XzyjV2HkfgWyBsba/yz4FPAxa44=", "dev": true}}}, "core-util-is": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/core-util-is/download/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=", "dev": true}, "create-ecdh": {"version": "4.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/create-ecdh/download/create-ecdh-4.0.4.tgz", "integrity": "sha1-1uf0v/pmc2CFoHYv06YyaE2rzE4=", "dev": true, "requires": {"bn.js": "^4.1.0", "elliptic": "^6.5.3"}, "dependencies": {"bn.js": {"version": "4.11.9", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bn.js/download/bn.js-4.11.9.tgz", "integrity": "sha1-JtVWgpRY+dHoH8SJUkk9C6NQeCg=", "dev": true}}}, "create-hash": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/create-hash/download/create-hash-1.2.0.tgz", "integrity": "sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=", "dev": true, "requires": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "md5.js": "^1.3.4", "ripemd160": "^2.0.1", "sha.js": "^2.4.0"}}, "create-hmac": {"version": "1.1.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/create-hmac/download/create-hmac-1.1.7.tgz", "integrity": "sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=", "dev": true, "requires": {"cipher-base": "^1.0.3", "create-hash": "^1.1.0", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "cross-env": {"version": "6.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cross-env/download/cross-env-6.0.3.tgz", "integrity": "sha1-Qla3HkmzpAY3oM5wdopu9ccq6UE=", "requires": {"cross-spawn": "^7.0.0"}}, "cross-spawn": {"version": "7.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cross-spawn/download/cross-spawn-7.0.3.tgz", "integrity": "sha1-9zqFudXUHQRVUcF34ogtSshXKKY=", "requires": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}}, "crypto-browserify": {"version": "3.12.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/crypto-browserify/download/crypto-browserify-3.12.0.tgz", "integrity": "sha1-OWz58xN/A+S45TLFj2mCVOAPgOw=", "dev": true, "requires": {"browserify-cipher": "^1.0.0", "browserify-sign": "^4.0.0", "create-ecdh": "^4.0.0", "create-hash": "^1.1.0", "create-hmac": "^1.1.0", "diffie-hellman": "^5.0.0", "inherits": "^2.0.1", "pbkdf2": "^3.0.3", "public-encrypt": "^4.0.0", "randombytes": "^2.0.0", "randomfill": "^1.0.3"}}, "cssom": {"version": "0.3.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cssom/download/cssom-0.3.8.tgz", "integrity": "sha1-nxJ29bK0Y/IRTT8sdSUK+MGjb0o=", "dev": true}, "cssstyle": {"version": "1.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cssstyle/download/cssstyle-1.4.0.tgz", "integrity": "sha1-nTEyginTxWXGHlhrAgQaKPzNzPE=", "dev": true, "requires": {"cssom": "0.3.x"}}, "cyclist": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cyclist/download/cyclist-1.0.1.tgz", "integrity": "sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk=", "dev": true}, "dashdash": {"version": "1.14.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/dashdash/download/dashdash-1.14.1.tgz", "integrity": "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=", "dev": true, "requires": {"assert-plus": "^1.0.0"}}, "data-urls": {"version": "1.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/data-urls/download/data-urls-1.1.0.tgz", "integrity": "sha1-Fe4Fgrql4iu1nHcUDaj5x2lju/4=", "dev": true, "requires": {"abab": "^2.0.0", "whatwg-mimetype": "^2.2.0", "whatwg-url": "^7.0.0"}, "dependencies": {"whatwg-url": {"version": "7.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/whatwg-url/download/whatwg-url-7.1.0.tgz", "integrity": "sha1-wsSS8eymEpiO/T0iZr4bn8YXDQY=", "dev": true, "requires": {"lodash.sortby": "^4.7.0", "tr46": "^1.0.1", "webidl-conversions": "^4.0.2"}}}}, "debug": {"version": "2.6.9", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "decamelize": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/decamelize/download/decamelize-1.2.0.tgz", "integrity": "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=", "dev": true}, "decode-uri-component": {"version": "0.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/decode-uri-component/download/decode-uri-component-0.2.0.tgz", "integrity": "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=", "dev": true}, "deep-is": {"version": "0.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/deep-is/download/deep-is-0.1.3.tgz", "integrity": "sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=", "dev": true}, "define-properties": {"version": "1.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-properties/download/define-properties-1.1.3.tgz", "integrity": "sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=", "dev": true, "requires": {"object-keys": "^1.0.12"}}, "define-property": {"version": "2.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/download/define-property-2.0.2.tgz", "integrity": "sha1-1Flono1lS6d+AqgX+HENcCyxbp0=", "dev": true, "requires": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "dependencies": {"is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-descriptor/download/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "delayed-stream": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/delayed-stream/download/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk=", "dev": true}, "des.js": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/des.js/download/des.js-1.0.1.tgz", "integrity": "sha1-U4IULhvcU/hdhtU+X0qn3rkeCEM=", "dev": true, "requires": {"inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}, "detect-file": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/detect-file/download/detect-file-1.0.0.tgz", "integrity": "sha1-8NZtA2cqglyxtzvbP+YjEMjlUrc=", "dev": true}, "detect-newline": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/detect-newline/download/detect-newline-2.1.0.tgz", "integrity": "sha1-9B8cEL5LAOh7XxPaaAdZ8sW/0+I=", "dev": true}, "diff-sequences": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/diff-sequences/download/diff-sequences-24.9.0.tgz", "integrity": "sha1-VxXWJE4qpl9Iu6C8ly2wsLEelbU=", "dev": true}, "diffie-hellman": {"version": "5.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/diffie-hellman/download/diffie-hellman-5.0.3.tgz", "integrity": "sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=", "dev": true, "requires": {"bn.js": "^4.1.0", "miller-rabin": "^4.0.0", "randombytes": "^2.0.0"}, "dependencies": {"bn.js": {"version": "4.11.9", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bn.js/download/bn.js-4.11.9.tgz", "integrity": "sha1-JtVWgpRY+dHoH8SJUkk9C6NQeCg=", "dev": true}}}, "domain-browser": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/domain-browser/download/domain-browser-1.2.0.tgz", "integrity": "sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto=", "dev": true}, "domexception": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/domexception/download/domexception-1.0.1.tgz", "integrity": "sha1-k3RCZEymoxJh7zbj7Gd/6AVYLJA=", "dev": true, "requires": {"webidl-conversions": "^4.0.2"}}, "duplexify": {"version": "3.7.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/duplexify/download/duplexify-3.7.1.tgz", "integrity": "sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk=", "dev": true, "requires": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}}, "ecc-jsbn": {"version": "0.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz", "integrity": "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=", "dev": true, "requires": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "electron-to-chromium": {"version": "1.3.873", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/electron-to-chromium/-/electron-to-chromium-1.3.873.tgz", "integrity": "sha1-wjjJGZ5JUZUv6BWmXBvqtdtIJrg=", "dev": true}, "elliptic": {"version": "6.5.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/elliptic/download/elliptic-6.5.4.tgz", "integrity": "sha1-2jfOvTHnmhNn6UG1ku0fvr1Yq7s=", "dev": true, "requires": {"bn.js": "^4.11.9", "brorand": "^1.1.0", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.1", "inherits": "^2.0.4", "minimalistic-assert": "^1.0.1", "minimalistic-crypto-utils": "^1.0.1"}, "dependencies": {"bn.js": {"version": "4.12.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bn.js/download/bn.js-4.12.0.tgz", "integrity": "sha1-d1s/J477uXGO7HNh9IP7Nvu/6og=", "dev": true}}}, "emoji-regex": {"version": "7.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/emoji-regex/download/emoji-regex-7.0.3.tgz", "integrity": "sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=", "dev": true}, "emojis-list": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/emojis-list/download/emojis-list-3.0.0.tgz", "integrity": "sha1-VXBmIEatKeLpFucariYKvf9Pang=", "dev": true}, "end-of-stream": {"version": "1.4.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/end-of-stream/download/end-of-stream-1.4.4.tgz", "integrity": "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=", "dev": true, "requires": {"once": "^1.4.0"}}, "enhanced-resolve": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/enhanced-resolve/download/enhanced-resolve-4.3.0.tgz", "integrity": "sha1-O4BvO/r8HsfeaVUe+TzKRsFwQSY=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "memory-fs": "^0.5.0", "tapable": "^1.0.0"}, "dependencies": {"memory-fs": {"version": "0.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/memory-fs/download/memory-fs-0.5.0.tgz", "integrity": "sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw=", "dev": true, "requires": {"errno": "^0.1.3", "readable-stream": "^2.0.1"}}}}, "errno": {"version": "0.1.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/errno/download/errno-0.1.8.tgz", "integrity": "sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8=", "dev": true, "requires": {"prr": "~1.0.1"}}, "error-ex": {"version": "1.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/error-ex/download/error-ex-1.3.2.tgz", "integrity": "sha1-tKxAZIEH/c3PriQvQovqihTU8b8=", "dev": true, "requires": {"is-arrayish": "^0.2.1"}}, "es-abstract": {"version": "1.17.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/es-abstract/download/es-abstract-1.17.7.tgz", "integrity": "sha1-pN5hsvZpifx0IWdsHLl4dXOs5Uw=", "dev": true, "requires": {"es-to-primitive": "^1.2.1", "function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1", "is-callable": "^1.2.2", "is-regex": "^1.1.1", "object-inspect": "^1.8.0", "object-keys": "^1.1.1", "object.assign": "^4.1.1", "string.prototype.trimend": "^1.0.1", "string.prototype.trimstart": "^1.0.1"}}, "es-to-primitive": {"version": "1.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/es-to-primitive/download/es-to-primitive-1.2.1.tgz", "integrity": "sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=", "dev": true, "requires": {"is-callable": "^1.1.4", "is-date-object": "^1.0.1", "is-symbol": "^1.0.2"}}, "escalade": {"version": "3.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/escalade/-/escalade-3.1.1.tgz", "integrity": "sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=", "dev": true}, "escape-string-regexp": {"version": "1.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=", "dev": true}, "escodegen": {"version": "1.14.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/escodegen/download/escodegen-1.14.3.tgz", "integrity": "sha1-TnuB+6YVgdyXWC7XjKt/Do1j9QM=", "dev": true, "requires": {"esprima": "^4.0.1", "estraverse": "^4.2.0", "esutils": "^2.0.2", "optionator": "^0.8.1", "source-map": "~0.6.1"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "optional": true}}}, "eslint-scope": {"version": "4.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/eslint-scope/download/eslint-scope-4.0.3.tgz", "integrity": "sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=", "dev": true, "requires": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}}, "esprima": {"version": "4.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/esprima/download/esprima-4.0.1.tgz", "integrity": "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=", "dev": true}, "esrecurse": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/esrecurse/download/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "dev": true, "requires": {"estraverse": "^5.2.0"}, "dependencies": {"estraverse": {"version": "5.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/estraverse/download/estraverse-5.2.0.tgz", "integrity": "sha1-MH30JUfmzHMk088DwVXVzbjFOIA=", "dev": true}}}, "estraverse": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/estraverse/download/estraverse-4.3.0.tgz", "integrity": "sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=", "dev": true}, "esutils": {"version": "2.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/esutils/download/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=", "dev": true}, "events": {"version": "3.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/events/download/events-3.2.0.tgz", "integrity": "sha1-k7h8GPjvzUICpGGuxN/AVWtjk3k=", "dev": true}, "evp_bytestokey": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz", "integrity": "sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=", "dev": true, "requires": {"md5.js": "^1.3.4", "safe-buffer": "^5.1.1"}}, "exec-sh": {"version": "0.3.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/exec-sh/download/exec-sh-0.3.4.tgz", "integrity": "sha1-OgGM61JsxvbfK7UEsr/o46STTsU=", "dev": true}, "execa": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/execa/download/execa-1.0.0.tgz", "integrity": "sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=", "dev": true, "requires": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "dependencies": {"cross-spawn": {"version": "6.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cross-spawn/download/cross-spawn-6.0.5.tgz", "integrity": "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=", "dev": true, "requires": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "path-key": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-key/download/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=", "dev": true}, "shebang-command": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/shebang-command/download/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "dev": true, "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/shebang-regex/download/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=", "dev": true}, "which": {"version": "1.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/which/download/which-1.3.1.tgz", "integrity": "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=", "dev": true, "requires": {"isexe": "^2.0.0"}}}}, "exit": {"version": "0.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/exit/download/exit-0.1.2.tgz", "integrity": "sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=", "dev": true}, "expand-brackets": {"version": "2.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/expand-brackets/download/expand-brackets-2.1.4.tgz", "integrity": "sha1-t3c14xXOMPa27/D4OwQVGiJEliI=", "dev": true, "requires": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/download/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "expand-tilde": {"version": "2.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/expand-tilde/download/expand-tilde-2.0.2.tgz", "integrity": "sha1-l+gBqgUt8CRU3kawK/YhZCzchQI=", "dev": true, "requires": {"homedir-polyfill": "^1.0.1"}}, "expect": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/expect/download/expect-24.9.0.tgz", "integrity": "sha1-t1FltIFwdPpKFXeU9G/p8boVtso=", "dev": true, "requires": {"@jest/types": "^24.9.0", "ansi-styles": "^3.2.0", "jest-get-type": "^24.9.0", "jest-matcher-utils": "^24.9.0", "jest-message-util": "^24.9.0", "jest-regex-util": "^24.9.0"}}, "extend": {"version": "3.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend/download/extend-3.0.2.tgz", "integrity": "sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=", "dev": true}, "extend-shallow": {"version": "3.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/download/extend-shallow-3.0.2.tgz", "integrity": "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=", "dev": true, "requires": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-extendable/download/is-extendable-1.0.1.tgz", "integrity": "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=", "dev": true, "requires": {"is-plain-object": "^2.0.4"}}}}, "extglob": {"version": "2.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extglob/download/extglob-2.0.4.tgz", "integrity": "sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=", "dev": true, "requires": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/download/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-descriptor/download/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "extsprintf": {"version": "1.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extsprintf/download/extsprintf-1.3.0.tgz", "integrity": "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=", "dev": true}, "fast-deep-equal": {"version": "3.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=", "dev": true}, "fast-json-stable-stringify": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=", "dev": true}, "fast-levenshtein": {"version": "2.0.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=", "dev": true}, "fb-watchman": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fb-watchman/download/fb-watchman-2.0.1.tgz", "integrity": "sha1-/IT7OdJwnPP/bXQ3BhV7tXCKioU=", "dev": true, "requires": {"bser": "2.1.1"}}, "figgy-pudding": {"version": "3.5.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/figgy-pudding/download/figgy-pudding-3.5.2.tgz", "integrity": "sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4=", "dev": true}, "file-uri-to-path": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz", "integrity": "sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=", "dev": true, "optional": true}, "fill-range": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fill-range/download/fill-range-4.0.0.tgz", "integrity": "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=", "dev": true, "requires": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "find-cache-dir": {"version": "3.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/find-cache-dir/download/find-cache-dir-3.3.1.tgz", "integrity": "sha1-ibM/rUpGcNqpT4Vff74x1thP6IA=", "dev": true, "requires": {"commondir": "^1.0.1", "make-dir": "^3.0.2", "pkg-dir": "^4.1.0"}, "dependencies": {"make-dir": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/make-dir/download/make-dir-3.1.0.tgz", "integrity": "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=", "dev": true, "requires": {"semver": "^6.0.0"}}, "semver": {"version": "6.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/download/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "dev": true}}}, "find-up": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/find-up/download/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "dev": true, "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}, "findup-sync": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/findup-sync/download/findup-sync-3.0.0.tgz", "integrity": "sha1-F7EI+e5RLft6XH88iyfqnhqcCNE=", "dev": true, "requires": {"detect-file": "^1.0.0", "is-glob": "^4.0.0", "micromatch": "^3.0.4", "resolve-dir": "^1.0.1"}}, "flush-write-stream": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/flush-write-stream/download/flush-write-stream-1.1.1.tgz", "integrity": "sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug=", "dev": true, "requires": {"inherits": "^2.0.3", "readable-stream": "^2.3.6"}}, "follow-redirects": {"version": "1.13.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/follow-redirects/download/follow-redirects-1.13.3.tgz", "integrity": "sha1-5VmK1QF0wbxOhyMB6CrCzZf5Amc="}, "for-in": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/for-in/download/for-in-1.0.2.tgz", "integrity": "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=", "dev": true}, "forever-agent": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/forever-agent/download/forever-agent-0.6.1.tgz", "integrity": "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=", "dev": true}, "form-data": {"version": "2.3.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/form-data/download/form-data-2.3.3.tgz", "integrity": "sha1-3M5SwF9kTymManq5Nr1yTO/786Y=", "dev": true, "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}}, "fragment-cache": {"version": "0.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fragment-cache/download/fragment-cache-0.2.1.tgz", "integrity": "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=", "dev": true, "requires": {"map-cache": "^0.2.2"}}, "from2": {"version": "2.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/from2/download/from2-2.3.0.tgz", "integrity": "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=", "dev": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "fs-write-stream-atomic": {"version": "1.0.10", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz", "integrity": "sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "iferr": "^0.1.5", "imurmurhash": "^0.1.4", "readable-stream": "1 || 2"}}, "fs.realpath": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fs.realpath/download/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "dev": true}, "fsevents": {"version": "2.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fsevents/download/fsevents-2.1.3.tgz", "integrity": "sha1-+3OHA66NL5/pAMM4Nt3r7ouX8j4=", "dev": true, "optional": true}, "function-bind": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/function-bind/download/function-bind-1.1.1.tgz", "integrity": "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=", "dev": true}, "gensync": {"version": "1.0.0-beta.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=", "dev": true}, "get-caller-file": {"version": "2.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/get-caller-file/download/get-caller-file-2.0.5.tgz", "integrity": "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=", "dev": true}, "get-intrinsic": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/get-intrinsic/download/get-intrinsic-1.0.2.tgz", "integrity": "sha1-aCDaIm5QskiU4IhZRp3Gg2FUXUk=", "dev": true, "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1"}}, "get-stream": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/get-stream/download/get-stream-4.1.0.tgz", "integrity": "sha1-wbJVV189wh1Zv8ec09K0axw6VLU=", "dev": true, "requires": {"pump": "^3.0.0"}}, "get-value": {"version": "2.0.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/get-value/download/get-value-2.0.6.tgz", "integrity": "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=", "dev": true}, "getpass": {"version": "0.1.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/getpass/download/getpass-0.1.7.tgz", "integrity": "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=", "dev": true, "requires": {"assert-plus": "^1.0.0"}}, "glob": {"version": "7.1.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/glob/download/glob-7.1.6.tgz", "integrity": "sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY=", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-parent": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/glob-parent/download/glob-parent-3.1.0.tgz", "integrity": "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=", "dev": true, "optional": true, "requires": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}, "dependencies": {"is-glob": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-glob/download/is-glob-3.1.0.tgz", "integrity": "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=", "dev": true, "optional": true, "requires": {"is-extglob": "^2.1.0"}}}}, "global-modules": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/global-modules/download/global-modules-2.0.0.tgz", "integrity": "sha1-mXYFrSNF8n9RU5vqJldEISFcd4A=", "dev": true, "requires": {"global-prefix": "^3.0.0"}, "dependencies": {"global-prefix": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/global-prefix/download/global-prefix-3.0.0.tgz", "integrity": "sha1-/IX3MGTfafUEIfR/iD/luRO6m5c=", "dev": true, "requires": {"ini": "^1.3.5", "kind-of": "^6.0.2", "which": "^1.3.1"}}, "which": {"version": "1.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/which/download/which-1.3.1.tgz", "integrity": "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=", "dev": true, "requires": {"isexe": "^2.0.0"}}}}, "global-prefix": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/global-prefix/download/global-prefix-1.0.2.tgz", "integrity": "sha1-2/dDxsFJklk8ZVVoy2btMsASLr4=", "dev": true, "requires": {"expand-tilde": "^2.0.2", "homedir-polyfill": "^1.0.1", "ini": "^1.3.4", "is-windows": "^1.0.1", "which": "^1.2.14"}, "dependencies": {"which": {"version": "1.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/which/download/which-1.3.1.tgz", "integrity": "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=", "dev": true, "requires": {"isexe": "^2.0.0"}}}}, "globals": {"version": "11.12.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/globals/download/globals-11.12.0.tgz", "integrity": "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=", "dev": true}, "graceful-fs": {"version": "4.2.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/graceful-fs/download/graceful-fs-4.2.4.tgz", "integrity": "sha1-Ila94U02MpWMRl68ltxGfKB6Kfs=", "dev": true}, "growly": {"version": "1.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/growly/download/growly-1.3.0.tgz", "integrity": "sha1-8QdIy+dq+WS3yWyTxrzCivEgwIE=", "dev": true}, "har-schema": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/har-schema/download/har-schema-2.0.0.tgz", "integrity": "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=", "dev": true}, "har-validator": {"version": "5.1.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/har-validator/download/har-validator-5.1.5.tgz", "integrity": "sha1-HwgDufjLIMD6E4It8ezds2veHv0=", "dev": true, "requires": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}}, "has": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has/download/has-1.0.3.tgz", "integrity": "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=", "dev": true, "requires": {"function-bind": "^1.1.1"}}, "has-ansi": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-ansi/download/has-ansi-2.0.0.tgz", "integrity": "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=", "dev": true, "requires": {"ansi-regex": "^2.0.0"}, "dependencies": {"ansi-regex": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-regex/download/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true}}}, "has-flag": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/download/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "dev": true}, "has-symbols": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-symbols/download/has-symbols-1.0.1.tgz", "integrity": "sha1-n1IUdYpEGWxAbZvXbOv4HsLdMeg=", "dev": true}, "has-value": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-value/download/has-value-1.0.0.tgz", "integrity": "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=", "dev": true, "requires": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}}, "has-values": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-values/download/has-values-1.0.0.tgz", "integrity": "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=", "dev": true, "requires": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "dependencies": {"kind-of": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/download/kind-of-4.0.0.tgz", "integrity": "sha1-IIE989cSkosgc3hpGkUGb65y3Vc=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "hash-base": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/hash-base/download/hash-base-3.1.0.tgz", "integrity": "sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM=", "dev": true, "requires": {"inherits": "^2.0.4", "readable-stream": "^3.6.0", "safe-buffer": "^5.2.0"}, "dependencies": {"readable-stream": {"version": "3.6.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/readable-stream/download/readable-stream-3.6.0.tgz", "integrity": "sha1-M3u9o63AcGvT4CRCaihtS0sskZg=", "dev": true, "requires": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}}, "safe-buffer": {"version": "5.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/safe-buffer/download/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "dev": true}}}, "hash.js": {"version": "1.1.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/hash.js/download/hash.js-1.1.7.tgz", "integrity": "sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=", "dev": true, "requires": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "hmac-drbg": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/hmac-drbg/download/hmac-drbg-1.0.1.tgz", "integrity": "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=", "dev": true, "requires": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}}, "homedir-polyfill": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/homedir-polyfill/download/homedir-polyfill-1.0.3.tgz", "integrity": "sha1-dDKYzvTlrz4ZQWH7rcwhUdOgWOg=", "dev": true, "requires": {"parse-passwd": "^1.0.0"}}, "hosted-git-info": {"version": "2.8.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/hosted-git-info/download/hosted-git-info-2.8.8.tgz", "integrity": "sha1-dTm9S8Hg4KiVgVouAmJCCxKFhIg=", "dev": true}, "html-encoding-sniffer": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/html-encoding-sniffer/download/html-encoding-sniffer-1.0.2.tgz", "integrity": "sha1-5w2EuU2lOqN14R/jo1G+ZkLKRvg=", "dev": true, "requires": {"whatwg-encoding": "^1.0.1"}}, "html-escaper": {"version": "2.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/html-escaper/download/html-escaper-2.0.2.tgz", "integrity": "sha1-39YAJ9o2o238viNiYsAKWCJoFFM=", "dev": true}, "http-signature": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/http-signature/download/http-signature-1.2.0.tgz", "integrity": "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=", "dev": true, "requires": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}}, "https-browserify": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/https-browserify/download/https-browserify-1.0.0.tgz", "integrity": "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=", "dev": true}, "iconv-lite": {"version": "0.4.24", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/iconv-lite/download/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "dev": true, "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "ieee754": {"version": "1.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ieee754/download/ieee754-1.2.1.tgz", "integrity": "sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=", "dev": true}, "iferr": {"version": "0.1.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/iferr/download/iferr-0.1.5.tgz", "integrity": "sha1-xg7taebY/bazEEofy8ocGS3FtQE=", "dev": true}, "import-local": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/import-local/download/import-local-2.0.0.tgz", "integrity": "sha1-VQcL44pZk88Y72236WH1vuXFoJ0=", "dev": true, "requires": {"pkg-dir": "^3.0.0", "resolve-cwd": "^2.0.0"}, "dependencies": {"find-up": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/find-up/download/find-up-3.0.0.tgz", "integrity": "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=", "dev": true, "requires": {"locate-path": "^3.0.0"}}, "locate-path": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/locate-path/download/locate-path-3.0.0.tgz", "integrity": "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=", "dev": true, "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "p-locate": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-locate/download/p-locate-3.0.0.tgz", "integrity": "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=", "dev": true, "requires": {"p-limit": "^2.0.0"}}, "path-exists": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-exists/download/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "dev": true}, "pkg-dir": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pkg-dir/download/pkg-dir-3.0.0.tgz", "integrity": "sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=", "dev": true, "requires": {"find-up": "^3.0.0"}}}}, "imurmurhash": {"version": "0.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/imurmurhash/download/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true}, "infer-owner": {"version": "1.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/infer-owner/download/infer-owner-1.0.4.tgz", "integrity": "sha1-xM78qo5RBRwqQLos6KPScpWvlGc=", "dev": true}, "inflight": {"version": "1.0.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/inflight/download/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "dev": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/inherits/download/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "dev": true}, "ini": {"version": "1.3.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ini/download/ini-1.3.8.tgz", "integrity": "sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=", "dev": true}, "interpret": {"version": "1.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/interpret/download/interpret-1.4.0.tgz", "integrity": "sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=", "dev": true}, "invariant": {"version": "2.2.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/invariant/download/invariant-2.2.4.tgz", "integrity": "sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=", "dev": true, "requires": {"loose-envify": "^1.0.0"}}, "is-accessor-descriptor": {"version": "0.1.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz", "integrity": "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "is-arrayish": {"version": "0.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-arrayish/download/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=", "dev": true}, "is-binary-path": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-binary-path/download/is-binary-path-1.0.1.tgz", "integrity": "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=", "dev": true, "optional": true, "requires": {"binary-extensions": "^1.0.0"}}, "is-buffer": {"version": "1.1.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-buffer/download/is-buffer-1.1.6.tgz", "integrity": "sha1-76ouqdqg16suoTqXsritUf776L4=", "dev": true}, "is-callable": {"version": "1.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-callable/download/is-callable-1.2.2.tgz", "integrity": "sha1-x8ZxXNItTdtI0+GZcCI6zquwgNk=", "dev": true}, "is-ci": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-ci/download/is-ci-2.0.0.tgz", "integrity": "sha1-a8YzQYGBDgS1wis9WJ/cpVAmQEw=", "dev": true, "requires": {"ci-info": "^2.0.0"}}, "is-core-module": {"version": "2.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-core-module/download/is-core-module-2.2.0.tgz", "integrity": "sha1-lwN+89UiJNhRY/VZeytj2a/tmBo=", "dev": true, "requires": {"has": "^1.0.3"}}, "is-data-descriptor": {"version": "0.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz", "integrity": "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "is-date-object": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-date-object/download/is-date-object-1.0.2.tgz", "integrity": "sha1-vac28s2P0G0yhE53Q7+nSUw7/X4=", "dev": true}, "is-descriptor": {"version": "0.1.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-descriptor/download/is-descriptor-0.1.6.tgz", "integrity": "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=", "dev": true, "requires": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "dependencies": {"kind-of": {"version": "5.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/download/kind-of-5.1.0.tgz", "integrity": "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=", "dev": true}}}, "is-extendable": {"version": "0.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-extendable/download/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "dev": true}, "is-extglob": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-extglob/download/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true}, "is-fullwidth-code-point": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=", "dev": true}, "is-generator-fn": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-generator-fn/download/is-generator-fn-2.1.0.tgz", "integrity": "sha1-fRQK3DiarzARqPKipM+m+q3/sRg=", "dev": true}, "is-glob": {"version": "4.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-glob/download/is-glob-4.0.1.tgz", "integrity": "sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw=", "dev": true, "requires": {"is-extglob": "^2.1.1"}}, "is-negative-zero": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-negative-zero/download/is-negative-zero-2.0.1.tgz", "integrity": "sha1-PedGwY3aIxkkGlNnWQjY92bxHCQ=", "dev": true}, "is-number": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-number/download/is-number-3.0.0.tgz", "integrity": "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "is-plain-object": {"version": "2.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-plain-object/download/is-plain-object-2.0.4.tgz", "integrity": "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=", "dev": true, "requires": {"isobject": "^3.0.1"}}, "is-regex": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-regex/download/is-regex-1.1.1.tgz", "integrity": "sha1-xvmKrMVG9s7FRooHt7FTq1ZKV7k=", "dev": true, "requires": {"has-symbols": "^1.0.1"}}, "is-stream": {"version": "1.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-stream/download/is-stream-1.1.0.tgz", "integrity": "sha1-EtSj3U5o4Lec6428hBc66A2RykQ=", "dev": true}, "is-symbol": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-symbol/download/is-symbol-1.0.3.tgz", "integrity": "sha1-OOEBS55jKb4N6dJKQU/XRB7GGTc=", "dev": true, "requires": {"has-symbols": "^1.0.1"}}, "is-typedarray": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-typedarray/download/is-typedarray-1.0.0.tgz", "integrity": "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=", "dev": true}, "is-windows": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-windows/download/is-windows-1.0.2.tgz", "integrity": "sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=", "dev": true}, "is-wsl": {"version": "1.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-wsl/download/is-wsl-1.1.0.tgz", "integrity": "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=", "dev": true}, "isarray": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/isarray/download/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "isexe": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/isexe/download/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="}, "isobject": {"version": "3.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/isobject/download/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8=", "dev": true}, "isstream": {"version": "0.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/isstream/download/isstream-0.1.2.tgz", "integrity": "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=", "dev": true}, "istanbul-lib-coverage": {"version": "2.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/istanbul-lib-coverage/download/istanbul-lib-coverage-2.0.5.tgz", "integrity": "sha1-Z18KtpUD+tSx2En3NrqsqAM0T0k=", "dev": true}, "istanbul-lib-instrument": {"version": "3.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/istanbul-lib-instrument/download/istanbul-lib-instrument-3.3.0.tgz", "integrity": "sha1-pfY9kfC7wMPkee9MXeAnM17G1jA=", "dev": true, "requires": {"@babel/generator": "^7.4.0", "@babel/parser": "^7.4.3", "@babel/template": "^7.4.0", "@babel/traverse": "^7.4.3", "@babel/types": "^7.4.0", "istanbul-lib-coverage": "^2.0.5", "semver": "^6.0.0"}, "dependencies": {"semver": {"version": "6.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/download/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "dev": true}}}, "istanbul-lib-report": {"version": "2.0.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/istanbul-lib-report/download/istanbul-lib-report-2.0.8.tgz", "integrity": "sha1-WoETzXRtQ8SInro2qxDn1QybTzM=", "dev": true, "requires": {"istanbul-lib-coverage": "^2.0.5", "make-dir": "^2.1.0", "supports-color": "^6.1.0"}, "dependencies": {"supports-color": {"version": "6.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-6.1.0.tgz", "integrity": "sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "istanbul-lib-source-maps": {"version": "3.0.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/istanbul-lib-source-maps/download/istanbul-lib-source-maps-3.0.6.tgz", "integrity": "sha1-KEmXxIIRdS7EhiU9qX44ed77qMg=", "dev": true, "requires": {"debug": "^4.1.1", "istanbul-lib-coverage": "^2.0.5", "make-dir": "^2.1.0", "rimraf": "^2.6.3", "source-map": "^0.6.1"}, "dependencies": {"debug": {"version": "4.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/download/debug-4.3.1.tgz", "integrity": "sha1-8NIpxQXgxtjEmsVT0bE9wYP2su4=", "dev": true, "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/download/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "dev": true}, "source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "istanbul-reports": {"version": "2.2.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/istanbul-reports/download/istanbul-reports-2.2.7.tgz", "integrity": "sha1-XZOfYjfXtIOTzAlZ6rQM1P0FaTE=", "dev": true, "requires": {"html-escaper": "^2.0.0"}}, "jest": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest/download/jest-24.9.0.tgz", "integrity": "sha1-mH0pDAWgi1LFYYjBAC42jtsAcXE=", "dev": true, "requires": {"import-local": "^2.0.0", "jest-cli": "^24.9.0"}, "dependencies": {"jest-cli": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-cli/download/jest-cli-24.9.0.tgz", "integrity": "sha1-rS3mLQdHLUGcarwwH8QyuYsQ0q8=", "dev": true, "requires": {"@jest/core": "^24.9.0", "@jest/test-result": "^24.9.0", "@jest/types": "^24.9.0", "chalk": "^2.0.1", "exit": "^0.1.2", "import-local": "^2.0.0", "is-ci": "^2.0.0", "jest-config": "^24.9.0", "jest-util": "^24.9.0", "jest-validate": "^24.9.0", "prompts": "^2.0.1", "realpath-native": "^1.1.0", "yargs": "^13.3.0"}}}}, "jest-changed-files": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-changed-files/download/jest-changed-files-24.9.0.tgz", "integrity": "sha1-CNjBXreaf6P8mCabwUtFHugvgDk=", "dev": true, "requires": {"@jest/types": "^24.9.0", "execa": "^1.0.0", "throat": "^4.0.0"}}, "jest-config": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-config/download/jest-config-24.9.0.tgz", "integrity": "sha1-+xu8YMc6Rq8DWQcZ76SCXm5N0bU=", "dev": true, "requires": {"@babel/core": "^7.1.0", "@jest/test-sequencer": "^24.9.0", "@jest/types": "^24.9.0", "babel-jest": "^24.9.0", "chalk": "^2.0.1", "glob": "^7.1.1", "jest-environment-jsdom": "^24.9.0", "jest-environment-node": "^24.9.0", "jest-get-type": "^24.9.0", "jest-jasmine2": "^24.9.0", "jest-regex-util": "^24.3.0", "jest-resolve": "^24.9.0", "jest-util": "^24.9.0", "jest-validate": "^24.9.0", "micromatch": "^3.1.10", "pretty-format": "^24.9.0", "realpath-native": "^1.1.0"}}, "jest-diff": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-diff/download/jest-diff-24.9.0.tgz", "integrity": "sha1-kxt9DVd4obr3RSy4FuMl43JAVdo=", "dev": true, "requires": {"chalk": "^2.0.1", "diff-sequences": "^24.9.0", "jest-get-type": "^24.9.0", "pretty-format": "^24.9.0"}}, "jest-docblock": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-docblock/download/jest-docblock-24.9.0.tgz", "integrity": "sha1-eXAgGAK6Vg4cQJLMJcvt9a9ajOI=", "dev": true, "requires": {"detect-newline": "^2.1.0"}}, "jest-each": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-each/download/jest-each-24.9.0.tgz", "integrity": "sha1-6y2mAuKmEImNvF8fbfO6hrVfiwU=", "dev": true, "requires": {"@jest/types": "^24.9.0", "chalk": "^2.0.1", "jest-get-type": "^24.9.0", "jest-util": "^24.9.0", "pretty-format": "^24.9.0"}}, "jest-environment-jsdom": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-environment-jsdom/download/jest-environment-jsdom-24.9.0.tgz", "integrity": "sha1-SwgGx/yU+V7bNpppzCd47sK3N1s=", "dev": true, "requires": {"@jest/environment": "^24.9.0", "@jest/fake-timers": "^24.9.0", "@jest/types": "^24.9.0", "jest-mock": "^24.9.0", "jest-util": "^24.9.0", "jsdom": "^11.5.1"}}, "jest-environment-node": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-environment-node/download/jest-environment-node-24.9.0.tgz", "integrity": "sha1-Mz0tJ5b5aH8q7r8HQrUZ8zwcv9M=", "dev": true, "requires": {"@jest/environment": "^24.9.0", "@jest/fake-timers": "^24.9.0", "@jest/types": "^24.9.0", "jest-mock": "^24.9.0", "jest-util": "^24.9.0"}}, "jest-get-type": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-get-type/download/jest-get-type-24.9.0.tgz", "integrity": "sha1-FoSgyKUPLkkBtmRK6GH1ee7S7w4=", "dev": true}, "jest-haste-map": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-haste-map/download/jest-haste-map-24.9.0.tgz", "integrity": "sha1-s4pdZCdJNOIfpBeump++t3zqrH0=", "dev": true, "requires": {"@jest/types": "^24.9.0", "anymatch": "^2.0.0", "fb-watchman": "^2.0.0", "fsevents": "^1.2.7", "graceful-fs": "^4.1.15", "invariant": "^2.2.4", "jest-serializer": "^24.9.0", "jest-util": "^24.9.0", "jest-worker": "^24.9.0", "micromatch": "^3.1.10", "sane": "^4.0.3", "walker": "^1.0.7"}, "dependencies": {"fsevents": {"version": "1.2.13", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fsevents/download/fsevents-1.2.13.tgz", "integrity": "sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=", "dev": true, "optional": true, "requires": {"bindings": "^1.5.0", "nan": "^2.12.1"}}}}, "jest-jasmine2": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-jasmine2/download/jest-jasmine2-24.9.0.tgz", "integrity": "sha1-H3sb0yQsF3TmKsq7NkbZavw75qA=", "dev": true, "requires": {"@babel/traverse": "^7.1.0", "@jest/environment": "^24.9.0", "@jest/test-result": "^24.9.0", "@jest/types": "^24.9.0", "chalk": "^2.0.1", "co": "^4.6.0", "expect": "^24.9.0", "is-generator-fn": "^2.0.0", "jest-each": "^24.9.0", "jest-matcher-utils": "^24.9.0", "jest-message-util": "^24.9.0", "jest-runtime": "^24.9.0", "jest-snapshot": "^24.9.0", "jest-util": "^24.9.0", "pretty-format": "^24.9.0", "throat": "^4.0.0"}}, "jest-leak-detector": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-leak-detector/download/jest-leak-detector-24.9.0.tgz", "integrity": "sha1-tmXep8dxAMXE99/LFTtlzwfc+Wo=", "dev": true, "requires": {"jest-get-type": "^24.9.0", "pretty-format": "^24.9.0"}}, "jest-matcher-utils": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-matcher-utils/download/jest-matcher-utils-24.9.0.tgz", "integrity": "sha1-9bNmHV5ijf/m3WUlHf2uDofDoHM=", "dev": true, "requires": {"chalk": "^2.0.1", "jest-diff": "^24.9.0", "jest-get-type": "^24.9.0", "pretty-format": "^24.9.0"}}, "jest-message-util": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-message-util/download/jest-message-util-24.9.0.tgz", "integrity": "sha1-Un9UoeOA9eICqNEUmw7IcvQxGeM=", "dev": true, "requires": {"@babel/code-frame": "^7.0.0", "@jest/test-result": "^24.9.0", "@jest/types": "^24.9.0", "@types/stack-utils": "^1.0.1", "chalk": "^2.0.1", "micromatch": "^3.1.10", "slash": "^2.0.0", "stack-utils": "^1.0.1"}}, "jest-mock": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-mock/download/jest-mock-24.9.0.tgz", "integrity": "sha1-wig1VB7jebkIZzrVEIeiGFwT8cY=", "dev": true, "requires": {"@jest/types": "^24.9.0"}}, "jest-pnp-resolver": {"version": "1.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-pnp-resolver/download/jest-pnp-resolver-1.2.2.tgz", "integrity": "sha1-twSsCuAoqJEIpNBAs/kZ393I4zw=", "dev": true}, "jest-regex-util": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-regex-util/download/jest-regex-util-24.9.0.tgz", "integrity": "sha1-wT+zOAveIr9ldUMsST6o/jeWVjY=", "dev": true}, "jest-resolve": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-resolve/download/jest-resolve-24.9.0.tgz", "integrity": "sha1-3/BMdoevNMTdflJIktnPd+XRcyE=", "dev": true, "requires": {"@jest/types": "^24.9.0", "browser-resolve": "^1.11.3", "chalk": "^2.0.1", "jest-pnp-resolver": "^1.2.1", "realpath-native": "^1.1.0"}}, "jest-resolve-dependencies": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-resolve-dependencies/download/jest-resolve-dependencies-24.9.0.tgz", "integrity": "sha1-rQVRmJWcTPuopPBmxnOj8HhlB6s=", "dev": true, "requires": {"@jest/types": "^24.9.0", "jest-regex-util": "^24.3.0", "jest-snapshot": "^24.9.0"}}, "jest-runner": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-runner/download/jest-runner-24.9.0.tgz", "integrity": "sha1-V0+v29VEVcKzS0vfQ2WiOFf830I=", "dev": true, "requires": {"@jest/console": "^24.7.1", "@jest/environment": "^24.9.0", "@jest/test-result": "^24.9.0", "@jest/types": "^24.9.0", "chalk": "^2.4.2", "exit": "^0.1.2", "graceful-fs": "^4.1.15", "jest-config": "^24.9.0", "jest-docblock": "^24.3.0", "jest-haste-map": "^24.9.0", "jest-jasmine2": "^24.9.0", "jest-leak-detector": "^24.9.0", "jest-message-util": "^24.9.0", "jest-resolve": "^24.9.0", "jest-runtime": "^24.9.0", "jest-util": "^24.9.0", "jest-worker": "^24.6.0", "source-map-support": "^0.5.6", "throat": "^4.0.0"}}, "jest-runtime": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-runtime/download/jest-runtime-24.9.0.tgz", "integrity": "sha1-nxRYOvak9zFKap2fAibhp4HI5Kw=", "dev": true, "requires": {"@jest/console": "^24.7.1", "@jest/environment": "^24.9.0", "@jest/source-map": "^24.3.0", "@jest/transform": "^24.9.0", "@jest/types": "^24.9.0", "@types/yargs": "^13.0.0", "chalk": "^2.0.1", "exit": "^0.1.2", "glob": "^7.1.3", "graceful-fs": "^4.1.15", "jest-config": "^24.9.0", "jest-haste-map": "^24.9.0", "jest-message-util": "^24.9.0", "jest-mock": "^24.9.0", "jest-regex-util": "^24.3.0", "jest-resolve": "^24.9.0", "jest-snapshot": "^24.9.0", "jest-util": "^24.9.0", "jest-validate": "^24.9.0", "realpath-native": "^1.1.0", "slash": "^2.0.0", "strip-bom": "^3.0.0", "yargs": "^13.3.0"}}, "jest-serializer": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-serializer/download/jest-serializer-24.9.0.tgz", "integrity": "sha1-5tfX75bTHouQeacUdUxdXFgojnM=", "dev": true}, "jest-snapshot": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-snapshot/download/jest-snapshot-24.9.0.tgz", "integrity": "sha1-7I6cpPLsDFyHro+SXPl0l7DpUbo=", "dev": true, "requires": {"@babel/types": "^7.0.0", "@jest/types": "^24.9.0", "chalk": "^2.0.1", "expect": "^24.9.0", "jest-diff": "^24.9.0", "jest-get-type": "^24.9.0", "jest-matcher-utils": "^24.9.0", "jest-message-util": "^24.9.0", "jest-resolve": "^24.9.0", "mkdirp": "^0.5.1", "natural-compare": "^1.4.0", "pretty-format": "^24.9.0", "semver": "^6.2.0"}, "dependencies": {"semver": {"version": "6.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/download/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "dev": true}}}, "jest-util": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-util/download/jest-util-24.9.0.tgz", "integrity": "sha1-c5aBTkhTbS6Fo33j5MQx18sUAWI=", "dev": true, "requires": {"@jest/console": "^24.9.0", "@jest/fake-timers": "^24.9.0", "@jest/source-map": "^24.9.0", "@jest/test-result": "^24.9.0", "@jest/types": "^24.9.0", "callsites": "^3.0.0", "chalk": "^2.0.1", "graceful-fs": "^4.1.15", "is-ci": "^2.0.0", "mkdirp": "^0.5.1", "slash": "^2.0.0", "source-map": "^0.6.0"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "jest-validate": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-validate/download/jest-validate-24.9.0.tgz", "integrity": "sha1-B3XFU2DRc82FTkAYB1bU/1Le+Ks=", "dev": true, "requires": {"@jest/types": "^24.9.0", "camelcase": "^5.3.1", "chalk": "^2.0.1", "jest-get-type": "^24.9.0", "leven": "^3.1.0", "pretty-format": "^24.9.0"}}, "jest-watcher": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-watcher/download/jest-watcher-24.9.0.tgz", "integrity": "sha1-S1bl0c7/AF9biOUo3Jr8jdTtKzs=", "dev": true, "requires": {"@jest/test-result": "^24.9.0", "@jest/types": "^24.9.0", "@types/yargs": "^13.0.0", "ansi-escapes": "^3.0.0", "chalk": "^2.0.1", "jest-util": "^24.9.0", "string-length": "^2.0.0"}}, "jest-worker": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jest-worker/download/jest-worker-24.9.0.tgz", "integrity": "sha1-Xb/bWy0yLphWeJgjipaXvM5ns+U=", "dev": true, "requires": {"merge-stream": "^2.0.0", "supports-color": "^6.1.0"}, "dependencies": {"supports-color": {"version": "6.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-6.1.0.tgz", "integrity": "sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "js-tokens": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/js-tokens/download/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk=", "dev": true}, "jsbn": {"version": "0.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jsbn/download/jsbn-0.1.1.tgz", "integrity": "sha1-peZUwuWi3rXyAdls77yoDA7y9RM=", "dev": true}, "jsdom": {"version": "11.12.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jsdom/download/jsdom-11.12.0.tgz", "integrity": "sha1-GoDUDd03ih3lllbp5txaO6hle8g=", "dev": true, "requires": {"abab": "^2.0.0", "acorn": "^5.5.3", "acorn-globals": "^4.1.0", "array-equal": "^1.0.0", "cssom": ">= 0.3.2 < 0.4.0", "cssstyle": "^1.0.0", "data-urls": "^1.0.0", "domexception": "^1.0.1", "escodegen": "^1.9.1", "html-encoding-sniffer": "^1.0.2", "left-pad": "^1.3.0", "nwsapi": "^2.0.7", "parse5": "4.0.0", "pn": "^1.1.0", "request": "^2.87.0", "request-promise-native": "^1.0.5", "sax": "^1.2.4", "symbol-tree": "^3.2.2", "tough-cookie": "^2.3.4", "w3c-hr-time": "^1.0.1", "webidl-conversions": "^4.0.2", "whatwg-encoding": "^1.0.3", "whatwg-mimetype": "^2.1.0", "whatwg-url": "^6.4.1", "ws": "^5.2.0", "xml-name-validator": "^3.0.0"}}, "jsesc": {"version": "2.5.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jsesc/download/jsesc-2.5.2.tgz", "integrity": "sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=", "dev": true}, "json-parse-better-errors": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz", "integrity": "sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=", "dev": true}, "json-schema": {"version": "0.2.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/json-schema/download/json-schema-0.2.3.tgz", "integrity": "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM=", "dev": true}, "json-schema-traverse": {"version": "0.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "dev": true}, "json-stringify-safe": {"version": "5.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz", "integrity": "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=", "dev": true}, "json5": {"version": "2.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/json5/-/json5-2.2.0.tgz", "integrity": "sha1-Lf7+cgxrpSXZ69kJlQ8FFTFsiaM=", "dev": true, "requires": {"minimist": "^1.2.5"}}, "jsprim": {"version": "1.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jsprim/download/jsprim-1.4.1.tgz", "integrity": "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=", "dev": true, "requires": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.2.3", "verror": "1.10.0"}}, "kind-of": {"version": "6.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/download/kind-of-6.0.3.tgz", "integrity": "sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=", "dev": true}, "kleur": {"version": "3.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kleur/download/kleur-3.0.3.tgz", "integrity": "sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=", "dev": true}, "left-pad": {"version": "1.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/left-pad/download/left-pad-1.3.0.tgz", "integrity": "sha1-W4o6d2Xf4AEmHd6RVYnngvjJTR4=", "dev": true}, "leven": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/leven/download/leven-3.1.0.tgz", "integrity": "sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=", "dev": true}, "levn": {"version": "0.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/levn/download/levn-0.3.0.tgz", "integrity": "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=", "dev": true, "requires": {"prelude-ls": "~1.1.2", "type-check": "~0.3.2"}}, "load-json-file": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/load-json-file/download/load-json-file-4.0.0.tgz", "integrity": "sha1-L19Fq5HjMhYjT9U62rZo607AmTs=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "parse-json": "^4.0.0", "pify": "^3.0.0", "strip-bom": "^3.0.0"}, "dependencies": {"pify": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pify/download/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true}}}, "loader-runner": {"version": "2.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/loader-runner/download/loader-runner-2.4.0.tgz", "integrity": "sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c=", "dev": true}, "loader-utils": {"version": "1.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/loader-utils/download/loader-utils-1.4.0.tgz", "integrity": "sha1-xXm140yzSxp07cbB+za/o3HVphM=", "dev": true, "requires": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^1.0.1"}, "dependencies": {"json5": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/json5/download/json5-1.0.1.tgz", "integrity": "sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4=", "dev": true, "requires": {"minimist": "^1.2.0"}}}}, "locate-path": {"version": "5.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/locate-path/download/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "dev": true, "requires": {"p-locate": "^4.1.0"}}, "lodash": {"version": "4.17.20", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/lodash/download/lodash-4.17.20.tgz", "integrity": "sha1-tEqbYpe8tpjxxRo1RaKzs2jVnFI=", "dev": true}, "lodash.debounce": {"version": "4.0.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/lodash.debounce/-/lodash.debounce-4.0.8.tgz", "integrity": "sha1-gteb/zCmfEAF/9XiUVMArZyk168=", "dev": true}, "lodash.sortby": {"version": "4.7.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/lodash.sortby/download/lodash.sortby-4.7.0.tgz", "integrity": "sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=", "dev": true}, "loose-envify": {"version": "1.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/loose-envify/download/loose-envify-1.4.0.tgz", "integrity": "sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=", "dev": true, "requires": {"js-tokens": "^3.0.0 || ^4.0.0"}}, "lru-cache": {"version": "5.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/lru-cache/download/lru-cache-5.1.1.tgz", "integrity": "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=", "dev": true, "requires": {"yallist": "^3.0.2"}}, "make-dir": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/make-dir/download/make-dir-2.1.0.tgz", "integrity": "sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=", "dev": true, "requires": {"pify": "^4.0.1", "semver": "^5.6.0"}}, "makeerror": {"version": "1.0.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/makeerror/download/makeerror-1.0.11.tgz", "integrity": "sha1-4BpckQnyr3lmDk6LlYd5AYT1qWw=", "dev": true, "requires": {"tmpl": "1.0.x"}}, "map-cache": {"version": "0.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/map-cache/download/map-cache-0.2.2.tgz", "integrity": "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=", "dev": true}, "map-visit": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/map-visit/download/map-visit-1.0.0.tgz", "integrity": "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=", "dev": true, "requires": {"object-visit": "^1.0.0"}}, "md5.js": {"version": "1.3.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/md5.js/download/md5.js-1.3.5.tgz", "integrity": "sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=", "dev": true, "requires": {"hash-base": "^3.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "memory-fs": {"version": "0.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/memory-fs/download/memory-fs-0.4.1.tgz", "integrity": "sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=", "dev": true, "requires": {"errno": "^0.1.3", "readable-stream": "^2.0.1"}}, "merge-stream": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/merge-stream/download/merge-stream-2.0.0.tgz", "integrity": "sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=", "dev": true}, "micromatch": {"version": "3.1.10", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/micromatch/download/micromatch-3.1.10.tgz", "integrity": "sha1-cIWbyVyYQJUvNZoGij/En57PrCM=", "dev": true, "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}}, "miller-rabin": {"version": "4.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/miller-rabin/download/miller-rabin-4.0.1.tgz", "integrity": "sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=", "dev": true, "requires": {"bn.js": "^4.0.0", "brorand": "^1.0.1"}, "dependencies": {"bn.js": {"version": "4.11.9", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bn.js/download/bn.js-4.11.9.tgz", "integrity": "sha1-JtVWgpRY+dHoH8SJUkk9C6NQeCg=", "dev": true}}}, "mime": {"version": "2.4.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/mime/download/mime-2.4.7.tgz", "integrity": "sha1-lirtm+DtGckf19wuzl1/TompDXQ=", "dev": true}, "mime-db": {"version": "1.44.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/mime-db/download/mime-db-1.44.0.tgz", "integrity": "sha1-+hHF6wrKEzS0Izy01S8QxaYnL5I=", "dev": true}, "mime-types": {"version": "2.1.27", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/mime-types/download/mime-types-2.1.27.tgz", "integrity": "sha1-R5SfmOJ56lMRn1ci4PNOUpvsAJ8=", "dev": true, "requires": {"mime-db": "1.44.0"}}, "minimalistic-assert": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz", "integrity": "sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=", "dev": true}, "minimalistic-crypto-utils": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz", "integrity": "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=", "dev": true}, "minimatch": {"version": "3.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/minimatch/download/minimatch-3.0.4.tgz", "integrity": "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=", "dev": true, "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/minimist/download/minimist-1.2.5.tgz", "integrity": "sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=", "dev": true}, "mississippi": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/mississippi/download/mississippi-3.0.0.tgz", "integrity": "sha1-6goykfl+C16HdrNj1fChLZTGcCI=", "dev": true, "requires": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^3.0.0", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}}, "mixin-deep": {"version": "1.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/mixin-deep/download/mixin-deep-1.3.2.tgz", "integrity": "sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=", "dev": true, "requires": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-extendable/download/is-extendable-1.0.1.tgz", "integrity": "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=", "dev": true, "requires": {"is-plain-object": "^2.0.4"}}}}, "mkdirp": {"version": "0.5.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/mkdirp/download/mkdirp-0.5.5.tgz", "integrity": "sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=", "dev": true, "requires": {"minimist": "^1.2.5"}}, "move-concurrently": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/move-concurrently/download/move-concurrently-1.0.1.tgz", "integrity": "sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=", "dev": true, "requires": {"aproba": "^1.1.1", "copy-concurrently": "^1.0.0", "fs-write-stream-atomic": "^1.0.8", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.3"}}, "ms": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "nan": {"version": "2.14.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/nan/download/nan-2.14.2.tgz", "integrity": "sha1-9TdkAGlRaPTMaUrJOT0MlYXu6hk=", "dev": true, "optional": true}, "nanomatch": {"version": "1.2.13", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/nanomatch/download/nanomatch-1.2.13.tgz", "integrity": "sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=", "dev": true, "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}}, "natural-compare": {"version": "1.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/natural-compare/download/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "dev": true}, "neo-async": {"version": "2.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/neo-async/download/neo-async-2.6.2.tgz", "integrity": "sha1-tKr7k+OustgXTKU88WOrfXMIMF8=", "dev": true}, "nice-try": {"version": "1.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/nice-try/download/nice-try-1.0.5.tgz", "integrity": "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=", "dev": true}, "node-int64": {"version": "0.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/node-int64/download/node-int64-0.4.0.tgz", "integrity": "sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=", "dev": true}, "node-libs-browser": {"version": "2.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/node-libs-browser/download/node-libs-browser-2.2.1.tgz", "integrity": "sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU=", "dev": true, "requires": {"assert": "^1.1.1", "browserify-zlib": "^0.2.0", "buffer": "^4.3.0", "console-browserify": "^1.1.0", "constants-browserify": "^1.0.0", "crypto-browserify": "^3.11.0", "domain-browser": "^1.1.1", "events": "^3.0.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "0.0.1", "process": "^0.11.10", "punycode": "^1.2.4", "querystring-es3": "^0.2.0", "readable-stream": "^2.3.3", "stream-browserify": "^2.0.1", "stream-http": "^2.7.2", "string_decoder": "^1.0.0", "timers-browserify": "^2.0.4", "tty-browserify": "0.0.0", "url": "^0.11.0", "util": "^0.11.0", "vm-browserify": "^1.0.1"}, "dependencies": {"punycode": {"version": "1.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/punycode/download/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4=", "dev": true}}}, "node-modules-regexp": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/node-modules-regexp/download/node-modules-regexp-1.0.0.tgz", "integrity": "sha1-jZ2+KJZKSsVxLpExZCEHxx6Q7EA=", "dev": true}, "node-notifier": {"version": "5.4.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/node-notifier/download/node-notifier-5.4.3.tgz", "integrity": "sha1-y3La+UyTkECY4oucWQ/YZuRkvVA=", "dev": true, "requires": {"growly": "^1.3.0", "is-wsl": "^1.1.0", "semver": "^5.5.0", "shellwords": "^0.1.1", "which": "^1.3.0"}, "dependencies": {"which": {"version": "1.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/which/download/which-1.3.1.tgz", "integrity": "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=", "dev": true, "requires": {"isexe": "^2.0.0"}}}}, "node-releases": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/node-releases/-/node-releases-2.0.0.tgz", "integrity": "sha1-Z9x0kDEAp96wRAN7ii5fRTuwVAA=", "dev": true}, "normalize-package-data": {"version": "2.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/normalize-package-data/download/normalize-package-data-2.5.0.tgz", "integrity": "sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=", "dev": true, "requires": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "normalize-path": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/normalize-path/download/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "dev": true, "optional": true}, "npm-run-path": {"version": "2.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/npm-run-path/download/npm-run-path-2.0.2.tgz", "integrity": "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=", "dev": true, "requires": {"path-key": "^2.0.0"}, "dependencies": {"path-key": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-key/download/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=", "dev": true}}}, "nwsapi": {"version": "2.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/nwsapi/download/nwsapi-2.2.0.tgz", "integrity": "sha1-IEh5qePQaP8qVROcLHcngGgaOLc=", "dev": true}, "oauth-sign": {"version": "0.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/oauth-sign/download/oauth-sign-0.9.0.tgz", "integrity": "sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=", "dev": true}, "object-assign": {"version": "4.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object-assign/download/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true}, "object-copy": {"version": "0.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object-copy/download/object-copy-0.1.0.tgz", "integrity": "sha1-fn2Fi3gb18mRpBupde04EnVOmYw=", "dev": true, "requires": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/download/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "kind-of": {"version": "3.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "object-inspect": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object-inspect/download/object-inspect-1.9.0.tgz", "integrity": "sha1-yQUh104RJ7ZyZt7TOUrWEWmGUzo=", "dev": true}, "object-keys": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object-keys/download/object-keys-1.1.1.tgz", "integrity": "sha1-HEfyct8nfzsdrwYWd9nILiMixg4=", "dev": true}, "object-visit": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object-visit/download/object-visit-1.0.1.tgz", "integrity": "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=", "dev": true, "requires": {"isobject": "^3.0.0"}}, "object.assign": {"version": "4.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object.assign/download/object.assign-4.1.2.tgz", "integrity": "sha1-DtVKNC7Os3s4/3brgxoOeIy2OUA=", "dev": true, "requires": {"call-bind": "^1.0.0", "define-properties": "^1.1.3", "has-symbols": "^1.0.1", "object-keys": "^1.1.1"}}, "object.getownpropertydescriptors": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.1.1.tgz", "integrity": "sha1-Df2o0QgHTZxWPoBJDIg7ZmEJFUQ=", "dev": true, "requires": {"call-bind": "^1.0.0", "define-properties": "^1.1.3", "es-abstract": "^1.18.0-next.1"}, "dependencies": {"es-abstract": {"version": "1.18.0-next.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/es-abstract/download/es-abstract-1.18.0-next.1.tgz", "integrity": "sha1-bjoKS9pxflAjqzuOkL7DYQjSLGg=", "dev": true, "requires": {"es-to-primitive": "^1.2.1", "function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1", "is-callable": "^1.2.2", "is-negative-zero": "^2.0.0", "is-regex": "^1.1.1", "object-inspect": "^1.8.0", "object-keys": "^1.1.1", "object.assign": "^4.1.1", "string.prototype.trimend": "^1.0.1", "string.prototype.trimstart": "^1.0.1"}}}}, "object.pick": {"version": "1.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object.pick/download/object.pick-1.3.0.tgz", "integrity": "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=", "dev": true, "requires": {"isobject": "^3.0.1"}}, "once": {"version": "1.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/once/download/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "dev": true, "requires": {"wrappy": "1"}}, "optionator": {"version": "0.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/optionator/download/optionator-0.8.3.tgz", "integrity": "sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=", "dev": true, "requires": {"deep-is": "~0.1.3", "fast-levenshtein": "~2.0.6", "levn": "~0.3.0", "prelude-ls": "~1.1.2", "type-check": "~0.3.2", "word-wrap": "~1.2.3"}}, "os-browserify": {"version": "0.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/os-browserify/download/os-browserify-0.3.0.tgz", "integrity": "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=", "dev": true}, "os-tmpdir": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/os-tmpdir/download/os-tmpdir-1.0.2.tgz", "integrity": "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=", "dev": true}, "p-each-series": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-each-series/download/p-each-series-1.0.0.tgz", "integrity": "sha1-kw89Et0fUOdDRFeiLNbwSsatf3E=", "dev": true, "requires": {"p-reduce": "^1.0.0"}}, "p-finally": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-finally/download/p-finally-1.0.0.tgz", "integrity": "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=", "dev": true}, "p-limit": {"version": "2.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-limit/download/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-locate/download/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "dev": true, "requires": {"p-limit": "^2.2.0"}}, "p-reduce": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-reduce/download/p-reduce-1.0.0.tgz", "integrity": "sha1-GMKw3ZNqRpClKfgjH1ig/bakffo=", "dev": true}, "p-try": {"version": "2.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-try/download/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "dev": true}, "pako": {"version": "1.0.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pako/download/pako-1.0.11.tgz", "integrity": "sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=", "dev": true}, "parallel-transform": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/parallel-transform/download/parallel-transform-1.2.0.tgz", "integrity": "sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw=", "dev": true, "requires": {"cyclist": "^1.0.1", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}}, "parse-asn1": {"version": "5.1.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/parse-asn1/download/parse-asn1-5.1.6.tgz", "integrity": "sha1-OFCAo+wTy2KmLTlAnLPoiETNrtQ=", "dev": true, "requires": {"asn1.js": "^5.2.0", "browserify-aes": "^1.0.0", "evp_bytestokey": "^1.0.0", "pbkdf2": "^3.0.3", "safe-buffer": "^5.1.1"}}, "parse-json": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/parse-json/download/parse-json-4.0.0.tgz", "integrity": "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=", "dev": true, "requires": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}}, "parse-passwd": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/parse-passwd/download/parse-passwd-1.0.0.tgz", "integrity": "sha1-bVuTSkVpk7I9N/QKOC1vFmao5cY=", "dev": true}, "parse5": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/parse5/download/parse5-4.0.0.tgz", "integrity": "sha1-bXhlbj2o14tOwLkG98CO8d/j9gg=", "dev": true}, "pascalcase": {"version": "0.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pascalcase/download/pascalcase-0.1.1.tgz", "integrity": "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=", "dev": true}, "path-browserify": {"version": "0.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-browserify/download/path-browserify-0.0.1.tgz", "integrity": "sha1-5sTd1+06onxoogzE5Q4aTug7vEo=", "dev": true}, "path-dirname": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-dirname/download/path-dirname-1.0.2.tgz", "integrity": "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=", "dev": true, "optional": true}, "path-exists": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-exists/download/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "dev": true}, "path-is-absolute": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-is-absolute/download/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "dev": true}, "path-key": {"version": "3.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-key/download/path-key-3.1.1.tgz", "integrity": "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U="}, "path-parse": {"version": "1.0.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-parse/download/path-parse-1.0.6.tgz", "integrity": "sha1-1i27VnlAXXLEc37FhgDp3c8G0kw=", "dev": true}, "path-type": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-type/download/path-type-3.0.0.tgz", "integrity": "sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=", "dev": true, "requires": {"pify": "^3.0.0"}, "dependencies": {"pify": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pify/download/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true}}}, "pbkdf2": {"version": "3.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pbkdf2/download/pbkdf2-3.1.1.tgz", "integrity": "sha1-y4cksPramEWWhW0abrr9NYRlS5Q=", "dev": true, "requires": {"create-hash": "^1.1.2", "create-hmac": "^1.1.4", "ripemd160": "^2.0.1", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "performance-now": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/performance-now/download/performance-now-2.1.0.tgz", "integrity": "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=", "dev": true}, "picocolors": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/picocolors/-/picocolors-1.0.0.tgz", "integrity": "sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=", "dev": true}, "picomatch": {"version": "2.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/picomatch/download/picomatch-2.2.2.tgz", "integrity": "sha1-IfMz6ba46v8CRo9RRupAbTRfTa0=", "dev": true, "optional": true}, "pify": {"version": "4.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pify/download/pify-4.0.1.tgz", "integrity": "sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=", "dev": true}, "pirates": {"version": "4.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pirates/download/pirates-4.0.1.tgz", "integrity": "sha1-ZDqSyviUVm+RsrmG0sZpUKji+4c=", "dev": true, "requires": {"node-modules-regexp": "^1.0.0"}}, "pkg-dir": {"version": "4.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pkg-dir/download/pkg-dir-4.2.0.tgz", "integrity": "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=", "dev": true, "requires": {"find-up": "^4.0.0"}}, "pn": {"version": "1.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pn/download/pn-1.1.0.tgz", "integrity": "sha1-4vTO8OIZ9GPBeas3Rj5OHs3Muvs=", "dev": true}, "posix-character-classes": {"version": "0.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/posix-character-classes/download/posix-character-classes-0.1.1.tgz", "integrity": "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=", "dev": true}, "prelude-ls": {"version": "1.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/prelude-ls/download/prelude-ls-1.1.2.tgz", "integrity": "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=", "dev": true}, "prettier": {"version": "1.19.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/prettier/download/prettier-1.19.1.tgz", "integrity": "sha1-99f1/4qc2HKnvkyhQglZVqYHl8s=", "dev": true}, "prettier-webpack-plugin": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/prettier-webpack-plugin/download/prettier-webpack-plugin-1.2.0.tgz", "integrity": "sha1-SlY18FR0EWCTbH9QnwqbH5KOVVA=", "dev": true}, "pretty-format": {"version": "24.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pretty-format/download/pretty-format-24.9.0.tgz", "integrity": "sha1-EvrDGzcBmk7qPBGqmpWet2KKp8k=", "dev": true, "requires": {"@jest/types": "^24.9.0", "ansi-regex": "^4.0.0", "ansi-styles": "^3.2.0", "react-is": "^16.8.4"}}, "process": {"version": "0.11.10", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/process/download/process-0.11.10.tgz", "integrity": "sha1-czIwDoQBYb2j5podHZGn1LwW8YI=", "dev": true}, "process-nextick-args": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/process-nextick-args/download/process-nextick-args-2.0.1.tgz", "integrity": "sha1-eCDZsWEgzFXKmud5JoCufbptf+I=", "dev": true}, "promise-inflight": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/promise-inflight/download/promise-inflight-1.0.1.tgz", "integrity": "sha1-mEcocL8igTL8vdhoEputEsPAKeM=", "dev": true}, "prompts": {"version": "2.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/prompts/download/prompts-2.4.0.tgz", "integrity": "sha1-SqXeByOiMdHukSHED99mPfc/Ydc=", "dev": true, "requires": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}}, "prr": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/prr/download/prr-1.0.1.tgz", "integrity": "sha1-0/wRS6BplaRexok/SEzrHXj19HY=", "dev": true}, "psl": {"version": "1.8.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/psl/download/psl-1.8.0.tgz", "integrity": "sha1-kyb4vPsBOtzABf3/BWrM4CDlHCQ=", "dev": true}, "public-encrypt": {"version": "4.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/public-encrypt/download/public-encrypt-4.0.3.tgz", "integrity": "sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=", "dev": true, "requires": {"bn.js": "^4.1.0", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "parse-asn1": "^5.0.0", "randombytes": "^2.0.1", "safe-buffer": "^5.1.2"}, "dependencies": {"bn.js": {"version": "4.11.9", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bn.js/download/bn.js-4.11.9.tgz", "integrity": "sha1-JtVWgpRY+dHoH8SJUkk9C6NQeCg=", "dev": true}}}, "pump": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pump/download/pump-3.0.0.tgz", "integrity": "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=", "dev": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "pumpify": {"version": "1.5.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pumpify/download/pumpify-1.5.1.tgz", "integrity": "sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4=", "dev": true, "requires": {"duplexify": "^3.6.0", "inherits": "^2.0.3", "pump": "^2.0.0"}, "dependencies": {"pump": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pump/download/pump-2.0.1.tgz", "integrity": "sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=", "dev": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}}}, "punycode": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/punycode/download/punycode-2.1.1.tgz", "integrity": "sha1-tYsBCsQMIsVldhbI0sLALHv0eew=", "dev": true}, "qs": {"version": "6.5.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/qs/download/qs-6.5.2.tgz", "integrity": "sha1-yzroBuh0BERYTvFUzo7pjUA/PjY=", "dev": true}, "querystring": {"version": "0.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/querystring/download/querystring-0.2.0.tgz", "integrity": "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=", "dev": true}, "querystring-es3": {"version": "0.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/querystring-es3/download/querystring-es3-0.2.1.tgz", "integrity": "sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=", "dev": true}, "randombytes": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/randombytes/download/randombytes-2.1.0.tgz", "integrity": "sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=", "dev": true, "requires": {"safe-buffer": "^5.1.0"}}, "randomfill": {"version": "1.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/randomfill/download/randomfill-1.0.4.tgz", "integrity": "sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=", "dev": true, "requires": {"randombytes": "^2.0.5", "safe-buffer": "^5.1.0"}}, "react-is": {"version": "16.13.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/react-is/download/react-is-16.13.1.tgz", "integrity": "sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=", "dev": true}, "read-pkg": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/read-pkg/download/read-pkg-3.0.0.tgz", "integrity": "sha1-nLxoaXj+5l0WwA4rGcI3/Pbjg4k=", "dev": true, "requires": {"load-json-file": "^4.0.0", "normalize-package-data": "^2.3.2", "path-type": "^3.0.0"}}, "read-pkg-up": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/read-pkg-up/download/read-pkg-up-4.0.0.tgz", "integrity": "sha1-GyIcYIi6d5lgHICPkRYcZuWPiXg=", "dev": true, "requires": {"find-up": "^3.0.0", "read-pkg": "^3.0.0"}, "dependencies": {"find-up": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/find-up/download/find-up-3.0.0.tgz", "integrity": "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=", "dev": true, "requires": {"locate-path": "^3.0.0"}}, "locate-path": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/locate-path/download/locate-path-3.0.0.tgz", "integrity": "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=", "dev": true, "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "p-locate": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-locate/download/p-locate-3.0.0.tgz", "integrity": "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=", "dev": true, "requires": {"p-limit": "^2.0.0"}}, "path-exists": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-exists/download/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "dev": true}}}, "readable-stream": {"version": "2.3.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/readable-stream/download/readable-stream-2.3.7.tgz", "integrity": "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "readdirp": {"version": "2.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/readdirp/download/readdirp-2.2.1.tgz", "integrity": "sha1-DodiKjMlqjPokihcr4tOhGUppSU=", "dev": true, "optional": true, "requires": {"graceful-fs": "^4.1.11", "micromatch": "^3.1.10", "readable-stream": "^2.0.2"}}, "realpath-native": {"version": "1.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/realpath-native/download/realpath-native-1.1.0.tgz", "integrity": "sha1-IAMpT+oj+wZy8kduviL89Jii1lw=", "dev": true, "requires": {"util.promisify": "^1.0.0"}}, "regenerate": {"version": "1.4.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/regenerate/-/regenerate-1.4.2.tgz", "integrity": "sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=", "dev": true}, "regenerate-unicode-properties": {"version": "9.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/regenerate-unicode-properties/-/regenerate-unicode-properties-9.0.0.tgz", "integrity": "sha1-VNCccRXh9T3CMUqXSzLBw0Tv4yY=", "dev": true, "requires": {"regenerate": "^1.4.2"}}, "regenerator-runtime": {"version": "0.13.9", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/regenerator-runtime/-/regenerator-runtime-0.13.9.tgz", "integrity": "sha1-iSV0Kpj/2QgUmI11Zq0wyjsmO1I="}, "regenerator-transform": {"version": "0.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/regenerator-transform/-/regenerator-transform-0.14.5.tgz", "integrity": "sha1-yY2hVGg2ccnE3LFuznNlF+G3/rQ=", "dev": true, "requires": {"@babel/runtime": "^7.8.4"}}, "regex-not": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/regex-not/download/regex-not-1.0.2.tgz", "integrity": "sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=", "dev": true, "requires": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}}, "regexpu-core": {"version": "4.8.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/regexpu-core/-/regexpu-core-4.8.0.tgz", "integrity": "sha1-5WBbo2G2excYR4UBMnUC9EeamPA=", "dev": true, "requires": {"regenerate": "^1.4.2", "regenerate-unicode-properties": "^9.0.0", "regjsgen": "^0.5.2", "regjsparser": "^0.7.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.0.0"}}, "regjsgen": {"version": "0.5.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/regjsgen/-/regjsgen-0.5.2.tgz", "integrity": "sha1-kv8pX7He7L9uzaslQ9IH6RqjNzM=", "dev": true}, "regjsparser": {"version": "0.7.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/regjsparser/-/regjsparser-0.7.0.tgz", "integrity": "sha1-prZntUyIXhi1JVTLSWDvcRh+mWg=", "dev": true, "requires": {"jsesc": "~0.5.0"}, "dependencies": {"jsesc": {"version": "0.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jsesc/-/jsesc-0.5.0.tgz", "integrity": "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=", "dev": true}}}, "remove-trailing-separator": {"version": "1.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz", "integrity": "sha1-wkvOKig62tW8P1jg1IJJuSN52O8=", "dev": true}, "repeat-element": {"version": "1.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/repeat-element/download/repeat-element-1.1.3.tgz", "integrity": "sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4=", "dev": true}, "repeat-string": {"version": "1.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/repeat-string/download/repeat-string-1.6.1.tgz", "integrity": "sha1-jcrkcOHIirwtYA//Sndihtp15jc=", "dev": true}, "request": {"version": "2.88.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/request/download/request-2.88.2.tgz", "integrity": "sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=", "dev": true, "requires": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.3", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.5.0", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}}, "request-promise-core": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/request-promise-core/download/request-promise-core-1.1.4.tgz", "integrity": "sha1-Pu3UIjII1BmGe3jOgVFn0QWToi8=", "dev": true, "requires": {"lodash": "^4.17.19"}}, "request-promise-native": {"version": "1.0.9", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/request-promise-native/download/request-promise-native-1.0.9.tgz", "integrity": "sha1-5AcSBSal79yaObKKVnm/R7nZ3Cg=", "dev": true, "requires": {"request-promise-core": "1.1.4", "stealthy-require": "^1.1.1", "tough-cookie": "^2.3.3"}}, "require-directory": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/require-directory/download/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I=", "dev": true}, "require-main-filename": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/require-main-filename/download/require-main-filename-2.0.0.tgz", "integrity": "sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=", "dev": true}, "resolve": {"version": "1.19.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/resolve/download/resolve-1.19.0.tgz", "integrity": "sha1-GvW/YwQJc0oGfK4pMYqsf6KaJnw=", "dev": true, "requires": {"is-core-module": "^2.1.0", "path-parse": "^1.0.6"}}, "resolve-cwd": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/resolve-cwd/download/resolve-cwd-2.0.0.tgz", "integrity": "sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=", "dev": true, "requires": {"resolve-from": "^3.0.0"}}, "resolve-dir": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/resolve-dir/download/resolve-dir-1.0.1.tgz", "integrity": "sha1-eaQGRMNivoLybv/nOcm7U4IEb0M=", "dev": true, "requires": {"expand-tilde": "^2.0.0", "global-modules": "^1.0.0"}, "dependencies": {"global-modules": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/global-modules/download/global-modules-1.0.0.tgz", "integrity": "sha1-bXcPDrUjrHgWTXK15xqIdyZcw+o=", "dev": true, "requires": {"global-prefix": "^1.0.1", "is-windows": "^1.0.1", "resolve-dir": "^1.0.0"}}}}, "resolve-from": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/resolve-from/download/resolve-from-3.0.0.tgz", "integrity": "sha1-six699nWiBvItuZTM17rywoYh0g=", "dev": true}, "resolve-url": {"version": "0.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/resolve-url/download/resolve-url-0.2.1.tgz", "integrity": "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=", "dev": true}, "ret": {"version": "0.1.15", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ret/download/ret-0.1.15.tgz", "integrity": "sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=", "dev": true}, "rimraf": {"version": "2.7.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/rimraf/download/rimraf-2.7.1.tgz", "integrity": "sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=", "dev": true, "requires": {"glob": "^7.1.3"}}, "ripemd160": {"version": "2.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ripemd160/download/ripemd160-2.0.2.tgz", "integrity": "sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=", "dev": true, "requires": {"hash-base": "^3.0.0", "inherits": "^2.0.1"}}, "rsvp": {"version": "4.8.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/rsvp/download/rsvp-4.8.5.tgz", "integrity": "sha1-yPFVMR0Wf2jyHhaN9x7FsIMRNzQ=", "dev": true}, "run-queue": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/run-queue/download/run-queue-1.0.3.tgz", "integrity": "sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=", "dev": true, "requires": {"aproba": "^1.1.1"}}, "safe-buffer": {"version": "5.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/safe-buffer/download/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "dev": true}, "safe-regex": {"version": "1.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/safe-regex/download/safe-regex-1.1.0.tgz", "integrity": "sha1-QKNmnzsHfR6UPURinhV91IAjvy4=", "dev": true, "requires": {"ret": "~0.1.10"}}, "safer-buffer": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/safer-buffer/download/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "dev": true}, "sane": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/sane/download/sane-4.1.0.tgz", "integrity": "sha1-7Ygf2SJzOmxGG8GJ3CtsAG8//e0=", "dev": true, "requires": {"@cnakazawa/watch": "^1.0.3", "anymatch": "^2.0.0", "capture-exit": "^2.0.0", "exec-sh": "^0.3.2", "execa": "^1.0.0", "fb-watchman": "^2.0.0", "micromatch": "^3.1.4", "minimist": "^1.1.1", "walker": "~1.0.5"}}, "sax": {"version": "1.2.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/sax/download/sax-1.2.4.tgz", "integrity": "sha1-KBYjTiN4vdxOU1T6tcqold9xANk=", "dev": true}, "schema-utils": {"version": "2.7.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/schema-utils/download/schema-utils-2.7.1.tgz", "integrity": "sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=", "dev": true, "requires": {"@types/json-schema": "^7.0.5", "ajv": "^6.12.4", "ajv-keywords": "^3.5.2"}}, "semver": {"version": "5.7.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/download/semver-5.7.1.tgz", "integrity": "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=", "dev": true}, "serialize-javascript": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/serialize-javascript/download/serialize-javascript-4.0.0.tgz", "integrity": "sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao=", "dev": true, "requires": {"randombytes": "^2.1.0"}}, "set-blocking": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/set-blocking/download/set-blocking-2.0.0.tgz", "integrity": "sha1-BF+XgtARrppoA93TgrJDkrPYkPc=", "dev": true}, "set-value": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/set-value/download/set-value-2.0.1.tgz", "integrity": "sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=", "dev": true, "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "setimmediate": {"version": "1.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/setimmediate/download/setimmediate-1.0.5.tgz", "integrity": "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=", "dev": true}, "sha.js": {"version": "2.4.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/sha.js/download/sha.js-2.4.11.tgz", "integrity": "sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=", "dev": true, "requires": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "shebang-command": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/shebang-command/download/shebang-command-2.0.0.tgz", "integrity": "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=", "requires": {"shebang-regex": "^3.0.0"}}, "shebang-regex": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/shebang-regex/download/shebang-regex-3.0.0.tgz", "integrity": "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI="}, "shellwords": {"version": "0.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/shellwords/download/shellwords-0.1.1.tgz", "integrity": "sha1-1rkYHBpI05cyTISHHvvPxz/AZUs=", "dev": true}, "signal-exit": {"version": "3.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/signal-exit/download/signal-exit-3.0.3.tgz", "integrity": "sha1-oUEMLt2PB3sItOJTyOrPyvBXRhw=", "dev": true}, "sisteransi": {"version": "1.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/sisteransi/download/sisteransi-1.0.5.tgz", "integrity": "sha1-E01oEpd1ZDfMBcoBNw06elcQde0=", "dev": true}, "slash": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/slash/download/slash-2.0.0.tgz", "integrity": "sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=", "dev": true}, "snapdragon": {"version": "0.8.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/snapdragon/download/snapdragon-0.8.2.tgz", "integrity": "sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=", "dev": true, "requires": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/download/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "snapdragon-node": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/snapdragon-node/download/snapdragon-node-2.1.1.tgz", "integrity": "sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=", "dev": true, "requires": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/download/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-descriptor/download/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "snapdragon-util": {"version": "3.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/snapdragon-util/download/snapdragon-util-3.0.1.tgz", "integrity": "sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=", "dev": true, "requires": {"kind-of": "^3.2.0"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "source-list-map": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-list-map/download/source-list-map-2.0.1.tgz", "integrity": "sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=", "dev": true}, "source-map": {"version": "0.5.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/download/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true}, "source-map-resolve": {"version": "0.5.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map-resolve/download/source-map-resolve-0.5.3.tgz", "integrity": "sha1-GQhmvs51U+H48mei7oLGBrVQmho=", "dev": true, "requires": {"atob": "^2.1.2", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "source-map-support": {"version": "0.5.19", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map-support/download/source-map-support-0.5.19.tgz", "integrity": "sha1-qYti+G3K9PZzmWSMCFKRq56P7WE=", "dev": true, "requires": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "source-map-url": {"version": "0.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map-url/download/source-map-url-0.4.0.tgz", "integrity": "sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=", "dev": true}, "spdx-correct": {"version": "3.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/spdx-correct/download/spdx-correct-3.1.1.tgz", "integrity": "sha1-3s6BrJweZxPl99G28X1Gj6U9iak=", "dev": true, "requires": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "spdx-exceptions": {"version": "2.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz", "integrity": "sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=", "dev": true}, "spdx-expression-parse": {"version": "3.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz", "integrity": "sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=", "dev": true, "requires": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "spdx-license-ids": {"version": "3.0.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/spdx-license-ids/download/spdx-license-ids-3.0.7.tgz", "integrity": "sha1-6cGKQQ5e1+EkQqVJ+9ivp2cDjWU=", "dev": true}, "split-string": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/split-string/download/split-string-3.1.0.tgz", "integrity": "sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=", "dev": true, "requires": {"extend-shallow": "^3.0.0"}}, "sshpk": {"version": "1.16.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/sshpk/download/sshpk-1.16.1.tgz", "integrity": "sha1-+2YcC+8ps520B2nuOfpwCT1vaHc=", "dev": true, "requires": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}}, "ssri": {"version": "6.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ssri/download/ssri-6.0.1.tgz", "integrity": "sha1-KjxBso3UW2K2Nnbst0ABJlrp7dg=", "dev": true, "requires": {"figgy-pudding": "^3.5.1"}}, "stack-utils": {"version": "1.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/stack-utils/download/stack-utils-1.0.4.tgz", "integrity": "sha1-S2AJcdz8au0MvfKoJoF3zJFsh8g=", "dev": true, "requires": {"escape-string-regexp": "^2.0.0"}, "dependencies": {"escape-string-regexp": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/escape-string-regexp/download/escape-string-regexp-2.0.0.tgz", "integrity": "sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=", "dev": true}}}, "static-extend": {"version": "0.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/static-extend/download/static-extend-0.1.2.tgz", "integrity": "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=", "dev": true, "requires": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/download/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}}}, "stealthy-require": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/stealthy-require/download/stealthy-require-1.1.1.tgz", "integrity": "sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks=", "dev": true}, "stream-browserify": {"version": "2.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/stream-browserify/download/stream-browserify-2.0.2.tgz", "integrity": "sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs=", "dev": true, "requires": {"inherits": "~2.0.1", "readable-stream": "^2.0.2"}}, "stream-each": {"version": "1.2.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/stream-each/download/stream-each-1.2.3.tgz", "integrity": "sha1-6+J6DDibBPvMIzZClS4Qcxr6m64=", "dev": true, "requires": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}}, "stream-http": {"version": "2.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/stream-http/download/stream-http-2.8.3.tgz", "integrity": "sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw=", "dev": true, "requires": {"builtin-status-codes": "^3.0.0", "inherits": "^2.0.1", "readable-stream": "^2.3.6", "to-arraybuffer": "^1.0.0", "xtend": "^4.0.0"}}, "stream-shift": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/stream-shift/download/stream-shift-1.0.1.tgz", "integrity": "sha1-1wiCgVWasneEJCebCHfaPDktWj0=", "dev": true}, "string-length": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/string-length/download/string-length-2.0.0.tgz", "integrity": "sha1-1A27aGo6zpYMHP/KVivyxF+DY+0=", "dev": true, "requires": {"astral-regex": "^1.0.0", "strip-ansi": "^4.0.0"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-regex/download/ansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=", "dev": true}, "strip-ansi": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/strip-ansi/download/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "dev": true, "requires": {"ansi-regex": "^3.0.0"}}}}, "string-width": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/string-width/download/string-width-3.1.0.tgz", "integrity": "sha1-InZ74htirxCBV0MG9prFG2IgOWE=", "dev": true, "requires": {"emoji-regex": "^7.0.1", "is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^5.1.0"}}, "string.prototype.trimend": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/string.prototype.trimend/download/string.prototype.trimend-1.0.3.tgz", "integrity": "sha1-oivVPMpcfPRNfJ1ccyEYhz1s0Ys=", "dev": true, "requires": {"call-bind": "^1.0.0", "define-properties": "^1.1.3"}}, "string.prototype.trimstart": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/string.prototype.trimstart/download/string.prototype.trimstart-1.0.3.tgz", "integrity": "sha1-m0y1kOEjuzZWRAHVmCQpjeUP1ao=", "dev": true, "requires": {"call-bind": "^1.0.0", "define-properties": "^1.1.3"}}, "string_decoder": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/string_decoder/download/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "requires": {"safe-buffer": "~5.1.0"}}, "strip-ansi": {"version": "5.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/strip-ansi/download/strip-ansi-5.2.0.tgz", "integrity": "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=", "dev": true, "requires": {"ansi-regex": "^4.1.0"}}, "strip-bom": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/strip-bom/-/strip-bom-3.0.0.tgz", "integrity": "sha1-NG/BXiSPQ1MJZnZSY1Gb01Kpbxk=", "dev": true}, "strip-eof": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/strip-eof/download/strip-eof-1.0.0.tgz", "integrity": "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=", "dev": true}, "supports-color": {"version": "5.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "dev": true, "requires": {"has-flag": "^3.0.0"}}, "symbol-tree": {"version": "3.2.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/symbol-tree/download/symbol-tree-3.2.4.tgz", "integrity": "sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=", "dev": true}, "tapable": {"version": "1.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tapable/download/tapable-1.1.3.tgz", "integrity": "sha1-ofzMBrWNth/XpF2i2kT186Pme6I=", "dev": true}, "terser": {"version": "4.8.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/terser/download/terser-4.8.0.tgz", "integrity": "sha1-YwVjQ9fHC7KfOvZlhlpG/gOg3xc=", "dev": true, "requires": {"commander": "^2.20.0", "source-map": "~0.6.1", "source-map-support": "~0.5.12"}, "dependencies": {"commander": {"version": "2.20.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/commander/download/commander-2.20.3.tgz", "integrity": "sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=", "dev": true}, "source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "terser-webpack-plugin": {"version": "1.4.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/terser-webpack-plugin/download/terser-webpack-plugin-1.4.5.tgz", "integrity": "sha1-oheu+uozDnNP+sthIOwfoxLWBAs=", "dev": true, "requires": {"cacache": "^12.0.2", "find-cache-dir": "^2.1.0", "is-wsl": "^1.1.0", "schema-utils": "^1.0.0", "serialize-javascript": "^4.0.0", "source-map": "^0.6.1", "terser": "^4.1.2", "webpack-sources": "^1.4.0", "worker-farm": "^1.7.0"}, "dependencies": {"find-cache-dir": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/find-cache-dir/download/find-cache-dir-2.1.0.tgz", "integrity": "sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=", "dev": true, "requires": {"commondir": "^1.0.1", "make-dir": "^2.0.0", "pkg-dir": "^3.0.0"}}, "find-up": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/find-up/download/find-up-3.0.0.tgz", "integrity": "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=", "dev": true, "requires": {"locate-path": "^3.0.0"}}, "locate-path": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/locate-path/download/locate-path-3.0.0.tgz", "integrity": "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=", "dev": true, "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "p-locate": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-locate/download/p-locate-3.0.0.tgz", "integrity": "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=", "dev": true, "requires": {"p-limit": "^2.0.0"}}, "path-exists": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-exists/download/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "dev": true}, "pkg-dir": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pkg-dir/download/pkg-dir-3.0.0.tgz", "integrity": "sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=", "dev": true, "requires": {"find-up": "^3.0.0"}}, "schema-utils": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/schema-utils/download/schema-utils-1.0.0.tgz", "integrity": "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=", "dev": true, "requires": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}}, "source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "test-exclude": {"version": "5.2.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/test-exclude/download/test-exclude-5.2.3.tgz", "integrity": "sha1-w9Ph4xHrfuQF4JLawQrv0JCR6sA=", "dev": true, "requires": {"glob": "^7.1.3", "minimatch": "^3.0.4", "read-pkg-up": "^4.0.0", "require-main-filename": "^2.0.0"}}, "throat": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/throat/download/throat-4.1.0.tgz", "integrity": "sha1-iQN8vJLFarGJJua6TLsgDhVnKmo=", "dev": true}, "through2": {"version": "2.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/through2/download/through2-2.0.5.tgz", "integrity": "sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=", "dev": true, "requires": {"readable-stream": "~2.3.6", "xtend": "~4.0.1"}}, "timers-browserify": {"version": "2.0.12", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/timers-browserify/download/timers-browserify-2.0.12.tgz", "integrity": "sha1-RKRcEfv0B/NPl7zNFXfGUjYbAO4=", "dev": true, "requires": {"setimmediate": "^1.0.4"}}, "tmp": {"version": "0.0.29", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tmp/download/tmp-0.0.29.tgz", "integrity": "sha1-8lEl/w3Z2jzLDC3Tce4SiLuRKMA=", "dev": true, "requires": {"os-tmpdir": "~1.0.1"}}, "tmpl": {"version": "1.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tmpl/download/tmpl-1.0.4.tgz", "integrity": "sha1-I2QN17QtAEM5ERQIIOXPRA5SHdE=", "dev": true}, "to-arraybuffer": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz", "integrity": "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=", "dev": true}, "to-fast-properties": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/to-fast-properties/download/to-fast-properties-2.0.0.tgz", "integrity": "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=", "dev": true}, "to-object-path": {"version": "0.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/to-object-path/download/to-object-path-0.3.0.tgz", "integrity": "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "to-regex": {"version": "3.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/to-regex/download/to-regex-3.0.2.tgz", "integrity": "sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=", "dev": true, "requires": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}}, "to-regex-range": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/to-regex-range/download/to-regex-range-2.1.1.tgz", "integrity": "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=", "dev": true, "requires": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}}, "tough-cookie": {"version": "2.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tough-cookie/download/tough-cookie-2.5.0.tgz", "integrity": "sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=", "dev": true, "requires": {"psl": "^1.1.28", "punycode": "^2.1.1"}}, "tr46": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tr46/download/tr46-1.0.1.tgz", "integrity": "sha1-qLE/1r/SSJUZZ0zN5VujaTtwbQk=", "dev": true, "requires": {"punycode": "^2.1.0"}}, "tslib": {"version": "1.14.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tslib/download/tslib-1.14.1.tgz", "integrity": "sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=", "dev": true}, "tty-browserify": {"version": "0.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tty-browserify/download/tty-browserify-0.0.0.tgz", "integrity": "sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=", "dev": true}, "tunnel-agent": {"version": "0.6.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tunnel-agent/download/tunnel-agent-0.6.0.tgz", "integrity": "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=", "dev": true, "requires": {"safe-buffer": "^5.0.1"}}, "tweetnacl": {"version": "0.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tweetnacl/download/tweetnacl-0.14.5.tgz", "integrity": "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=", "dev": true}, "type-check": {"version": "0.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/type-check/download/type-check-0.3.2.tgz", "integrity": "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=", "dev": true, "requires": {"prelude-ls": "~1.1.2"}}, "typedarray": {"version": "0.0.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/typedarray/download/typedarray-0.0.6.tgz", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=", "dev": true}, "uglify-es": {"version": "3.3.9", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/uglify-es/download/uglify-es-3.3.9.tgz", "integrity": "sha1-DBxPBwC+2NvBJM2zBNJZLKID5nc=", "dev": true, "requires": {"commander": "~2.13.0", "source-map": "~0.6.1"}, "dependencies": {"commander": {"version": "2.13.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/commander/download/commander-2.13.0.tgz", "integrity": "sha1-aWS8pnaF33wfFDDFhPB9dZeIW5w=", "dev": true}, "source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "unicode-canonical-property-names-ecmascript": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz", "integrity": "sha1-MBrNxSVjFnDTn2FG4Od/9rvevdw=", "dev": true}, "unicode-match-property-ecmascript": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz", "integrity": "sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=", "dev": true, "requires": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}}, "unicode-match-property-value-ecmascript": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.0.0.tgz", "integrity": "sha1-GgGqVyR8FMVouJd1pUk4eIGJpxQ=", "dev": true}, "unicode-property-aliases-ecmascript": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.0.0.tgz", "integrity": "sha1-CjbLmlhcT2q9Ua0d7dsoXBZSl8g=", "dev": true}, "union-value": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/union-value/download/union-value-1.0.1.tgz", "integrity": "sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=", "dev": true, "requires": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^2.0.1"}}, "unique-filename": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/unique-filename/download/unique-filename-1.1.1.tgz", "integrity": "sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=", "dev": true, "requires": {"unique-slug": "^2.0.0"}}, "unique-slug": {"version": "2.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/unique-slug/download/unique-slug-2.0.2.tgz", "integrity": "sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=", "dev": true, "requires": {"imurmurhash": "^0.1.4"}}, "unset-value": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/unset-value/download/unset-value-1.0.0.tgz", "integrity": "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=", "dev": true, "requires": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "dependencies": {"has-value": {"version": "0.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-value/download/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "dev": true, "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "dependencies": {"isobject": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/isobject/download/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "dev": true, "requires": {"isarray": "1.0.0"}}}}, "has-values": {"version": "0.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-values/download/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E=", "dev": true}}}, "upath": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/upath/download/upath-1.2.0.tgz", "integrity": "sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=", "dev": true, "optional": true}, "uri-js": {"version": "4.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/uri-js/download/uri-js-4.4.0.tgz", "integrity": "sha1-qnFCYd55PoqCNHp7zJznTobyhgI=", "dev": true, "requires": {"punycode": "^2.1.0"}}, "urix": {"version": "0.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/urix/download/urix-0.1.0.tgz", "integrity": "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=", "dev": true}, "url": {"version": "0.11.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/url/download/url-0.11.0.tgz", "integrity": "sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=", "dev": true, "requires": {"punycode": "1.3.2", "querystring": "0.2.0"}, "dependencies": {"punycode": {"version": "1.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/punycode/download/punycode-1.3.2.tgz", "integrity": "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=", "dev": true}}}, "url-loader": {"version": "2.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/url-loader/download/url-loader-2.3.0.tgz", "integrity": "sha1-4OLvZY8APvuMpBsPP/v3a6uIZYs=", "dev": true, "requires": {"loader-utils": "^1.2.3", "mime": "^2.4.4", "schema-utils": "^2.5.0"}}, "use": {"version": "3.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/use/download/use-3.1.1.tgz", "integrity": "sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=", "dev": true}, "util": {"version": "0.11.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/util/download/util-0.11.1.tgz", "integrity": "sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE=", "dev": true, "requires": {"inherits": "2.0.3"}, "dependencies": {"inherits": {"version": "2.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/inherits/download/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=", "dev": true}}}, "util-deprecate": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/util-deprecate/download/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "dev": true}, "util.promisify": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/util.promisify/download/util.promisify-1.0.1.tgz", "integrity": "sha1-a693dLgO6w91INi4HQeYKlmruu4=", "dev": true, "requires": {"define-properties": "^1.1.3", "es-abstract": "^1.17.2", "has-symbols": "^1.0.1", "object.getownpropertydescriptors": "^2.1.0"}}, "uuid": {"version": "3.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/uuid/download/uuid-3.4.0.tgz", "integrity": "sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=", "dev": true}, "v8-compile-cache": {"version": "2.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/v8-compile-cache/download/v8-compile-cache-2.2.0.tgz", "integrity": "sha1-lHHvo++RKNL3xqfKOcTda1BVsTI=", "dev": true}, "validate-npm-package-license": {"version": "3.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz", "integrity": "sha1-/JH2uce6FchX9MssXe/uw51PQQo=", "dev": true, "requires": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "verror": {"version": "1.10.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/verror/download/verror-1.10.0.tgz", "integrity": "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=", "dev": true, "requires": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "vm-browserify": {"version": "1.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/vm-browserify/download/vm-browserify-1.1.2.tgz", "integrity": "sha1-eGQcSIuObKkadfUR56OzKobl3aA=", "dev": true}, "w3c-hr-time": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/w3c-hr-time/download/w3c-hr-time-1.0.2.tgz", "integrity": "sha1-ConN9cwVgi35w2BUNnaWPgzDCM0=", "dev": true, "requires": {"browser-process-hrtime": "^1.0.0"}}, "walker": {"version": "1.0.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/walker/download/walker-1.0.7.tgz", "integrity": "sha1-L3+bj9ENZ3JisYqITijRlhjgKPs=", "dev": true, "requires": {"makeerror": "1.0.x"}}, "watchpack": {"version": "1.7.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/watchpack/download/watchpack-1.7.5.tgz", "integrity": "sha1-EmfmxV4Lm1vkTCAjrtVDeiwmxFM=", "dev": true, "requires": {"chokidar": "^3.4.1", "graceful-fs": "^4.1.2", "neo-async": "^2.5.0", "watchpack-chokidar2": "^2.0.1"}}, "watchpack-chokidar2": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/watchpack-chokidar2/download/watchpack-chokidar2-2.0.1.tgz", "integrity": "sha1-OFAAcu5uzmbzdpk2lQ6hdxvhyVc=", "dev": true, "optional": true, "requires": {"chokidar": "^2.1.8"}, "dependencies": {"chokidar": {"version": "2.1.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chokidar/download/chokidar-2.1.8.tgz", "integrity": "sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc=", "dev": true, "optional": true, "requires": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "fsevents": "^1.2.7", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1"}}, "fsevents": {"version": "1.2.13", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fsevents/download/fsevents-1.2.13.tgz", "integrity": "sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=", "dev": true, "optional": true, "requires": {"bindings": "^1.5.0", "nan": "^2.12.1"}}}}, "webidl-conversions": {"version": "4.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/webidl-conversions/download/webidl-conversions-4.0.2.tgz", "integrity": "sha1-qFWYCx8LazWbodXZ+zmulB+qY60=", "dev": true}, "webpack": {"version": "4.44.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/webpack/download/webpack-4.44.2.tgz", "integrity": "sha1-a/4rCvBVyLLR6Q7SzZNj+EEma3I=", "dev": true, "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-module-context": "1.9.0", "@webassemblyjs/wasm-edit": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0", "acorn": "^6.4.1", "ajv": "^6.10.2", "ajv-keywords": "^3.4.1", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^4.3.0", "eslint-scope": "^4.0.3", "json-parse-better-errors": "^1.0.2", "loader-runner": "^2.4.0", "loader-utils": "^1.2.3", "memory-fs": "^0.4.1", "micromatch": "^3.1.10", "mkdirp": "^0.5.3", "neo-async": "^2.6.1", "node-libs-browser": "^2.2.1", "schema-utils": "^1.0.0", "tapable": "^1.1.3", "terser-webpack-plugin": "^1.4.3", "watchpack": "^1.7.4", "webpack-sources": "^1.4.1"}, "dependencies": {"acorn": {"version": "6.4.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/acorn/download/acorn-6.4.2.tgz", "integrity": "sha1-NYZv1xBSjpLeEM8GAWSY5H454eY=", "dev": true}, "schema-utils": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/schema-utils/download/schema-utils-1.0.0.tgz", "integrity": "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=", "dev": true, "requires": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}}}}, "webpack-cli": {"version": "3.3.12", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/webpack-cli/download/webpack-cli-3.3.12.tgz", "integrity": "sha1-lOmtoIFFPNCqYJyZ5QABL9OtLUo=", "dev": true, "requires": {"chalk": "^2.4.2", "cross-spawn": "^6.0.5", "enhanced-resolve": "^4.1.1", "findup-sync": "^3.0.0", "global-modules": "^2.0.0", "import-local": "^2.0.0", "interpret": "^1.4.0", "loader-utils": "^1.4.0", "supports-color": "^6.1.0", "v8-compile-cache": "^2.1.1", "yargs": "^13.3.2"}, "dependencies": {"cross-spawn": {"version": "6.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cross-spawn/download/cross-spawn-6.0.5.tgz", "integrity": "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=", "dev": true, "requires": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "path-key": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-key/download/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=", "dev": true}, "shebang-command": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/shebang-command/download/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "dev": true, "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/shebang-regex/download/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=", "dev": true}, "supports-color": {"version": "6.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/download/supports-color-6.1.0.tgz", "integrity": "sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=", "dev": true, "requires": {"has-flag": "^3.0.0"}}, "which": {"version": "1.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/which/download/which-1.3.1.tgz", "integrity": "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=", "dev": true, "requires": {"isexe": "^2.0.0"}}}}, "webpack-parallel-uglify-plugin": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/webpack-parallel-uglify-plugin/download/webpack-parallel-uglify-plugin-1.1.4.tgz", "integrity": "sha1-C/jNjfW4QsB+8mvxGJIPXl0elsQ=", "dev": true, "requires": {"babel-code-frame": "^6.26.0", "glob": "^7.0.5", "mkdirp": "^0.5.1", "pify": "^3.0.0", "tmp": "0.0.29", "uglify-es": "^3.3.9", "uglify-js": "^3.6.0", "webpack-sources": "^1.0.0", "worker-farm": "^1.3.1"}, "dependencies": {"pify": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pify/download/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true}, "uglify-js": {"version": "3.12.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/uglify-js/download/uglify-js-3.12.3.tgz", "integrity": "sha1-uybEq+DmjFXpd2vKm+2ZpN9z+s8=", "dev": true}}}, "webpack-sources": {"version": "1.4.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/webpack-sources/download/webpack-sources-1.4.3.tgz", "integrity": "sha1-7t2OwLko+/HL/plOItLYkPMwqTM=", "dev": true, "requires": {"source-list-map": "^2.0.0", "source-map": "~0.6.1"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "whatwg-encoding": {"version": "1.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/whatwg-encoding/download/whatwg-encoding-1.0.5.tgz", "integrity": "sha1-WrrPd3wyFmpR0IXWtPPn0nET3bA=", "dev": true, "requires": {"iconv-lite": "0.4.24"}}, "whatwg-mimetype": {"version": "2.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/whatwg-mimetype/download/whatwg-mimetype-2.3.0.tgz", "integrity": "sha1-PUseAxLSB5h5+Cav8Y2+7KWWD78=", "dev": true}, "whatwg-url": {"version": "6.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/whatwg-url/download/whatwg-url-6.5.0.tgz", "integrity": "sha1-8t8Cv/F2/WUHDfdK1cy7WhmZZag=", "dev": true, "requires": {"lodash.sortby": "^4.7.0", "tr46": "^1.0.1", "webidl-conversions": "^4.0.2"}}, "which": {"version": "2.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/which/download/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "requires": {"isexe": "^2.0.0"}}, "which-module": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/which-module/download/which-module-2.0.0.tgz", "integrity": "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=", "dev": true}, "word-wrap": {"version": "1.2.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/word-wrap/download/word-wrap-1.2.3.tgz", "integrity": "sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=", "dev": true}, "worker-farm": {"version": "1.7.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/worker-farm/download/worker-farm-1.7.0.tgz", "integrity": "sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag=", "dev": true, "requires": {"errno": "~0.1.7"}}, "wrap-ansi": {"version": "5.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/wrap-ansi/download/wrap-ansi-5.1.0.tgz", "integrity": "sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=", "dev": true, "requires": {"ansi-styles": "^3.2.0", "string-width": "^3.0.0", "strip-ansi": "^5.0.0"}}, "wrappy": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/wrappy/download/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "dev": true}, "write-file-atomic": {"version": "2.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/write-file-atomic/download/write-file-atomic-2.4.1.tgz", "integrity": "sha1-0LBUY8GIroBDlv1asqNwBir4dSk=", "dev": true, "requires": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}}, "ws": {"version": "5.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ws/download/ws-5.2.2.tgz", "integrity": "sha1-3/7xSGa46NyRM1glFNG++vlumA8=", "dev": true, "requires": {"async-limiter": "~1.0.0"}}, "xml-name-validator": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/xml-name-validator/download/xml-name-validator-3.0.0.tgz", "integrity": "sha1-auc+Bt5NjG5H+fsYH3jWSK1FfGo=", "dev": true}, "xtend": {"version": "4.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/xtend/download/xtend-4.0.2.tgz", "integrity": "sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=", "dev": true}, "y18n": {"version": "4.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/y18n/download/y18n-4.0.1.tgz", "integrity": "sha1-jbK4PDHF11CZu4kLI/MJSJHiR9Q=", "dev": true}, "yallist": {"version": "3.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/yallist/download/yallist-3.1.1.tgz", "integrity": "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=", "dev": true}, "yargs": {"version": "13.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/yargs/download/yargs-13.3.2.tgz", "integrity": "sha1-rX/+/sGqWVZayRX4Lcyzipwxot0=", "dev": true, "requires": {"cliui": "^5.0.0", "find-up": "^3.0.0", "get-caller-file": "^2.0.1", "require-directory": "^2.1.1", "require-main-filename": "^2.0.0", "set-blocking": "^2.0.0", "string-width": "^3.0.0", "which-module": "^2.0.0", "y18n": "^4.0.0", "yargs-parser": "^13.1.2"}, "dependencies": {"find-up": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/find-up/download/find-up-3.0.0.tgz", "integrity": "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=", "dev": true, "requires": {"locate-path": "^3.0.0"}}, "locate-path": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/locate-path/download/locate-path-3.0.0.tgz", "integrity": "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=", "dev": true, "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "p-locate": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-locate/download/p-locate-3.0.0.tgz", "integrity": "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=", "dev": true, "requires": {"p-limit": "^2.0.0"}}, "path-exists": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-exists/download/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "dev": true}}}, "yargs-parser": {"version": "13.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/yargs-parser/download/yargs-parser-13.1.2.tgz", "integrity": "sha1-Ew8JcC667vJlDVTObj5XBvek+zg=", "dev": true, "requires": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}}}}
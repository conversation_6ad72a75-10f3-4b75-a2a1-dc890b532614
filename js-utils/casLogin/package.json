{"name": "@ky/caslogin", "version": "3.2.8", "description": "快运CMS CAS Login", "scripts": {"build": "cross-env DEPLOY_ENV=\"prod\" webpack", "test": "jest", "trypublish": "npm publish || true"}, "publishConfig": {"registry": "https://artifactory.sf-express.com/artifactory/api/npm/npm-sf-local/"}, "main": "build/index.js", "author": "<PERSON><PERSON>", "license": "MIT", "keywords": [], "devDependencies": {"@babel/core": "^7.13.10", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/plugin-transform-runtime": "^7.16.5", "@babel/preset-env": "^7.13.10", "babel-loader": "^8.2.2", "babel-preset-minify": "^0.5.0", "jest": "^24.8.0", "prettier": "^1.18.2", "prettier-webpack-plugin": "^1.2.0", "url-loader": "^2.0.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.4", "webpack-parallel-uglify-plugin": "^1.1.2"}, "dependencies": {"@babel/runtime": "^7.16.5", "axios": "^0.21.1", "core-js": "^3.18.3", "cross-env": "^6.0.3"}, "jest": {"moduleNameMapper": {"\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "<rootDir>/scripts/testMock.js", "\\.(css|less)$": "<rootDir>/scripts/testMock.js"}}, "gitHead": "cda8a4d059bd387cb9046075a1cbff030e593eb1"}
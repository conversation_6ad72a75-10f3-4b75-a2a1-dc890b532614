/*
 * @Descripttion:
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-03 16:22:22
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-06-15 21:53:43
 */
const TerserPlugin = require('terser-webpack-plugin');

module.exports = {
  entry: {
    'web-sensors': './lib/webSensors.js',
    'wx-sensors': './lib/wxSensors.js',
    'web-sensors.min': './lib/webSensors.js',
    'wx-sensors.min': './lib/wxSensors.js',
  },
  output: {
    path: __dirname,
    filename: '[name].js',
    library: 'KySensors',
    libraryTarget: 'umd',
    libraryExport: 'default',
  },
  mode: 'none',
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: 'babel-loader',
      },
    ],
  },
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        include: /\.min\.js$/,
      }),
    ],
  },
  plugins: [],
};

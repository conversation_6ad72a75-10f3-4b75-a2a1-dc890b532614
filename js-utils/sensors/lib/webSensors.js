/*
 * @Descripttion: 快运神策Web端
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-01 17:04:17
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-06-14 16:34:02
 */
import { isPropsNameValid, isEventNameValid, isJsonStr } from './utils';
import rules from './rule.json';
const sensors = require('sa-sdk-javascript');

class KyWebSensors {
  constructor() {
    // 初始化SDK
    // this._sensors = sensors;
    // 记录上一次的时间
    this._lastTime = new Date().getTime();
    // 调用方传入的公有属性
    this._commonProps = {};
  }

  init(initConfig = {}) {
    const { heatmap = {}, ...restConfig } = initConfig;
    sensors.init({
      server_url: `https://ubs.sf-express.com/sa?project=expressTechgray`,
      is_track_single_page: false,
      use_client_time: true,
      send_type: 'beacon',
      show_log: false,
      heatmap: {
        clickmap: 'not_collect',
        scroll_notice_map: 'not_collect',
        ...heatmap,
      },
      ...restConfig,
    });
  }

  // 使用插件
  use(pluginEvent, options) {
    if (sensors.use) {
      if (options) {
        sensors.use(pluginEvent, options);
      } else {
        sensors.use(pluginEvent);
      }
    }
  }

  // 重置时间
  restLastTime() {
    this._lastTime = new Date().getTime();
  }

  // 注册公有属性
  registerPage(config = {}) {
    if (this._isCommonPropsValid(config)) {
      this._commonProps = config;
      if (isPropsNameValid(config, rules.common)) {
        sensors.registerPage({
          ...config,
        });
        // 在此处调用页面加载时长事件
        this.use('PageLoad');
      }
    }
  }

  // 登录
  login(userId) {
    sensors.login(userId);
  }

  // 设置用户属性 （如果存在则覆盖）
  setProfile(config = {}, callback = () => {}) {
    if (isPropsNameValid(config, rules.user)) {
      sensors.setProfile(
        {
          ...config,
        },
        callback,
      );
    }
  }

  // 设置用户属性（如果不存在则设置，存在就不设置）
  setOnceProfile(config = {}, callback = () => {}) {
    if (isPropsNameValid(config, rules.user)) {
      sensors.setOnceProfile(
        {
          ...config,
        },
        callback,
      );
    }
  }

  // 功能入口
  functionClick(config = {}) {
    if (this._isCommonPropsValid(this._commonProps)) {
      const conf = this._returnRestConfig(config);
      if (conf && isPropsNameValid(conf, rules.eventProps)) {
        sensors.track('FunctionClick', {
          ...conf,
        });
      }
    }
  }

  // 页面停留
  pageStay(config = {}) {
    if (this._isCommonPropsValid(this._commonProps)) {
      const conf = this._returnRestConfig(config, '$title');
      if (!conf) return;
      const { maxStayTime } = conf;
      delete conf.maxStayTime;
      const nowTime = new Date().getTime();
      const lastTime = this._lastTime;
      let timeDiff = Math.ceil((nowTime - lastTime) / 1000);
      if (timeDiff <= 0) {
        timeDiff = 0;
      }
      if (maxStayTime) {
        timeDiff = timeDiff > maxStayTime ? maxStayTime : timeDiff;
      }
      if (timeDiff) {
        sensors.track('PageStay', {
          ...conf,
          $event_duration: timeDiff,
        });
      }
      this._lastTime = nowTime;
    }
  }

  // 页面浏览
  pageview(config = {}) {
    if (this._isCommonPropsValid(this._commonProps)) {
      const conf = this._returnRestConfig(config, '$title');
      if (conf && isPropsNameValid(conf, rules.eventProps)) {
        sensors.track('$pageview', {
          ...conf,
        });
      }
    }
  }

  // 元素点击
  webClick(config = {}) {
    if (this._isCommonPropsValid(this._commonProps)) {
      const conf = this._returnRestConfig(config, '$element_content');
      if (conf && isPropsNameValid(conf, rules.eventProps)) {
        sensors.track('$WebClick', {
          ...conf,
        });
      }
    }
  }

  // 行为追踪事件
  track(eventName, config = {}) {
    if (this._isCommonPropsValid(this._commonProps)) {
      const conf = this._returnRestConfig(config);
      if (
        conf &&
        isEventNameValid(eventName) &&
        isPropsNameValid(conf, rules.eventProps)
      ) {
        sensors.track(eventName, {
          ...conf,
        });
      }
    }
  }

  // 处理参数属性
  _returnRestConfig(config = {}, key) {
    let conf = {};
    if (typeof config === 'string') {
      conf = { [key]: config };
    } else if (Object.prototype.toString.call(config) === '[object Object]') {
      conf = { ...config };
    } else {
      console.error('参数必须是字符串 或者 Object对象');
      return undefined;
    }
    // 处理extend字段
    if (conf.extend) {
      if (Object.prototype.toString.call(conf.extend) === '[object Object]') {
        conf.extend = JSON.stringify(conf.extend);
      } else if (!isJsonStr(conf.extend)) {
        delete conf.extend;
        console.error('extend字段必须是JSON 字符串 或者 Object对象');
      }
    }
    return conf;
  }

  // 检测公有属性是否有system_code和charge_sys_code
  _isCommonPropsValid(commonProps = {}) {
    if (commonProps.system_code && commonProps.charge_sys_code) {
      return true;
    }
    console.error(
      '神策error: 在registerPage方法中，system_code与charge_sys_code必须同时传入, 且不能为空!',
    );
    return false;
  }

  // 神策真实实例
  // getSensorInstance() {
  //   return sensors;
  // }
}

export default new KyWebSensors();

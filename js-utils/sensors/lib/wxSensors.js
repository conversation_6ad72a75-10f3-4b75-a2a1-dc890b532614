/*
 * @Descripttion: 快运神策 小程序 端
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-02 17:04:17
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-11-25 14:16:56
 */

import sensors from 'sa-sdk-miniprogram';
import { isPropsNameValid, isEventNameValid, isObj, isJsonStr } from './utils';
import rules from './rule.json';

// 系统事件名
const PAGE_VIEW_EVENT = '$MPViewScreen'; // 预置事件 - 页面浏览
const CLICK_EVENT = '$MPClick'; // 预置事件 - 点击
const EXPOSURE_EVENT = 'Exposure'; // 自定义事件事件 - 曝光
const PAGE_STAY_EVENT = 'PageStay'; // 自定义事件事件 - 页面停留
const FUNCTION_CLiCK_EVENT = 'FunctionClick'; // 自定义事件 - 功能入口

const MAX_STAY_TIME = 60 * 10; // 最大页面停留时间 10 分钟

let lastTime; // 记录时间
let commonProps={}; // 调用方传入的公有属性

// 格式化参数
const getFormatConf = conf => {
  if (!conf || !isObj(conf)) throw new Error('参数必须为对象');

  // 处理 extend 字段 （存放扩展参数）
  if (conf.extend) {
    if (isObj(conf.extend)) {
      conf.extend = JSON.stringify(conf.extend);
    } else if (!isJsonStr(conf.extend)) {
      delete conf.extend;
      console.error('参数的 extend 字段必须是 JSON 字符串或 Object 对象');
    }
  }

  return conf;
};

// 检测公有属性是否有system_code和charge_sys_code
const isCommonPropsValid = (commonProps1 = {}) => {
  if(commonProps1.system_code && commonProps1.charge_sys_code) {
    return true
  } else {
    console.error('神策error: 在registerApp方法中，system_code与charge_sys_code必须同时传入, 且不能为空!')
    return false
  }
};

const wxSensors = {
  // 暴露原始的 sensors 对象
  // _sensors: sensors,

  // 配置初始化参数
  setPara(initConf = {}) {
    const { autoTrack = {}, ...restConf } = initConf;

    sensors.setPara({
      name: 'sensors',
      server_url: 'https://ubs.sf-express.com/sa?project=expressTechgray',
      // 全埋点控制开关
      autoTrack: {
        appLaunch: true,
        appShow: true,
        appHide: true,
        pageShow: true,
        pageShare: false,
        mpClick: false,
        mpFavorite: false,
        ...autoTrack,
      },
      // 自定义渠道追踪参数，如source_channel: ["custom_param"]
      source_channel: [],
      // 是否允许控制台打印查看埋点数据(建议开启查看)
      show_log: true,
      // 是否允许修改 onShareAppMessage 里 return 的 path，用来增加(登录 ID，分享层级，当前的 path)，在 app onShow 中自动获取这些参数来查看具体分享来源、层级等
      allow_amend_share_path: true,
      ...restConf,
    });
  },

  // 设置事件公共属性
  registerApp(conf = {}) {
    if(isCommonPropsValid(conf)) {
      commonProps = conf
      if (isPropsNameValid(conf, rules.common)) {
        sensors.registerApp(conf);
      }
    }
  },

  // 登录
  login(userId) {
    sensors.login(userId);
  },

  // 设置用户属性
  setProfile(conf) {
    if (isPropsNameValid(conf, rules.user)) {
      sensors.setProfile(conf);
    }
  },

  // 设置 openId
  setOpenid(openId) {
    sensors.setOpenid(openId);
  },

  // 初始化
  init() {
    this.restLastTime();
    sensors.init();
  },

  // 重置时间
  restLastTime() {
    lastTime = Date.now();
  },

  // 行为追踪事件
  track(eventName, config = {}) {
    if(isCommonPropsValid(commonProps)) {
      const conf = getFormatConf(config);
      if (
        conf &&
        isEventNameValid(eventName) &&
        isPropsNameValid(conf, rules.eventProps)
      ) {
        sensors.track(eventName, conf);
      }
    }
  },

  // 页面浏览事件
  $pageView(conf = {}) {
    wxSensors.track(PAGE_VIEW_EVENT, conf);
  },
  pageview(conf = {}) {
    wxSensors.track(PAGE_VIEW_EVENT, conf);
  },

  // 点击事件
  $click(conf = {}) {
    wxSensors.track(CLICK_EVENT, conf);
  },
  mpClick(conf = {}) {
    wxSensors.track(CLICK_EVENT, conf);
  },

  // 曝光事件
  $exposure(conf = {}) {
    wxSensors.track(EXPOSURE_EVENT, conf);
  },
  exposure(conf = {}) {
    wxSensors.track(EXPOSURE_EVENT, conf);
  },

  // 页面停留时间上报
  $pageStay(conf = {}) {
    const timeDiff = Math.ceil((Date.now() - lastTime) / 1000);
    wxSensors.track(PAGE_STAY_EVENT, {
      $event_duration: Math.min(MAX_STAY_TIME, timeDiff),
      ...conf,
    });
  },
  pageStay(conf = {}) {
    const timeDiff = Math.ceil((Date.now() - lastTime) / 1000);
    wxSensors.track(PAGE_STAY_EVENT, {
      $event_duration: Math.min(MAX_STAY_TIME, timeDiff),
      ...conf,
    });
  },

  // 功能入口事件上报
  $functionClick(conf = {}) {
    wxSensors.track(FUNCTION_CLiCK_EVENT, conf);
  },
  functionClick(conf = {}) {
    wxSensors.track(FUNCTION_CLiCK_EVENT, conf);
  },
};

export default wxSensors;

/*
 * @Descripttion:
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-02 17:48:20
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-07-23 15:38:08
 */

// 判断属性命名是否规范
export const isPropsNameValid = (props, whileList) => {
  try {
    Object.keys(props).forEach(key => {
      if (!(/^\$/.test(key) || whileList.includes(key))) {
        throw new Error('属性不合法!');
      }
    });
    return true;
  } catch (err) {
    console.error(
      `属性不合法：推荐使用神策预置属性，自定义属性必须为如下`,
      `${JSON.stringify(whileList)}`,
    );
    return false;
  }
};

// 判断事件命名是否规范
export const isEventNameValid = eventName => {
  // 预置事件$开头, 自定义事件大写字母开头的驼峰式
  if (/^\$/.test(eventName) || /^([A-Z]+)([a-z]+)/.test(eventName)) {
    return true;
  }
  console.error(
    '事件名称不合法：推荐使用神策预置事件(以$开头)，或者自定义事件(以大写字母开头驼峰式)',
  );
  return false;
};

// 判断该字符串是否为json类型的字符串
export const isJsonStr = str => {
  try {
    JSON.parse(str);
  } catch (e) {
    return false;
  }
  return true;
};

// 判断是否为对象类型
export const isObj = obj =>
  Object.prototype.toString.call(obj) === '[object Object]';

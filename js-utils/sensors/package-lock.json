{"name": "@ky/sensors", "version": "1.1.6", "lockfileVersion": 1, "requires": true, "dependencies": {"@babel/code-frame": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/code-frame/-/code-frame-7.14.5.tgz", "integrity": "sha1-I7CNdA6D9JxeWZRfvxtD6Au/Tts=", "dev": true, "requires": {"@babel/highlight": "^7.14.5"}}, "@babel/compat-data": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/compat-data/-/compat-data-7.14.5.tgz", "integrity": "sha1-jvTBjljoAcXJXTwcDyh0omgPreo=", "dev": true}, "@babel/core": {"version": "7.14.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/core/-/core-7.14.6.tgz", "integrity": "sha1-4IFOwalQAy/xbBOich3jmoQW/Ks=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/generator": "^7.14.5", "@babel/helper-compilation-targets": "^7.14.5", "@babel/helper-module-transforms": "^7.14.5", "@babel/helpers": "^7.14.6", "@babel/parser": "^7.14.6", "@babel/template": "^7.14.5", "@babel/traverse": "^7.14.5", "@babel/types": "^7.14.5", "convert-source-map": "^1.7.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.1.2", "semver": "^6.3.0", "source-map": "^0.5.0"}}, "@babel/generator": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/generator/-/generator-7.14.5.tgz", "integrity": "sha1-hI17nwMcrKnQzQrwGwY/Im9S14U=", "dev": true, "requires": {"@babel/types": "^7.14.5", "jsesc": "^2.5.1", "source-map": "^0.5.0"}}, "@babel/helper-annotate-as-pure": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.14.5.tgz", "integrity": "sha1-e/R47Dtxcm1WqMpXdbBG/CmHnmE=", "dev": true, "requires": {"@babel/types": "^7.14.5"}}, "@babel/helper-builder-binary-assignment-operator-visitor": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.14.5.tgz", "integrity": "sha1-uTm0P4w3dlRDoZrnStixWXjgoZE=", "dev": true, "requires": {"@babel/helper-explode-assignable-expression": "^7.14.5", "@babel/types": "^7.14.5"}}, "@babel/helper-compilation-targets": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-compilation-targets/-/helper-compilation-targets-7.14.5.tgz", "integrity": "sha1-epnF0JZ5Eely/iw0EffVtJhJjs8=", "dev": true, "requires": {"@babel/compat-data": "^7.14.5", "@babel/helper-validator-option": "^7.14.5", "browserslist": "^4.16.6", "semver": "^6.3.0"}}, "@babel/helper-create-class-features-plugin": {"version": "7.14.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.14.6.tgz", "integrity": "sha1-8RRGm2wG+LXFnGxOdGIfUIU2JUI=", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.14.5", "@babel/helper-function-name": "^7.14.5", "@babel/helper-member-expression-to-functions": "^7.14.5", "@babel/helper-optimise-call-expression": "^7.14.5", "@babel/helper-replace-supers": "^7.14.5", "@babel/helper-split-export-declaration": "^7.14.5"}}, "@babel/helper-create-regexp-features-plugin": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.14.5.tgz", "integrity": "sha1-x9WsXpz2IcJgV3Ivt6ikxYiTWMQ=", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.14.5", "regexpu-core": "^4.7.1"}}, "@babel/helper-define-polyfill-provider": {"version": "0.2.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.2.3.tgz", "integrity": "sha1-BSXt7FCUZTooJojTTYRuTHXpwLY=", "dev": true, "requires": {"@babel/helper-compilation-targets": "^7.13.0", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-plugin-utils": "^7.13.0", "@babel/traverse": "^7.13.0", "debug": "^4.1.1", "lodash.debounce": "^4.0.8", "resolve": "^1.14.2", "semver": "^6.1.2"}}, "@babel/helper-explode-assignable-expression": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.14.5.tgz", "integrity": "sha1-iqcucIIFx7tkPkXHO0OGzfKh9kU=", "dev": true, "requires": {"@babel/types": "^7.14.5"}}, "@babel/helper-function-name": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-function-name/-/helper-function-name-7.14.5.tgz", "integrity": "sha1-ieLEdJcvFdjiM7Uu6MSA4s/NUMQ=", "dev": true, "requires": {"@babel/helper-get-function-arity": "^7.14.5", "@babel/template": "^7.14.5", "@babel/types": "^7.14.5"}}, "@babel/helper-get-function-arity": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-get-function-arity/-/helper-get-function-arity-7.14.5.tgz", "integrity": "sha1-Jfv6V5sJN+7h87gF7OTOOYxDGBU=", "dev": true, "requires": {"@babel/types": "^7.14.5"}}, "@babel/helper-hoist-variables": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-hoist-variables/-/helper-hoist-variables-7.14.5.tgz", "integrity": "sha1-4N0nwzp45XfXyIhJFqPn7x98f40=", "dev": true, "requires": {"@babel/types": "^7.14.5"}}, "@babel/helper-member-expression-to-functions": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.14.5.tgz", "integrity": "sha1-1ccOStE7QCyVFWx6U1aPUE4vt7g=", "dev": true, "requires": {"@babel/types": "^7.14.5"}}, "@babel/helper-module-imports": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-module-imports/-/helper-module-imports-7.14.5.tgz", "integrity": "sha1-bRpE32o4yVeqfDEtoHZCnxG0IvM=", "dev": true, "requires": {"@babel/types": "^7.14.5"}}, "@babel/helper-module-transforms": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-module-transforms/-/helper-module-transforms-7.14.5.tgz", "integrity": "sha1-feQvENeJtCPrkC69JAMcp3yx4Q4=", "dev": true, "requires": {"@babel/helper-module-imports": "^7.14.5", "@babel/helper-replace-supers": "^7.14.5", "@babel/helper-simple-access": "^7.14.5", "@babel/helper-split-export-declaration": "^7.14.5", "@babel/helper-validator-identifier": "^7.14.5", "@babel/template": "^7.14.5", "@babel/traverse": "^7.14.5", "@babel/types": "^7.14.5"}}, "@babel/helper-optimise-call-expression": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.14.5.tgz", "integrity": "sha1-8nOVqGGeBmWz8DZM3bQcJdcbSZw=", "dev": true, "requires": {"@babel/types": "^7.14.5"}}, "@babel/helper-plugin-utils": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-plugin-utils/-/helper-plugin-utils-7.14.5.tgz", "integrity": "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak=", "dev": true}, "@babel/helper-remap-async-to-generator": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.14.5.tgz", "integrity": "sha1-UUOckTYSlY9UqYek/8nuWHogRdY=", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.14.5", "@babel/helper-wrap-function": "^7.14.5", "@babel/types": "^7.14.5"}}, "@babel/helper-replace-supers": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-replace-supers/-/helper-replace-supers-7.14.5.tgz", "integrity": "sha1-DswLA8Qc1We0Ak6gFhNMKEFKu5Q=", "dev": true, "requires": {"@babel/helper-member-expression-to-functions": "^7.14.5", "@babel/helper-optimise-call-expression": "^7.14.5", "@babel/traverse": "^7.14.5", "@babel/types": "^7.14.5"}}, "@babel/helper-simple-access": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-simple-access/-/helper-simple-access-7.14.5.tgz", "integrity": "sha1-ZuqFz1O6C05Yi6d/yBP1OryqQcQ=", "dev": true, "requires": {"@babel/types": "^7.14.5"}}, "@babel/helper-skip-transparent-expression-wrappers": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.14.5.tgz", "integrity": "sha1-lvSGrAUMqfRLAJ++W305TKs6DuQ=", "dev": true, "requires": {"@babel/types": "^7.14.5"}}, "@babel/helper-split-export-declaration": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.14.5.tgz", "integrity": "sha1-IrI6VO9RwrdgXYUZMMGXbdC8aTo=", "dev": true, "requires": {"@babel/types": "^7.14.5"}}, "@babel/helper-validator-identifier": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.14.5.tgz", "integrity": "sha1-0PDid8US4Mk4J3+qhaOWjJpEwOg=", "dev": true}, "@babel/helper-validator-option": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-validator-option/-/helper-validator-option-7.14.5.tgz", "integrity": "sha1-bnKh//GNXfy4eOHmLxoCHEty1aM=", "dev": true}, "@babel/helper-wrap-function": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helper-wrap-function/-/helper-wrap-function-7.14.5.tgz", "integrity": "sha1-WRnRFb8P4yi4pdY7y2EPUWAfK/8=", "dev": true, "requires": {"@babel/helper-function-name": "^7.14.5", "@babel/template": "^7.14.5", "@babel/traverse": "^7.14.5", "@babel/types": "^7.14.5"}}, "@babel/helpers": {"version": "7.14.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/helpers/-/helpers-7.14.6.tgz", "integrity": "sha1-W1gwa5XxtH4qAZlDT6hlj6bCFjU=", "dev": true, "requires": {"@babel/template": "^7.14.5", "@babel/traverse": "^7.14.5", "@babel/types": "^7.14.5"}}, "@babel/highlight": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/highlight/-/highlight-7.14.5.tgz", "integrity": "sha1-aGGlLwOWZAUAH2qlNKAaJNmejNk=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.5", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}}, "@babel/parser": {"version": "7.14.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/parser/-/parser-7.14.6.tgz", "integrity": "sha1-2FzGjKPKyE6uOEwG8DKSH1In9LI=", "dev": true}, "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.14.5.tgz", "integrity": "sha1-S0ZzAuFUjtOxvkO+rizJz0Xgu34=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.14.5", "@babel/plugin-proposal-optional-chaining": "^7.14.5"}}, "@babel/plugin-proposal-async-generator-functions": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.14.5.tgz", "integrity": "sha1-QCSZDj3XQYH09CbqZXdp/0mi3zk=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-remap-async-to-generator": "^7.14.5", "@babel/plugin-syntax-async-generators": "^7.8.4"}}, "@babel/plugin-proposal-class-properties": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.14.5.tgz", "integrity": "sha1-QNHuFAxbHjGjUPT17tlFCWVZtC4=", "dev": true, "requires": {"@babel/helper-create-class-features-plugin": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-proposal-class-static-block": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-class-static-block/-/plugin-proposal-class-static-block-7.14.5.tgz", "integrity": "sha1-FY6eENRJw4Se8+zelKA9nxhBtoE=", "dev": true, "requires": {"@babel/helper-create-class-features-plugin": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-class-static-block": "^7.14.5"}}, "@babel/plugin-proposal-dynamic-import": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-dynamic-import/-/plugin-proposal-dynamic-import-7.14.5.tgz", "integrity": "sha1-DGYX30YcDB+P/ztHzVl3I2AQHSw=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}}, "@babel/plugin-proposal-export-namespace-from": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-export-namespace-from/-/plugin-proposal-export-namespace-from-7.14.5.tgz", "integrity": "sha1-260kQxDObM0IMHIWfYzqg6Uvr3Y=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-export-namespace-from": "^7.8.3"}}, "@babel/plugin-proposal-json-strings": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.14.5.tgz", "integrity": "sha1-ON5g2zYug6PYyUSshY3fnwwiOes=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-json-strings": "^7.8.3"}}, "@babel/plugin-proposal-logical-assignment-operators": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-logical-assignment-operators/-/plugin-proposal-logical-assignment-operators-7.14.5.tgz", "integrity": "sha1-bmIpwqmbAqspFfglceDMZGpAxzg=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}}, "@babel/plugin-proposal-nullish-coalescing-operator": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.14.5.tgz", "integrity": "sha1-7jhYnOAOLMWbKZ7D6kBvzToP2vY=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}}, "@babel/plugin-proposal-numeric-separator": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.14.5.tgz", "integrity": "sha1-g2Mb8z2aUd8YTCECoGmsDFjAXxg=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}}, "@babel/plugin-proposal-object-rest-spread": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.14.5.tgz", "integrity": "sha1-5YHVzN+hh+pu1z9WxqIcFYC5D78=", "dev": true, "requires": {"@babel/compat-data": "^7.14.5", "@babel/helper-compilation-targets": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-transform-parameters": "^7.14.5"}}, "@babel/plugin-proposal-optional-catch-binding": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.14.5.tgz", "integrity": "sha1-k53W7d7/Omf997PwRLU0cmJZjDw=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3"}}, "@babel/plugin-proposal-optional-chaining": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.14.5.tgz", "integrity": "sha1-+oNlHmCjYOPxN5fu8AuNUZaVtgM=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.14.5", "@babel/plugin-syntax-optional-chaining": "^7.8.3"}}, "@babel/plugin-proposal-private-methods": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-private-methods/-/plugin-proposal-private-methods-7.14.5.tgz", "integrity": "sha1-N0RklZlrKUXzD1vltg1eKqT1eS0=", "dev": true, "requires": {"@babel/helper-create-class-features-plugin": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-proposal-private-property-in-object": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.14.5.tgz", "integrity": "sha1-n2Wk0Ek6lAtMAfiqnT8YlKWH9jY=", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.14.5", "@babel/helper-create-class-features-plugin": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}}, "@babel/plugin-proposal-unicode-property-regex": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.14.5.tgz", "integrity": "sha1-D5XuDnV6XWR/N42qDsp+k/qou+g=", "dev": true, "requires": {"@babel/helper-create-regexp-features-plugin": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-syntax-async-generators": {"version": "7.8.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz", "integrity": "sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-class-properties": {"version": "7.12.13", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz", "integrity": "sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.12.13"}}, "@babel/plugin-syntax-class-static-block": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz", "integrity": "sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-syntax-dynamic-import": {"version": "7.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz", "integrity": "sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-export-namespace-from": {"version": "7.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz", "integrity": "sha1-AolkqbqA28CUyRXEh618TnpmRlo=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.3"}}, "@babel/plugin-syntax-json-strings": {"version": "7.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz", "integrity": "sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-logical-assignment-operators": {"version": "7.10.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "integrity": "sha1-ypHvRjA1MESLkGZSusLp/plB9pk=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "integrity": "sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz", "integrity": "sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.10.4"}}, "@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz", "integrity": "sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz", "integrity": "sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz", "integrity": "sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.8.0"}}, "@babel/plugin-syntax-private-property-in-object": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz", "integrity": "sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-syntax-top-level-await": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz", "integrity": "sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-arrow-functions": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.14.5.tgz", "integrity": "sha1-9xh9lYinaN0IC/TJ/+EX6mL3hio=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-async-to-generator": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.14.5.tgz", "integrity": "sha1-cseJCE2PIJSsuUVjOUPvhEPTnmc=", "dev": true, "requires": {"@babel/helper-module-imports": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-remap-async-to-generator": "^7.14.5"}}, "@babel/plugin-transform-block-scoped-functions": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.14.5.tgz", "integrity": "sha1-5IZB2ZnUvBV6Z+8zautUvET9OtQ=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-block-scoping": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.14.5.tgz", "integrity": "sha1-jMY+YeUPQuB45vCb53WnXyPvmTk=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-classes": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-classes/-/plugin-transform-classes-7.14.5.tgz", "integrity": "sha1-DpjoIJezhVCwO0g/m1GnjeCsss8=", "dev": true, "requires": {"@babel/helper-annotate-as-pure": "^7.14.5", "@babel/helper-function-name": "^7.14.5", "@babel/helper-optimise-call-expression": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-replace-supers": "^7.14.5", "@babel/helper-split-export-declaration": "^7.14.5", "globals": "^11.1.0"}}, "@babel/plugin-transform-computed-properties": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.14.5.tgz", "integrity": "sha1-G514mHQg0RIj1BGVRhzEO5dLIE8=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-destructuring": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.14.5.tgz", "integrity": "sha1-0yrRn/Gm2h6GHcYnINgNl3bjvzU=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-dotall-regex": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.14.5.tgz", "integrity": "sha1-L2v3bka9+AQ7Tn4WzyRTJim6DHo=", "dev": true, "requires": {"@babel/helper-create-regexp-features-plugin": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-duplicate-keys": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.14.5.tgz", "integrity": "sha1-NlpIRIgb3xUB46nwJw5/D5EXeVQ=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-exponentiation-operator": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.14.5.tgz", "integrity": "sha1-UVS43Wo9/m2Qkj1hckvT3uuQtJM=", "dev": true, "requires": {"@babel/helper-builder-binary-assignment-operator-visitor": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-for-of": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.14.5.tgz", "integrity": "sha1-2uOEYT3o93wZaohpy/YCpE9/wOs=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-function-name": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.14.5.tgz", "integrity": "sha1-6Bxl7LkAdG1/MYAva+0fUtkV1vI=", "dev": true, "requires": {"@babel/helper-function-name": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-literals": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-literals/-/plugin-transform-literals-7.14.5.tgz", "integrity": "sha1-QdBsf/XU0J489Fh70+zzkwxzD3g=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-member-expression-literals": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.14.5.tgz", "integrity": "sha1-s5zVISor8jWmF9Mg7CtIvMCRuKc=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-modules-amd": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.14.5.tgz", "integrity": "sha1-T9nOfjQRy4uDhISAtwQdgwBIWPc=", "dev": true, "requires": {"@babel/helper-module-transforms": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5", "babel-plugin-dynamic-import-node": "^2.3.3"}}, "@babel/plugin-transform-modules-commonjs": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.14.5.tgz", "integrity": "sha1-eq7g6pgoPelNqYso+MNXAUKdrZc=", "dev": true, "requires": {"@babel/helper-module-transforms": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-simple-access": "^7.14.5", "babel-plugin-dynamic-import-node": "^2.3.3"}}, "@babel/plugin-transform-modules-systemjs": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.14.5.tgz", "integrity": "sha1-x1NC74sw3N5CldNAGq4k5lY47Sk=", "dev": true, "requires": {"@babel/helper-hoist-variables": "^7.14.5", "@babel/helper-module-transforms": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-validator-identifier": "^7.14.5", "babel-plugin-dynamic-import-node": "^2.3.3"}}, "@babel/plugin-transform-modules-umd": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.14.5.tgz", "integrity": "sha1-+2Yt/uaXzOJ0p82lJRkKeQlqpuA=", "dev": true, "requires": {"@babel/helper-module-transforms": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-named-capturing-groups-regex": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.14.5.tgz", "integrity": "sha1-1Tfo7gg+5vaqT07vnSCB1VV0bkw=", "dev": true, "requires": {"@babel/helper-create-regexp-features-plugin": "^7.14.5"}}, "@babel/plugin-transform-new-target": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.14.5.tgz", "integrity": "sha1-Mb2ui5JdyEB26/zSqZQBQ67X2/g=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-object-super": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.14.5.tgz", "integrity": "sha1-0LX66snphZehYanPeMUn7ZNM3EU=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-replace-supers": "^7.14.5"}}, "@babel/plugin-transform-parameters": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.14.5.tgz", "integrity": "sha1-SWYuhqHz3cysY2On37H/ChWK/rM=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-property-literals": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.14.5.tgz", "integrity": "sha1-DduqH4PbNgbxzfSEb6HftHNFizQ=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-regenerator": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.14.5.tgz", "integrity": "sha1-lnb9VwftKPUicnxbPAqoVERAsE8=", "dev": true, "requires": {"regenerator-transform": "^0.14.2"}}, "@babel/plugin-transform-reserved-words": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.14.5.tgz", "integrity": "sha1-xEWJtmHP2++NQwDcx0ad/6kvgwQ=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-runtime": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.14.5.tgz", "integrity": "sha1-MEkdrUnGBZ+Pj6XuiJagCJ6YdSM=", "dev": true, "requires": {"@babel/helper-module-imports": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5", "babel-plugin-polyfill-corejs2": "^0.2.2", "babel-plugin-polyfill-corejs3": "^0.2.2", "babel-plugin-polyfill-regenerator": "^0.2.2", "semver": "^6.3.0"}}, "@babel/plugin-transform-shorthand-properties": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.14.5.tgz", "integrity": "sha1-l/E4VfFAkzjYyty6ymcK154JGlg=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-spread": {"version": "7.14.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-spread/-/plugin-transform-spread-7.14.6.tgz", "integrity": "sha1-a9QOV/596UqpBIUZY7VhZlL3MUQ=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.14.5"}}, "@babel/plugin-transform-sticky-regex": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.14.5.tgz", "integrity": "sha1-W2F1Qmdei3dhKUOB88KMYz9Arrk=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-template-literals": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.14.5.tgz", "integrity": "sha1-pfK8Izk32EU4hdxza92Nn/q/PZM=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-typeof-symbol": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.14.5.tgz", "integrity": "sha1-Oa8nOemJor0pG/a1PxaYFCPUV9Q=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-unicode-escapes": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.14.5.tgz", "integrity": "sha1-nUvSpoHjxdes9PV/qeURddkdDGs=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/plugin-transform-unicode-regex": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.14.5.tgz", "integrity": "sha1-TNCbbIQl3YElXHzrP7GDbnQUOC4=", "dev": true, "requires": {"@babel/helper-create-regexp-features-plugin": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5"}}, "@babel/preset-env": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/preset-env/-/preset-env-7.14.5.tgz", "integrity": "sha1-wMhOdjZh/Q50KSw9URyzOwxmiZc=", "dev": true, "requires": {"@babel/compat-data": "^7.14.5", "@babel/helper-compilation-targets": "^7.14.5", "@babel/helper-plugin-utils": "^7.14.5", "@babel/helper-validator-option": "^7.14.5", "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "^7.14.5", "@babel/plugin-proposal-async-generator-functions": "^7.14.5", "@babel/plugin-proposal-class-properties": "^7.14.5", "@babel/plugin-proposal-class-static-block": "^7.14.5", "@babel/plugin-proposal-dynamic-import": "^7.14.5", "@babel/plugin-proposal-export-namespace-from": "^7.14.5", "@babel/plugin-proposal-json-strings": "^7.14.5", "@babel/plugin-proposal-logical-assignment-operators": "^7.14.5", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.14.5", "@babel/plugin-proposal-numeric-separator": "^7.14.5", "@babel/plugin-proposal-object-rest-spread": "^7.14.5", "@babel/plugin-proposal-optional-catch-binding": "^7.14.5", "@babel/plugin-proposal-optional-chaining": "^7.14.5", "@babel/plugin-proposal-private-methods": "^7.14.5", "@babel/plugin-proposal-private-property-in-object": "^7.14.5", "@babel/plugin-proposal-unicode-property-regex": "^7.14.5", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-export-namespace-from": "^7.8.3", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-top-level-await": "^7.14.5", "@babel/plugin-transform-arrow-functions": "^7.14.5", "@babel/plugin-transform-async-to-generator": "^7.14.5", "@babel/plugin-transform-block-scoped-functions": "^7.14.5", "@babel/plugin-transform-block-scoping": "^7.14.5", "@babel/plugin-transform-classes": "^7.14.5", "@babel/plugin-transform-computed-properties": "^7.14.5", "@babel/plugin-transform-destructuring": "^7.14.5", "@babel/plugin-transform-dotall-regex": "^7.14.5", "@babel/plugin-transform-duplicate-keys": "^7.14.5", "@babel/plugin-transform-exponentiation-operator": "^7.14.5", "@babel/plugin-transform-for-of": "^7.14.5", "@babel/plugin-transform-function-name": "^7.14.5", "@babel/plugin-transform-literals": "^7.14.5", "@babel/plugin-transform-member-expression-literals": "^7.14.5", "@babel/plugin-transform-modules-amd": "^7.14.5", "@babel/plugin-transform-modules-commonjs": "^7.14.5", "@babel/plugin-transform-modules-systemjs": "^7.14.5", "@babel/plugin-transform-modules-umd": "^7.14.5", "@babel/plugin-transform-named-capturing-groups-regex": "^7.14.5", "@babel/plugin-transform-new-target": "^7.14.5", "@babel/plugin-transform-object-super": "^7.14.5", "@babel/plugin-transform-parameters": "^7.14.5", "@babel/plugin-transform-property-literals": "^7.14.5", "@babel/plugin-transform-regenerator": "^7.14.5", "@babel/plugin-transform-reserved-words": "^7.14.5", "@babel/plugin-transform-shorthand-properties": "^7.14.5", "@babel/plugin-transform-spread": "^7.14.5", "@babel/plugin-transform-sticky-regex": "^7.14.5", "@babel/plugin-transform-template-literals": "^7.14.5", "@babel/plugin-transform-typeof-symbol": "^7.14.5", "@babel/plugin-transform-unicode-escapes": "^7.14.5", "@babel/plugin-transform-unicode-regex": "^7.14.5", "@babel/preset-modules": "^0.1.4", "@babel/types": "^7.14.5", "babel-plugin-polyfill-corejs2": "^0.2.2", "babel-plugin-polyfill-corejs3": "^0.2.2", "babel-plugin-polyfill-regenerator": "^0.2.2", "core-js-compat": "^3.14.0", "semver": "^6.3.0"}}, "@babel/preset-modules": {"version": "0.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/preset-modules/-/preset-modules-0.1.4.tgz", "integrity": "sha1-Ni8raMZihClw/bXiVP/I/BwuQV4=", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4", "@babel/plugin-transform-dotall-regex": "^7.4.4", "@babel/types": "^7.4.4", "esutils": "^2.0.2"}}, "@babel/runtime": {"version": "7.14.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/runtime/-/runtime-7.14.6.tgz", "integrity": "sha1-U1IDvAiS78fexgvcJ7Ls9uQJBi0=", "dev": true, "requires": {"regenerator-runtime": "^0.13.4"}}, "@babel/runtime-corejs3": {"version": "7.14.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/runtime-corejs3/-/runtime-corejs3-7.14.6.tgz", "integrity": "sha1-BmuWbtpASBdAGAyzyquGGj8gjNM=", "requires": {"core-js-pure": "^3.14.0", "regenerator-runtime": "^0.13.4"}}, "@babel/template": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/template/-/template-7.14.5.tgz", "integrity": "sha1-qbydizM1T/blWpxg0RCSAKaJdPQ=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/parser": "^7.14.5", "@babel/types": "^7.14.5"}}, "@babel/traverse": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/traverse/-/traverse-7.14.5.tgz", "integrity": "sha1-wRGw9Yr6tP6j0zhaQG9pJ0jFmHA=", "dev": true, "requires": {"@babel/code-frame": "^7.14.5", "@babel/generator": "^7.14.5", "@babel/helper-function-name": "^7.14.5", "@babel/helper-hoist-variables": "^7.14.5", "@babel/helper-split-export-declaration": "^7.14.5", "@babel/parser": "^7.14.5", "@babel/types": "^7.14.5", "debug": "^4.1.0", "globals": "^11.1.0"}}, "@babel/types": {"version": "7.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@babel/types/-/types-7.14.5.tgz", "integrity": "sha1-O7mXuoKaIQTO2yBonEpbgSHTg/8=", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.14.5", "to-fast-properties": "^2.0.0"}}, "@types/json-schema": {"version": "7.0.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@types/json-schema/-/json-schema-7.0.7.tgz", "integrity": "sha1-mKmTUWyFnrDVxMjwmDF6nqaNua0=", "dev": true}, "@webassemblyjs/ast": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/ast/-/ast-1.9.0.tgz", "integrity": "sha1-vYUGBLQEJFmlpBzX0zjL7Wle2WQ=", "dev": true, "requires": {"@webassemblyjs/helper-module-context": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/wast-parser": "1.9.0"}}, "@webassemblyjs/floating-point-hex-parser": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.9.0.tgz", "integrity": "sha1-PD07Jxvd/ITesA9xNEQ4MR1S/7Q=", "dev": true}, "@webassemblyjs/helper-api-error": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/helper-api-error/-/helper-api-error-1.9.0.tgz", "integrity": "sha1-ID9nbjM7lsnaLuqzzO8zxFkotqI=", "dev": true}, "@webassemblyjs/helper-buffer": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/helper-buffer/-/helper-buffer-1.9.0.tgz", "integrity": "sha1-oUQtJpxf6yP8vJ73WdrDVH8p3gA=", "dev": true}, "@webassemblyjs/helper-code-frame": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/helper-code-frame/-/helper-code-frame-1.9.0.tgz", "integrity": "sha1-ZH+Iks0gQ6gqwMjF51w28dkVnyc=", "dev": true, "requires": {"@webassemblyjs/wast-printer": "1.9.0"}}, "@webassemblyjs/helper-fsm": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/helper-fsm/-/helper-fsm-1.9.0.tgz", "integrity": "sha1-wFJWtxJEIUZx9LCOwQitY7cO3bg=", "dev": true}, "@webassemblyjs/helper-module-context": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/helper-module-context/-/helper-module-context-1.9.0.tgz", "integrity": "sha1-JdiIS3aDmHGgimxvgGw5ee9xLwc=", "dev": true, "requires": {"@webassemblyjs/ast": "1.9.0"}}, "@webassemblyjs/helper-wasm-bytecode": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.9.0.tgz", "integrity": "sha1-T+2L6sm4wU+MWLcNEk1UndH+V5A=", "dev": true}, "@webassemblyjs/helper-wasm-section": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.9.0.tgz", "integrity": "sha1-WkE41aYpK6GLBMWuSXF+QWeWU0Y=", "dev": true, "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-buffer": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/wasm-gen": "1.9.0"}}, "@webassemblyjs/ieee754": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/ieee754/-/ieee754-1.9.0.tgz", "integrity": "sha1-Fceg+6roP7JhQ7us9tbfFwKtOeQ=", "dev": true, "requires": {"@xtuc/ieee754": "^1.2.0"}}, "@webassemblyjs/leb128": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/leb128/-/leb128-1.9.0.tgz", "integrity": "sha1-8Zygt2ptxVYjoJz/p2noOPoeHJU=", "dev": true, "requires": {"@xtuc/long": "4.2.2"}}, "@webassemblyjs/utf8": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/utf8/-/utf8-1.9.0.tgz", "integrity": "sha1-BNM7Y2945qaBMifoJAL3Y3tiKas=", "dev": true}, "@webassemblyjs/wasm-edit": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/wasm-edit/-/wasm-edit-1.9.0.tgz", "integrity": "sha1-P+bXnT8PkiGDqoYALELdJWz+6c8=", "dev": true, "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-buffer": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/helper-wasm-section": "1.9.0", "@webassemblyjs/wasm-gen": "1.9.0", "@webassemblyjs/wasm-opt": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0", "@webassemblyjs/wast-printer": "1.9.0"}}, "@webassemblyjs/wasm-gen": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/wasm-gen/-/wasm-gen-1.9.0.tgz", "integrity": "sha1-ULxw7Gje2OJ2OwGhQYv0NJGnpJw=", "dev": true, "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/ieee754": "1.9.0", "@webassemblyjs/leb128": "1.9.0", "@webassemblyjs/utf8": "1.9.0"}}, "@webassemblyjs/wasm-opt": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/wasm-opt/-/wasm-opt-1.9.0.tgz", "integrity": "sha1-IhEYHlsxMmRDzIES658LkChyGmE=", "dev": true, "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-buffer": "1.9.0", "@webassemblyjs/wasm-gen": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0"}}, "@webassemblyjs/wasm-parser": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/wasm-parser/-/wasm-parser-1.9.0.tgz", "integrity": "sha1-nUjkSCbfSmWYKUqmyHRp1kL/9l4=", "dev": true, "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-api-error": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/ieee754": "1.9.0", "@webassemblyjs/leb128": "1.9.0", "@webassemblyjs/utf8": "1.9.0"}}, "@webassemblyjs/wast-parser": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/wast-parser/-/wast-parser-1.9.0.tgz", "integrity": "sha1-MDERXXmsW9JhVWzsw/qQo+9FGRQ=", "dev": true, "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/floating-point-hex-parser": "1.9.0", "@webassemblyjs/helper-api-error": "1.9.0", "@webassemblyjs/helper-code-frame": "1.9.0", "@webassemblyjs/helper-fsm": "1.9.0", "@xtuc/long": "4.2.2"}}, "@webassemblyjs/wast-printer": {"version": "1.9.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@webassemblyjs/wast-printer/-/wast-printer-1.9.0.tgz", "integrity": "sha1-STXVTIX+9jewDOn1I3dFHQDUeJk=", "dev": true, "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/wast-parser": "1.9.0", "@xtuc/long": "4.2.2"}}, "@xtuc/ieee754": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@xtuc/ieee754/-/ieee754-1.2.0.tgz", "integrity": "sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=", "dev": true}, "@xtuc/long": {"version": "4.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/@xtuc/long/-/long-4.2.2.tgz", "integrity": "sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=", "dev": true}, "acorn": {"version": "6.4.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/acorn/-/acorn-6.4.2.tgz", "integrity": "sha1-NYZv1xBSjpLeEM8GAWSY5H454eY=", "dev": true}, "ajv": {"version": "6.12.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ajv/-/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "dev": true, "requires": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ajv-errors": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ajv-errors/-/ajv-errors-1.0.1.tgz", "integrity": "sha1-81mGrOuRr63sQQL72FAUlQzvpk0=", "dev": true}, "ajv-keywords": {"version": "3.5.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "integrity": "sha1-MfKdpatuANHC0yms97WSlhTVAU0=", "dev": true}, "ansi-regex": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-regex/-/ansi-regex-4.1.0.tgz", "integrity": "sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc=", "dev": true}, "ansi-styles": {"version": "3.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "anymatch": {"version": "3.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/anymatch/-/anymatch-3.1.2.tgz", "integrity": "sha1-wFV8CWrzLxBhmPT04qODU343hxY=", "dev": true, "optional": true, "requires": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}}, "aproba": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/aproba/-/aproba-1.2.0.tgz", "integrity": "sha1-aALmJk79GMeQobDVF/DyYnvyyUo=", "dev": true}, "arr-diff": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/arr-diff/-/arr-diff-4.0.0.tgz", "integrity": "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=", "dev": true}, "arr-flatten": {"version": "1.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/arr-flatten/-/arr-flatten-1.1.0.tgz", "integrity": "sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=", "dev": true}, "arr-union": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/arr-union/-/arr-union-3.1.0.tgz", "integrity": "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=", "dev": true}, "array-unique": {"version": "0.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/array-unique/-/array-unique-0.3.2.tgz", "integrity": "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=", "dev": true}, "asn1.js": {"version": "5.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/asn1.js/-/asn1.js-5.4.1.tgz", "integrity": "sha1-EamAuE67kXgc41sP3C7ilON4Pwc=", "dev": true, "requires": {"bn.js": "^4.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0", "safer-buffer": "^2.1.0"}, "dependencies": {"bn.js": {"version": "4.12.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bn.js/-/bn.js-4.12.0.tgz", "integrity": "sha1-d1s/J477uXGO7HNh9IP7Nvu/6og=", "dev": true}}}, "assert": {"version": "1.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/assert/-/assert-1.5.0.tgz", "integrity": "sha1-VcEJqvbgrv2z3EtxJAxwv1dLGOs=", "dev": true, "requires": {"object-assign": "^4.1.1", "util": "0.10.3"}, "dependencies": {"inherits": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/inherits/-/inherits-2.0.1.tgz", "integrity": "sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE=", "dev": true}, "util": {"version": "0.10.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/util/-/util-0.10.3.tgz", "integrity": "sha1-evsa/lCAUkZInj23/g7TeTNqwPk=", "dev": true, "requires": {"inherits": "2.0.1"}}}}, "assign-symbols": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/assign-symbols/-/assign-symbols-1.0.0.tgz", "integrity": "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=", "dev": true}, "async-each": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/async-each/-/async-each-1.0.3.tgz", "integrity": "sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8=", "dev": true, "optional": true}, "atob": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/atob/-/atob-2.1.2.tgz", "integrity": "sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=", "dev": true}, "babel-loader": {"version": "8.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-loader/-/babel-loader-8.2.2.tgz", "integrity": "sha1-k2POhMEMmkDmx1N0jhRBtgyKC4E=", "dev": true, "requires": {"find-cache-dir": "^3.3.1", "loader-utils": "^1.4.0", "make-dir": "^3.1.0", "schema-utils": "^2.6.5"}}, "babel-plugin-dynamic-import-node": {"version": "2.3.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.3.tgz", "integrity": "sha1-hP2hnJduxcbe/vV/lCez3vZuF6M=", "dev": true, "requires": {"object.assign": "^4.1.0"}}, "babel-plugin-polyfill-corejs2": {"version": "0.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.2.2.tgz", "integrity": "sha1-6R<PERSON>Hheb9lPlLYYp5VOVpMFO/Uyc=", "dev": true, "requires": {"@babel/compat-data": "^7.13.11", "@babel/helper-define-polyfill-provider": "^0.2.2", "semver": "^6.1.1"}}, "babel-plugin-polyfill-corejs3": {"version": "0.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.2.2.tgz", "integrity": "sha1-dCShaC7kS67IFzJ3ELGwlOX49/U=", "dev": true, "requires": {"@babel/helper-define-polyfill-provider": "^0.2.2", "core-js-compat": "^3.9.1"}}, "babel-plugin-polyfill-regenerator": {"version": "0.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.2.2.tgz", "integrity": "sha1-sxDI1kKsraNIwfo7Pmzg6FG+4Hc=", "dev": true, "requires": {"@babel/helper-define-polyfill-provider": "^0.2.2"}}, "balanced-match": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=", "dev": true}, "base": {"version": "0.11.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/base/-/base-0.11.2.tgz", "integrity": "sha1-e95c7RRbbVUakNuH+DxVi060io8=", "dev": true, "requires": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "base64-js": {"version": "1.5.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=", "dev": true}, "big.js": {"version": "5.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/big.js/-/big.js-5.2.2.tgz", "integrity": "sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=", "dev": true}, "binary-extensions": {"version": "2.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/binary-extensions/-/binary-extensions-2.2.0.tgz", "integrity": "sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0=", "dev": true, "optional": true}, "bindings": {"version": "1.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bindings/-/bindings-1.5.0.tgz", "integrity": "sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=", "dev": true, "optional": true, "requires": {"file-uri-to-path": "1.0.0"}}, "bluebird": {"version": "3.7.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bluebird/-/bluebird-3.7.2.tgz", "integrity": "sha1-nyKcFb4nJFT/qXOs4NvueaGww28=", "dev": true}, "bn.js": {"version": "5.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bn.js/-/bn.js-5.2.0.tgz", "integrity": "sha1-NYhgZ0OWxpl3canQUfzBtX1K4AI=", "dev": true}, "brace-expansion": {"version": "1.1.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "2.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/braces/-/braces-2.3.2.tgz", "integrity": "sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=", "dev": true, "requires": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "brorand": {"version": "1.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/brorand/-/brorand-1.1.0.tgz", "integrity": "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=", "dev": true}, "browserify-aes": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/browserify-aes/-/browserify-aes-1.2.0.tgz", "integrity": "sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=", "dev": true, "requires": {"buffer-xor": "^1.0.3", "cipher-base": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.3", "inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "browserify-cipher": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/browserify-cipher/-/browserify-cipher-1.0.1.tgz", "integrity": "sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=", "dev": true, "requires": {"browserify-aes": "^1.0.4", "browserify-des": "^1.0.0", "evp_bytestokey": "^1.0.0"}}, "browserify-des": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/browserify-des/-/browserify-des-1.0.2.tgz", "integrity": "sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=", "dev": true, "requires": {"cipher-base": "^1.0.1", "des.js": "^1.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "browserify-rsa": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/browserify-rsa/-/browserify-rsa-4.1.0.tgz", "integrity": "sha1-sv0Gtbda4pf3zi3GUfkY9b4VjI0=", "dev": true, "requires": {"bn.js": "^5.0.0", "randombytes": "^2.0.1"}}, "browserify-sign": {"version": "4.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/browserify-sign/-/browserify-sign-4.2.1.tgz", "integrity": "sha1-6vSt1G3VS+O7OzbAzxWrvrp5VsM=", "dev": true, "requires": {"bn.js": "^5.1.1", "browserify-rsa": "^4.0.1", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "elliptic": "^6.5.3", "inherits": "^2.0.4", "parse-asn1": "^5.1.5", "readable-stream": "^3.6.0", "safe-buffer": "^5.2.0"}, "dependencies": {"readable-stream": {"version": "3.6.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/readable-stream/-/readable-stream-3.6.0.tgz", "integrity": "sha1-M3u9o63AcGvT4CRCaihtS0sskZg=", "dev": true, "requires": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}}, "safe-buffer": {"version": "5.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "dev": true}}}, "browserify-zlib": {"version": "0.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/browserify-zlib/-/browserify-zlib-0.2.0.tgz", "integrity": "sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=", "dev": true, "requires": {"pako": "~1.0.5"}}, "browserslist": {"version": "4.16.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/browserslist/-/browserslist-4.16.6.tgz", "integrity": "sha1-15ASd6WojlVO0wWxg+ybDAj2b6I=", "dev": true, "requires": {"caniuse-lite": "^1.0.30001219", "colorette": "^1.2.2", "electron-to-chromium": "^1.3.723", "escalade": "^3.1.1", "node-releases": "^1.1.71"}}, "buffer": {"version": "4.9.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/buffer/-/buffer-4.9.2.tgz", "integrity": "sha1-Iw6tNEACmIZEhBqwJEr4xEu+Pvg=", "dev": true, "requires": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}}, "buffer-from": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/buffer-from/-/buffer-from-1.1.1.tgz", "integrity": "sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8=", "dev": true}, "buffer-xor": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/buffer-xor/-/buffer-xor-1.0.3.tgz", "integrity": "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=", "dev": true}, "builtin-status-codes": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz", "integrity": "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=", "dev": true}, "cacache": {"version": "12.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cacache/-/cacache-12.0.4.tgz", "integrity": "sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw=", "dev": true, "requires": {"bluebird": "^3.5.5", "chownr": "^1.1.1", "figgy-pudding": "^3.5.1", "glob": "^7.1.4", "graceful-fs": "^4.1.15", "infer-owner": "^1.0.3", "lru-cache": "^5.1.1", "mississippi": "^3.0.0", "mkdirp": "^0.5.1", "move-concurrently": "^1.0.1", "promise-inflight": "^1.0.1", "rimraf": "^2.6.3", "ssri": "^6.0.1", "unique-filename": "^1.1.1", "y18n": "^4.0.0"}}, "cache-base": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cache-base/-/cache-base-1.0.1.tgz", "integrity": "sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=", "dev": true, "requires": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}}, "call-bind": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "dev": true, "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "camelcase": {"version": "5.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/camelcase/-/camelcase-5.3.1.tgz", "integrity": "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=", "dev": true}, "caniuse-lite": {"version": "1.0.30001237", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/caniuse-lite/-/caniuse-lite-1.0.30001237.tgz", "integrity": "sha1-S3eDZhUVuOcVH8Y3bP2X8OQnueU=", "dev": true}, "chalk": {"version": "2.4.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chalk/-/chalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "chokidar": {"version": "3.5.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chokidar/-/chokidar-3.5.1.tgz", "integrity": "sha1-7pznu+vSt59J8wR5nVRo4x4U5oo=", "dev": true, "optional": true, "requires": {"anymatch": "~3.1.1", "braces": "~3.0.2", "fsevents": "~2.3.1", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.5.0"}, "dependencies": {"braces": {"version": "3.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/braces/-/braces-3.0.2.tgz", "integrity": "sha1-NFThpGLujVmeI23zNs2epPiv4Qc=", "dev": true, "optional": true, "requires": {"fill-range": "^7.0.1"}}, "fill-range": {"version": "7.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fill-range/-/fill-range-7.0.1.tgz", "integrity": "sha1-GRmmp8df44ssfHflGYU12prN2kA=", "dev": true, "optional": true, "requires": {"to-regex-range": "^5.0.1"}}, "is-number": {"version": "7.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-number/-/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "dev": true, "optional": true}, "to-regex-range": {"version": "5.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "dev": true, "optional": true, "requires": {"is-number": "^7.0.0"}}}}, "chownr": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chownr/-/chownr-1.1.4.tgz", "integrity": "sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=", "dev": true}, "chrome-trace-event": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz", "integrity": "sha1-EBXs7UdB4V0GZkqVfbv1DQQeJqw=", "dev": true}, "cipher-base": {"version": "1.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cipher-base/-/cipher-base-1.0.4.tgz", "integrity": "sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94=", "dev": true, "requires": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "class-utils": {"version": "0.3.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/class-utils/-/class-utils-0.3.6.tgz", "integrity": "sha1-+TNprouafOAv1B+q0MqDAzGQxGM=", "dev": true, "requires": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}}}, "cliui": {"version": "5.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cliui/-/cliui-5.0.0.tgz", "integrity": "sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=", "dev": true, "requires": {"string-width": "^3.1.0", "strip-ansi": "^5.2.0", "wrap-ansi": "^5.1.0"}}, "collection-visit": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/collection-visit/-/collection-visit-1.0.0.tgz", "integrity": "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=", "dev": true, "requires": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}}, "color-convert": {"version": "1.9.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "dev": true, "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/color-name/-/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=", "dev": true}, "colorette": {"version": "1.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/colorette/-/colorette-1.2.2.tgz", "integrity": "sha1-y8x51emcrqLb8Q6zom/Ys+as+pQ=", "dev": true}, "commander": {"version": "2.20.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/commander/-/commander-2.20.3.tgz", "integrity": "sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=", "dev": true}, "commondir": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/commondir/-/commondir-1.0.1.tgz", "integrity": "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=", "dev": true}, "component-emitter": {"version": "1.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/component-emitter/-/component-emitter-1.3.0.tgz", "integrity": "sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=", "dev": true}, "concat-map": {"version": "0.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true}, "concat-stream": {"version": "1.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/concat-stream/-/concat-stream-1.6.2.tgz", "integrity": "sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=", "dev": true, "requires": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "console-browserify": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/console-browserify/-/console-browserify-1.2.0.tgz", "integrity": "sha1-ZwY871fOts9Jk6KrOlWECujEkzY=", "dev": true}, "constants-browserify": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/constants-browserify/-/constants-browserify-1.0.0.tgz", "integrity": "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=", "dev": true}, "convert-source-map": {"version": "1.7.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/convert-source-map/-/convert-source-map-1.7.0.tgz", "integrity": "sha1-F6LLiC1/d9NJBYXizmxSRCSjpEI=", "dev": true, "requires": {"safe-buffer": "~5.1.1"}}, "copy-concurrently": {"version": "1.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/copy-concurrently/-/copy-concurrently-1.0.5.tgz", "integrity": "sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=", "dev": true, "requires": {"aproba": "^1.1.1", "fs-write-stream-atomic": "^1.0.8", "iferr": "^0.1.5", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.0"}}, "copy-descriptor": {"version": "0.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/copy-descriptor/-/copy-descriptor-0.1.1.tgz", "integrity": "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=", "dev": true}, "core-js": {"version": "3.14.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/core-js/-/core-js-3.14.0.tgz", "integrity": "sha1-YjIrmMccwgGLAnlxppQZ4kJcKmw="}, "core-js-compat": {"version": "3.14.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/core-js-compat/-/core-js-compat-3.14.0.tgz", "integrity": "sha1-tXTavykYRoHVsWNXvTPRBN89KaU=", "dev": true, "requires": {"browserslist": "^4.16.6", "semver": "7.0.0"}, "dependencies": {"semver": {"version": "7.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/-/semver-7.0.0.tgz", "integrity": "sha1-XzyjV2HkfgWyBsba/yz4FPAxa44=", "dev": true}}}, "core-js-pure": {"version": "3.14.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/core-js-pure/-/core-js-pure-3.14.0.tgz", "integrity": "sha1-crz6y6dKZf/OBL+UrpHZZugO5VM="}, "core-util-is": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=", "dev": true}, "create-ecdh": {"version": "4.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/create-ecdh/-/create-ecdh-4.0.4.tgz", "integrity": "sha1-1uf0v/pmc2CFoHYv06YyaE2rzE4=", "dev": true, "requires": {"bn.js": "^4.1.0", "elliptic": "^6.5.3"}, "dependencies": {"bn.js": {"version": "4.12.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bn.js/-/bn.js-4.12.0.tgz", "integrity": "sha1-d1s/J477uXGO7HNh9IP7Nvu/6og=", "dev": true}}}, "create-hash": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/create-hash/-/create-hash-1.2.0.tgz", "integrity": "sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=", "dev": true, "requires": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "md5.js": "^1.3.4", "ripemd160": "^2.0.1", "sha.js": "^2.4.0"}}, "create-hmac": {"version": "1.1.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/create-hmac/-/create-hmac-1.1.7.tgz", "integrity": "sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=", "dev": true, "requires": {"cipher-base": "^1.0.3", "create-hash": "^1.1.0", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "cross-spawn": {"version": "6.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cross-spawn/-/cross-spawn-6.0.5.tgz", "integrity": "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=", "dev": true, "requires": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "dependencies": {"semver": {"version": "5.7.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/-/semver-5.7.1.tgz", "integrity": "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=", "dev": true}}}, "crypto-browserify": {"version": "3.12.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/crypto-browserify/-/crypto-browserify-3.12.0.tgz", "integrity": "sha1-OWz58xN/A+S45TLFj2mCVOAPgOw=", "dev": true, "requires": {"browserify-cipher": "^1.0.0", "browserify-sign": "^4.0.0", "create-ecdh": "^4.0.0", "create-hash": "^1.1.0", "create-hmac": "^1.1.0", "diffie-hellman": "^5.0.0", "inherits": "^2.0.1", "pbkdf2": "^3.0.3", "public-encrypt": "^4.0.0", "randombytes": "^2.0.0", "randomfill": "^1.0.3"}}, "cyclist": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/cyclist/-/cyclist-1.0.1.tgz", "integrity": "sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk=", "dev": true}, "debug": {"version": "4.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/-/debug-4.3.1.tgz", "integrity": "sha1-8NIpxQXgxtjEmsVT0bE9wYP2su4=", "dev": true, "requires": {"ms": "2.1.2"}}, "decamelize": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/decamelize/-/decamelize-1.2.0.tgz", "integrity": "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=", "dev": true}, "decode-uri-component": {"version": "0.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/decode-uri-component/-/decode-uri-component-0.2.0.tgz", "integrity": "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=", "dev": true}, "define-properties": {"version": "1.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-properties/-/define-properties-1.1.3.tgz", "integrity": "sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=", "dev": true, "requires": {"object-keys": "^1.0.12"}}, "define-property": {"version": "2.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/-/define-property-2.0.2.tgz", "integrity": "sha1-1Flono1lS6d+AqgX+HENcCyxbp0=", "dev": true, "requires": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "dependencies": {"is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "des.js": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/des.js/-/des.js-1.0.1.tgz", "integrity": "sha1-U4IULhvcU/hdhtU+X0qn3rkeCEM=", "dev": true, "requires": {"inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}, "detect-file": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/detect-file/-/detect-file-1.0.0.tgz", "integrity": "sha1-8NZtA2cqglyxtzvbP+YjEMjlUrc=", "dev": true}, "diffie-hellman": {"version": "5.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/diffie-hellman/-/diffie-hellman-5.0.3.tgz", "integrity": "sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=", "dev": true, "requires": {"bn.js": "^4.1.0", "miller-rabin": "^4.0.0", "randombytes": "^2.0.0"}, "dependencies": {"bn.js": {"version": "4.12.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bn.js/-/bn.js-4.12.0.tgz", "integrity": "sha1-d1s/J477uXGO7HNh9IP7Nvu/6og=", "dev": true}}}, "domain-browser": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/domain-browser/-/domain-browser-1.2.0.tgz", "integrity": "sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto=", "dev": true}, "duplexify": {"version": "3.7.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/duplexify/-/duplexify-3.7.1.tgz", "integrity": "sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk=", "dev": true, "requires": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}}, "electron-to-chromium": {"version": "1.3.752", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/electron-to-chromium/-/electron-to-chromium-1.3.752.tgz", "integrity": "sha1-ByhYfxublw7J/62TJJZCmu91DQk=", "dev": true}, "elliptic": {"version": "6.5.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/elliptic/-/elliptic-6.5.4.tgz", "integrity": "sha1-2jfOvTHnmhNn6UG1ku0fvr1Yq7s=", "dev": true, "requires": {"bn.js": "^4.11.9", "brorand": "^1.1.0", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.1", "inherits": "^2.0.4", "minimalistic-assert": "^1.0.1", "minimalistic-crypto-utils": "^1.0.1"}, "dependencies": {"bn.js": {"version": "4.12.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bn.js/-/bn.js-4.12.0.tgz", "integrity": "sha1-d1s/J477uXGO7HNh9IP7Nvu/6og=", "dev": true}}}, "emoji-regex": {"version": "7.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/emoji-regex/-/emoji-regex-7.0.3.tgz", "integrity": "sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=", "dev": true}, "emojis-list": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/emojis-list/-/emojis-list-3.0.0.tgz", "integrity": "sha1-VXBmIEatKeLpFucariYKvf9Pang=", "dev": true}, "end-of-stream": {"version": "1.4.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/end-of-stream/-/end-of-stream-1.4.4.tgz", "integrity": "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=", "dev": true, "requires": {"once": "^1.4.0"}}, "enhanced-resolve": {"version": "4.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/enhanced-resolve/-/enhanced-resolve-4.5.0.tgz", "integrity": "sha1-Lzz9hNvjtIfxjy2y7x4GSlccpew=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "memory-fs": "^0.5.0", "tapable": "^1.0.0"}, "dependencies": {"memory-fs": {"version": "0.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/memory-fs/-/memory-fs-0.5.0.tgz", "integrity": "sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw=", "dev": true, "requires": {"errno": "^0.1.3", "readable-stream": "^2.0.1"}}}}, "errno": {"version": "0.1.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/errno/-/errno-0.1.8.tgz", "integrity": "sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8=", "dev": true, "requires": {"prr": "~1.0.1"}}, "escalade": {"version": "3.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/escalade/-/escalade-3.1.1.tgz", "integrity": "sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=", "dev": true}, "escape-string-regexp": {"version": "1.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=", "dev": true}, "eslint-scope": {"version": "4.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/eslint-scope/-/eslint-scope-4.0.3.tgz", "integrity": "sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=", "dev": true, "requires": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}}, "esrecurse": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/esrecurse/-/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "dev": true, "requires": {"estraverse": "^5.2.0"}, "dependencies": {"estraverse": {"version": "5.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/estraverse/-/estraverse-5.2.0.tgz", "integrity": "sha1-MH30JUfmzHMk088DwVXVzbjFOIA=", "dev": true}}}, "estraverse": {"version": "4.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/estraverse/-/estraverse-4.3.0.tgz", "integrity": "sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=", "dev": true}, "esutils": {"version": "2.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/esutils/-/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=", "dev": true}, "events": {"version": "3.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/events/-/events-3.3.0.tgz", "integrity": "sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=", "dev": true}, "evp_bytestokey": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz", "integrity": "sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=", "dev": true, "requires": {"md5.js": "^1.3.4", "safe-buffer": "^5.1.1"}}, "expand-brackets": {"version": "2.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/expand-brackets/-/expand-brackets-2.1.4.tgz", "integrity": "sha1-t3c14xXOMPa27/D4OwQVGiJEliI=", "dev": true, "requires": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "ms": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "expand-tilde": {"version": "2.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/expand-tilde/-/expand-tilde-2.0.2.tgz", "integrity": "sha1-l+gBqgUt8CRU3kawK/YhZCzchQI=", "dev": true, "requires": {"homedir-polyfill": "^1.0.1"}}, "extend-shallow": {"version": "3.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/-/extend-shallow-3.0.2.tgz", "integrity": "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=", "dev": true, "requires": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-extendable/-/is-extendable-1.0.1.tgz", "integrity": "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=", "dev": true, "requires": {"is-plain-object": "^2.0.4"}}}}, "extglob": {"version": "2.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extglob/-/extglob-2.0.4.tgz", "integrity": "sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=", "dev": true, "requires": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "fast-deep-equal": {"version": "3.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=", "dev": true}, "fast-json-stable-stringify": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=", "dev": true}, "figgy-pudding": {"version": "3.5.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/figgy-pudding/-/figgy-pudding-3.5.2.tgz", "integrity": "sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4=", "dev": true}, "file-uri-to-path": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz", "integrity": "sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=", "dev": true, "optional": true}, "fill-range": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fill-range/-/fill-range-4.0.0.tgz", "integrity": "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=", "dev": true, "requires": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "find-cache-dir": {"version": "3.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/find-cache-dir/-/find-cache-dir-3.3.1.tgz", "integrity": "sha1-ibM/rUpGcNqpT4Vff74x1thP6IA=", "dev": true, "requires": {"commondir": "^1.0.1", "make-dir": "^3.0.2", "pkg-dir": "^4.1.0"}}, "find-up": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/find-up/-/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "dev": true, "requires": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}}, "findup-sync": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/findup-sync/-/findup-sync-3.0.0.tgz", "integrity": "sha1-F7EI+e5RLft6XH88iyfqnhqcCNE=", "dev": true, "requires": {"detect-file": "^1.0.0", "is-glob": "^4.0.0", "micromatch": "^3.0.4", "resolve-dir": "^1.0.1"}}, "flush-write-stream": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/flush-write-stream/-/flush-write-stream-1.1.1.tgz", "integrity": "sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug=", "dev": true, "requires": {"inherits": "^2.0.3", "readable-stream": "^2.3.6"}}, "for-in": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/for-in/-/for-in-1.0.2.tgz", "integrity": "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=", "dev": true}, "fragment-cache": {"version": "0.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fragment-cache/-/fragment-cache-0.2.1.tgz", "integrity": "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=", "dev": true, "requires": {"map-cache": "^0.2.2"}}, "from2": {"version": "2.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/from2/-/from2-2.3.0.tgz", "integrity": "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=", "dev": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "fs-write-stream-atomic": {"version": "1.0.10", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fs-write-stream-atomic/-/fs-write-stream-atomic-1.0.10.tgz", "integrity": "sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "iferr": "^0.1.5", "imurmurhash": "^0.1.4", "readable-stream": "1 || 2"}}, "fs.realpath": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "dev": true}, "fsevents": {"version": "2.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fsevents/-/fsevents-2.3.2.tgz", "integrity": "sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro=", "dev": true, "optional": true}, "function-bind": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/function-bind/-/function-bind-1.1.1.tgz", "integrity": "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=", "dev": true}, "gensync": {"version": "1.0.0-beta.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=", "dev": true}, "get-caller-file": {"version": "2.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=", "dev": true}, "get-intrinsic": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/get-intrinsic/-/get-intrinsic-1.1.1.tgz", "integrity": "sha1-FfWfN2+FXERpY5SPDSTNNje0q8Y=", "dev": true, "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1"}}, "get-value": {"version": "2.0.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/get-value/-/get-value-2.0.6.tgz", "integrity": "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=", "dev": true}, "glob": {"version": "7.1.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/glob/-/glob-7.1.7.tgz", "integrity": "sha1-Oxk+kjPwHULQs/eClLvutBj5SpA=", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-parent": {"version": "5.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "dev": true, "optional": true, "requires": {"is-glob": "^4.0.1"}}, "global-modules": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/global-modules/-/global-modules-2.0.0.tgz", "integrity": "sha1-mXYFrSNF8n9RU5vqJldEISFcd4A=", "dev": true, "requires": {"global-prefix": "^3.0.0"}, "dependencies": {"global-prefix": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/global-prefix/-/global-prefix-3.0.0.tgz", "integrity": "sha1-/IX3MGTfafUEIfR/iD/luRO6m5c=", "dev": true, "requires": {"ini": "^1.3.5", "kind-of": "^6.0.2", "which": "^1.3.1"}}}}, "global-prefix": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/global-prefix/-/global-prefix-1.0.2.tgz", "integrity": "sha1-2/dDxsFJklk8ZVVoy2btMsASLr4=", "dev": true, "requires": {"expand-tilde": "^2.0.2", "homedir-polyfill": "^1.0.1", "ini": "^1.3.4", "is-windows": "^1.0.1", "which": "^1.2.14"}}, "globals": {"version": "11.12.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/globals/-/globals-11.12.0.tgz", "integrity": "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=", "dev": true}, "graceful-fs": {"version": "4.2.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/graceful-fs/-/graceful-fs-4.2.6.tgz", "integrity": "sha1-/wQLKwhTsjw9MQJ1I3BvGIXXa+4=", "dev": true}, "has": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has/-/has-1.0.3.tgz", "integrity": "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=", "dev": true, "requires": {"function-bind": "^1.1.1"}}, "has-flag": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "dev": true}, "has-symbols": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-symbols/-/has-symbols-1.0.2.tgz", "integrity": "sha1-Fl0wcMADCXUqEjakeTMeOsVvFCM=", "dev": true}, "has-value": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-value/-/has-value-1.0.0.tgz", "integrity": "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=", "dev": true, "requires": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}}, "has-values": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-values/-/has-values-1.0.0.tgz", "integrity": "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=", "dev": true, "requires": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "dependencies": {"kind-of": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/-/kind-of-4.0.0.tgz", "integrity": "sha1-IIE989cSkosgc3hpGkUGb65y3Vc=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "hash-base": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/hash-base/-/hash-base-3.1.0.tgz", "integrity": "sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM=", "dev": true, "requires": {"inherits": "^2.0.4", "readable-stream": "^3.6.0", "safe-buffer": "^5.2.0"}, "dependencies": {"readable-stream": {"version": "3.6.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/readable-stream/-/readable-stream-3.6.0.tgz", "integrity": "sha1-M3u9o63AcGvT4CRCaihtS0sskZg=", "dev": true, "requires": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}}, "safe-buffer": {"version": "5.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "dev": true}}}, "hash.js": {"version": "1.1.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/hash.js/-/hash.js-1.1.7.tgz", "integrity": "sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=", "dev": true, "requires": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "hmac-drbg": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/hmac-drbg/-/hmac-drbg-1.0.1.tgz", "integrity": "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=", "dev": true, "requires": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}}, "homedir-polyfill": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/homedir-polyfill/-/homedir-polyfill-1.0.3.tgz", "integrity": "sha1-dDKYzvTlrz4ZQWH7rcwhUdOgWOg=", "dev": true, "requires": {"parse-passwd": "^1.0.0"}}, "https-browserify": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/https-browserify/-/https-browserify-1.0.0.tgz", "integrity": "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=", "dev": true}, "ieee754": {"version": "1.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=", "dev": true}, "iferr": {"version": "0.1.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/iferr/-/iferr-0.1.5.tgz", "integrity": "sha1-xg7taebY/bazEEofy8ocGS3FtQE=", "dev": true}, "import-local": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/import-local/-/import-local-2.0.0.tgz", "integrity": "sha1-VQcL44pZk88Y72236WH1vuXFoJ0=", "dev": true, "requires": {"pkg-dir": "^3.0.0", "resolve-cwd": "^2.0.0"}, "dependencies": {"find-up": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/find-up/-/find-up-3.0.0.tgz", "integrity": "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=", "dev": true, "requires": {"locate-path": "^3.0.0"}}, "locate-path": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/locate-path/-/locate-path-3.0.0.tgz", "integrity": "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=", "dev": true, "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "p-locate": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-locate/-/p-locate-3.0.0.tgz", "integrity": "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=", "dev": true, "requires": {"p-limit": "^2.0.0"}}, "path-exists": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-exists/-/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "dev": true}, "pkg-dir": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pkg-dir/-/pkg-dir-3.0.0.tgz", "integrity": "sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=", "dev": true, "requires": {"find-up": "^3.0.0"}}}}, "imurmurhash": {"version": "0.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true}, "infer-owner": {"version": "1.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/infer-owner/-/infer-owner-1.0.4.tgz", "integrity": "sha1-xM78qo5RBRwqQLos6KPScpWvlGc=", "dev": true}, "inflight": {"version": "1.0.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "dev": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/inherits/-/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "dev": true}, "ini": {"version": "1.3.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ini/-/ini-1.3.8.tgz", "integrity": "sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=", "dev": true}, "interpret": {"version": "1.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/interpret/-/interpret-1.4.0.tgz", "integrity": "sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=", "dev": true}, "is-accessor-descriptor": {"version": "0.1.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz", "integrity": "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "is-binary-path": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-binary-path/-/is-binary-path-2.1.0.tgz", "integrity": "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=", "dev": true, "optional": true, "requires": {"binary-extensions": "^2.0.0"}}, "is-buffer": {"version": "1.1.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha1-76ouqdqg16suoTqXsritUf776L4=", "dev": true}, "is-core-module": {"version": "2.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-core-module/-/is-core-module-2.4.0.tgz", "integrity": "sha1-jp/I4VAnsBFBgCbpjw5vTYYwXME=", "dev": true, "requires": {"has": "^1.0.3"}}, "is-data-descriptor": {"version": "0.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz", "integrity": "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "is-descriptor": {"version": "0.1.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-descriptor/-/is-descriptor-0.1.6.tgz", "integrity": "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=", "dev": true, "requires": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "dependencies": {"kind-of": {"version": "5.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/-/kind-of-5.1.0.tgz", "integrity": "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=", "dev": true}}}, "is-extendable": {"version": "0.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-extendable/-/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "dev": true}, "is-extglob": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true}, "is-fullwidth-code-point": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=", "dev": true}, "is-glob": {"version": "4.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-glob/-/is-glob-4.0.1.tgz", "integrity": "sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw=", "dev": true, "requires": {"is-extglob": "^2.1.1"}}, "is-number": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-number/-/is-number-3.0.0.tgz", "integrity": "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "is-plain-object": {"version": "2.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-plain-object/-/is-plain-object-2.0.4.tgz", "integrity": "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=", "dev": true, "requires": {"isobject": "^3.0.1"}}, "is-windows": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-windows/-/is-windows-1.0.2.tgz", "integrity": "sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=", "dev": true}, "is-wsl": {"version": "1.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-wsl/-/is-wsl-1.1.0.tgz", "integrity": "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=", "dev": true}, "isarray": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "isexe": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true}, "isobject": {"version": "3.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/isobject/-/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8=", "dev": true}, "js-tokens": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk=", "dev": true}, "jsesc": {"version": "2.5.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jsesc/-/jsesc-2.5.2.tgz", "integrity": "sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=", "dev": true}, "json-parse-better-errors": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz", "integrity": "sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=", "dev": true}, "json-schema-traverse": {"version": "0.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "dev": true}, "json5": {"version": "2.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/json5/-/json5-2.2.0.tgz", "integrity": "sha1-Lf7+cgxrpSXZ69kJlQ8FFTFsiaM=", "dev": true, "requires": {"minimist": "^1.2.5"}}, "kind-of": {"version": "6.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/-/kind-of-6.0.3.tgz", "integrity": "sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=", "dev": true}, "loader-runner": {"version": "2.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/loader-runner/-/loader-runner-2.4.0.tgz", "integrity": "sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c=", "dev": true}, "loader-utils": {"version": "1.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/loader-utils/-/loader-utils-1.4.0.tgz", "integrity": "sha1-xXm140yzSxp07cbB+za/o3HVphM=", "dev": true, "requires": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^1.0.1"}, "dependencies": {"json5": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/json5/-/json5-1.0.1.tgz", "integrity": "sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4=", "dev": true, "requires": {"minimist": "^1.2.0"}}}}, "locate-path": {"version": "5.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "dev": true, "requires": {"p-locate": "^4.1.0"}}, "lodash.debounce": {"version": "4.0.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/lodash.debounce/-/lodash.debounce-4.0.8.tgz", "integrity": "sha1-gteb/zCmfEAF/9XiUVMArZyk168=", "dev": true}, "lru-cache": {"version": "5.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=", "dev": true, "requires": {"yallist": "^3.0.2"}}, "make-dir": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/make-dir/-/make-dir-3.1.0.tgz", "integrity": "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=", "dev": true, "requires": {"semver": "^6.0.0"}}, "map-cache": {"version": "0.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/map-cache/-/map-cache-0.2.2.tgz", "integrity": "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=", "dev": true}, "map-visit": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/map-visit/-/map-visit-1.0.0.tgz", "integrity": "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=", "dev": true, "requires": {"object-visit": "^1.0.0"}}, "md5.js": {"version": "1.3.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/md5.js/-/md5.js-1.3.5.tgz", "integrity": "sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=", "dev": true, "requires": {"hash-base": "^3.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "memory-fs": {"version": "0.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/memory-fs/-/memory-fs-0.4.1.tgz", "integrity": "sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=", "dev": true, "requires": {"errno": "^0.1.3", "readable-stream": "^2.0.1"}}, "micromatch": {"version": "3.1.10", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/micromatch/-/micromatch-3.1.10.tgz", "integrity": "sha1-cIWbyVyYQJUvNZoGij/En57PrCM=", "dev": true, "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}}, "miller-rabin": {"version": "4.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/miller-rabin/-/miller-rabin-4.0.1.tgz", "integrity": "sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=", "dev": true, "requires": {"bn.js": "^4.0.0", "brorand": "^1.0.1"}, "dependencies": {"bn.js": {"version": "4.12.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bn.js/-/bn.js-4.12.0.tgz", "integrity": "sha1-d1s/J477uXGO7HNh9IP7Nvu/6og=", "dev": true}}}, "minimalistic-assert": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz", "integrity": "sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=", "dev": true}, "minimalistic-crypto-utils": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz", "integrity": "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=", "dev": true}, "minimatch": {"version": "3.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=", "dev": true, "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/minimist/-/minimist-1.2.5.tgz", "integrity": "sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=", "dev": true}, "mississippi": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/mississippi/-/mississippi-3.0.0.tgz", "integrity": "sha1-6goykfl+C16HdrNj1fChLZTGcCI=", "dev": true, "requires": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^3.0.0", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}}, "mixin-deep": {"version": "1.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/mixin-deep/-/mixin-deep-1.3.2.tgz", "integrity": "sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=", "dev": true, "requires": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-extendable/-/is-extendable-1.0.1.tgz", "integrity": "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=", "dev": true, "requires": {"is-plain-object": "^2.0.4"}}}}, "mkdirp": {"version": "0.5.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/mkdirp/-/mkdirp-0.5.5.tgz", "integrity": "sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=", "dev": true, "requires": {"minimist": "^1.2.5"}}, "move-concurrently": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/move-concurrently/-/move-concurrently-1.0.1.tgz", "integrity": "sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=", "dev": true, "requires": {"aproba": "^1.1.1", "copy-concurrently": "^1.0.0", "fs-write-stream-atomic": "^1.0.8", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.3"}}, "ms": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/-/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "dev": true}, "nan": {"version": "2.14.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/nan/-/nan-2.14.2.tgz", "integrity": "sha1-9TdkAGlRaPTMaUrJOT0MlYXu6hk=", "dev": true, "optional": true}, "nanomatch": {"version": "1.2.13", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/nanomatch/-/nanomatch-1.2.13.tgz", "integrity": "sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=", "dev": true, "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}}, "neo-async": {"version": "2.6.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/neo-async/-/neo-async-2.6.2.tgz", "integrity": "sha1-tKr7k+OustgXTKU88WOrfXMIMF8=", "dev": true}, "nice-try": {"version": "1.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/nice-try/-/nice-try-1.0.5.tgz", "integrity": "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=", "dev": true}, "node-libs-browser": {"version": "2.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/node-libs-browser/-/node-libs-browser-2.2.1.tgz", "integrity": "sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU=", "dev": true, "requires": {"assert": "^1.1.1", "browserify-zlib": "^0.2.0", "buffer": "^4.3.0", "console-browserify": "^1.1.0", "constants-browserify": "^1.0.0", "crypto-browserify": "^3.11.0", "domain-browser": "^1.1.1", "events": "^3.0.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "0.0.1", "process": "^0.11.10", "punycode": "^1.2.4", "querystring-es3": "^0.2.0", "readable-stream": "^2.3.3", "stream-browserify": "^2.0.1", "stream-http": "^2.7.2", "string_decoder": "^1.0.0", "timers-browserify": "^2.0.4", "tty-browserify": "0.0.0", "url": "^0.11.0", "util": "^0.11.0", "vm-browserify": "^1.0.1"}, "dependencies": {"punycode": {"version": "1.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/punycode/-/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4=", "dev": true}}}, "node-releases": {"version": "1.1.73", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/node-releases/-/node-releases-1.1.73.tgz", "integrity": "sha1-3U6B3dUnf/hGuAtSu0DEnt96eyA=", "dev": true}, "normalize-path": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/normalize-path/-/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "dev": true, "optional": true}, "object-assign": {"version": "4.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true}, "object-copy": {"version": "0.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object-copy/-/object-copy-0.1.0.tgz", "integrity": "sha1-fn2Fi3gb18mRpBupde04EnVOmYw=", "dev": true, "requires": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "kind-of": {"version": "3.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "object-keys": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object-keys/-/object-keys-1.1.1.tgz", "integrity": "sha1-HEfyct8nfzsdrwYWd9nILiMixg4=", "dev": true}, "object-visit": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object-visit/-/object-visit-1.0.1.tgz", "integrity": "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=", "dev": true, "requires": {"isobject": "^3.0.0"}}, "object.assign": {"version": "4.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object.assign/-/object.assign-4.1.2.tgz", "integrity": "sha1-DtVKNC7Os3s4/3brgxoOeIy2OUA=", "dev": true, "requires": {"call-bind": "^1.0.0", "define-properties": "^1.1.3", "has-symbols": "^1.0.1", "object-keys": "^1.1.1"}}, "object.pick": {"version": "1.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/object.pick/-/object.pick-1.3.0.tgz", "integrity": "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=", "dev": true, "requires": {"isobject": "^3.0.1"}}, "once": {"version": "1.4.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "dev": true, "requires": {"wrappy": "1"}}, "os-browserify": {"version": "0.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/os-browserify/-/os-browserify-0.3.0.tgz", "integrity": "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=", "dev": true}, "p-limit": {"version": "2.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "4.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "dev": true, "requires": {"p-limit": "^2.2.0"}}, "p-try": {"version": "2.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-try/-/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "dev": true}, "pako": {"version": "1.0.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pako/-/pako-1.0.11.tgz", "integrity": "sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=", "dev": true}, "parallel-transform": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/parallel-transform/-/parallel-transform-1.2.0.tgz", "integrity": "sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw=", "dev": true, "requires": {"cyclist": "^1.0.1", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}}, "parse-asn1": {"version": "5.1.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/parse-asn1/-/parse-asn1-5.1.6.tgz", "integrity": "sha1-OFCAo+wTy2KmLTlAnLPoiETNrtQ=", "dev": true, "requires": {"asn1.js": "^5.2.0", "browserify-aes": "^1.0.0", "evp_bytestokey": "^1.0.0", "pbkdf2": "^3.0.3", "safe-buffer": "^5.1.1"}}, "parse-passwd": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/parse-passwd/-/parse-passwd-1.0.0.tgz", "integrity": "sha1-bVuTSkVpk7I9N/QKOC1vFmao5cY=", "dev": true}, "pascalcase": {"version": "0.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pascalcase/-/pascalcase-0.1.1.tgz", "integrity": "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=", "dev": true}, "path-browserify": {"version": "0.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-browserify/-/path-browserify-0.0.1.tgz", "integrity": "sha1-5sTd1+06onxoogzE5Q4aTug7vEo=", "dev": true}, "path-dirname": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-dirname/-/path-dirname-1.0.2.tgz", "integrity": "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=", "dev": true, "optional": true}, "path-exists": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "dev": true}, "path-is-absolute": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "dev": true}, "path-key": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-key/-/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=", "dev": true}, "path-parse": {"version": "1.0.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=", "dev": true}, "pbkdf2": {"version": "3.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pbkdf2/-/pbkdf2-3.1.2.tgz", "integrity": "sha1-3YIqoIh1gOUvGgOdw+2hCO+uMHU=", "dev": true, "requires": {"create-hash": "^1.1.2", "create-hmac": "^1.1.4", "ripemd160": "^2.0.1", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "picomatch": {"version": "2.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/picomatch/-/picomatch-2.3.0.tgz", "integrity": "sha1-8fBh3o9qS/AiiS4tEoI0+5gwKXI=", "dev": true, "optional": true}, "pify": {"version": "4.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pify/-/pify-4.0.1.tgz", "integrity": "sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=", "dev": true}, "pkg-dir": {"version": "4.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pkg-dir/-/pkg-dir-4.2.0.tgz", "integrity": "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=", "dev": true, "requires": {"find-up": "^4.0.0"}}, "posix-character-classes": {"version": "0.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/posix-character-classes/-/posix-character-classes-0.1.1.tgz", "integrity": "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=", "dev": true}, "process": {"version": "0.11.10", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/process/-/process-0.11.10.tgz", "integrity": "sha1-czIwDoQBYb2j5podHZGn1LwW8YI=", "dev": true}, "process-nextick-args": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha1-eCDZsWEgzFXKmud5JoCufbptf+I=", "dev": true}, "promise-inflight": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/promise-inflight/-/promise-inflight-1.0.1.tgz", "integrity": "sha1-mEcocL8igTL8vdhoEputEsPAKeM=", "dev": true}, "prr": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/prr/-/prr-1.0.1.tgz", "integrity": "sha1-0/wRS6BplaRexok/SEzrHXj19HY=", "dev": true}, "public-encrypt": {"version": "4.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/public-encrypt/-/public-encrypt-4.0.3.tgz", "integrity": "sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=", "dev": true, "requires": {"bn.js": "^4.1.0", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "parse-asn1": "^5.0.0", "randombytes": "^2.0.1", "safe-buffer": "^5.1.2"}, "dependencies": {"bn.js": {"version": "4.12.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/bn.js/-/bn.js-4.12.0.tgz", "integrity": "sha1-d1s/J477uXGO7HNh9IP7Nvu/6og=", "dev": true}}}, "pump": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pump/-/pump-3.0.0.tgz", "integrity": "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=", "dev": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "pumpify": {"version": "1.5.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pumpify/-/pumpify-1.5.1.tgz", "integrity": "sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4=", "dev": true, "requires": {"duplexify": "^3.6.0", "inherits": "^2.0.3", "pump": "^2.0.0"}, "dependencies": {"pump": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pump/-/pump-2.0.1.tgz", "integrity": "sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=", "dev": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}}}, "punycode": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/punycode/-/punycode-2.1.1.tgz", "integrity": "sha1-tYsBCsQMIsVldhbI0sLALHv0eew=", "dev": true}, "querystring": {"version": "0.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/querystring/-/querystring-0.2.0.tgz", "integrity": "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=", "dev": true}, "querystring-es3": {"version": "0.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/querystring-es3/-/querystring-es3-0.2.1.tgz", "integrity": "sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=", "dev": true}, "randombytes": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/randombytes/-/randombytes-2.1.0.tgz", "integrity": "sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=", "dev": true, "requires": {"safe-buffer": "^5.1.0"}}, "randomfill": {"version": "1.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/randomfill/-/randomfill-1.0.4.tgz", "integrity": "sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=", "dev": true, "requires": {"randombytes": "^2.0.5", "safe-buffer": "^5.1.0"}}, "readable-stream": {"version": "2.3.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/readable-stream/-/readable-stream-2.3.7.tgz", "integrity": "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "readdirp": {"version": "3.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/readdirp/-/readdirp-3.5.0.tgz", "integrity": "sha1-m6dMAZsV02UnjS6Ru4xI17TULJ4=", "dev": true, "optional": true, "requires": {"picomatch": "^2.2.1"}}, "regenerate": {"version": "1.4.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/regenerate/-/regenerate-1.4.2.tgz", "integrity": "sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=", "dev": true}, "regenerate-unicode-properties": {"version": "8.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/regenerate-unicode-properties/-/regenerate-unicode-properties-8.2.0.tgz", "integrity": "sha1-5d5xEdZV57pgwFfb6f83yH5lzew=", "dev": true, "requires": {"regenerate": "^1.4.0"}}, "regenerator-runtime": {"version": "0.13.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/regenerator-runtime/-/regenerator-runtime-0.13.7.tgz", "integrity": "sha1-ysLazIoepnX+qrrriugziYrkb1U="}, "regenerator-transform": {"version": "0.14.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/regenerator-transform/-/regenerator-transform-0.14.5.tgz", "integrity": "sha1-yY2hVGg2ccnE3LFuznNlF+G3/rQ=", "dev": true, "requires": {"@babel/runtime": "^7.8.4"}}, "regex-not": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/regex-not/-/regex-not-1.0.2.tgz", "integrity": "sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=", "dev": true, "requires": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}}, "regexpu-core": {"version": "4.7.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/regexpu-core/-/regexpu-core-4.7.1.tgz", "integrity": "sha1-LepamgcjMpj78NuR+pq8TG4PitY=", "dev": true, "requires": {"regenerate": "^1.4.0", "regenerate-unicode-properties": "^8.2.0", "regjsgen": "^0.5.1", "regjsparser": "^0.6.4", "unicode-match-property-ecmascript": "^1.0.4", "unicode-match-property-value-ecmascript": "^1.2.0"}}, "regjsgen": {"version": "0.5.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/regjsgen/-/regjsgen-0.5.2.tgz", "integrity": "sha1-kv8pX7He7L9uzaslQ9IH6RqjNzM=", "dev": true}, "regjsparser": {"version": "0.6.9", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/regjsparser/-/regjsparser-0.6.9.tgz", "integrity": "sha1-tInu98mizkNydicBFCnPgzpxg+Y=", "dev": true, "requires": {"jsesc": "~0.5.0"}, "dependencies": {"jsesc": {"version": "0.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/jsesc/-/jsesc-0.5.0.tgz", "integrity": "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=", "dev": true}}}, "remove-trailing-separator": {"version": "1.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz", "integrity": "sha1-wkvOKig62tW8P1jg1IJJuSN52O8=", "dev": true, "optional": true}, "repeat-element": {"version": "1.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/repeat-element/-/repeat-element-1.1.4.tgz", "integrity": "sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek=", "dev": true}, "repeat-string": {"version": "1.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/repeat-string/-/repeat-string-1.6.1.tgz", "integrity": "sha1-jcrkcOHIirwtYA//Sndihtp15jc=", "dev": true}, "require-directory": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I=", "dev": true}, "require-main-filename": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/require-main-filename/-/require-main-filename-2.0.0.tgz", "integrity": "sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=", "dev": true}, "resolve": {"version": "1.20.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/resolve/-/resolve-1.20.0.tgz", "integrity": "sha1-YpoBP7P3B1XW8LeTXMHCxTeLGXU=", "dev": true, "requires": {"is-core-module": "^2.2.0", "path-parse": "^1.0.6"}}, "resolve-cwd": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/resolve-cwd/-/resolve-cwd-2.0.0.tgz", "integrity": "sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=", "dev": true, "requires": {"resolve-from": "^3.0.0"}}, "resolve-dir": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/resolve-dir/-/resolve-dir-1.0.1.tgz", "integrity": "sha1-eaQGRMNivoLybv/nOcm7U4IEb0M=", "dev": true, "requires": {"expand-tilde": "^2.0.0", "global-modules": "^1.0.0"}, "dependencies": {"global-modules": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/global-modules/-/global-modules-1.0.0.tgz", "integrity": "sha1-bXcPDrUjrHgWTXK15xqIdyZcw+o=", "dev": true, "requires": {"global-prefix": "^1.0.1", "is-windows": "^1.0.1", "resolve-dir": "^1.0.0"}}}}, "resolve-from": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/resolve-from/-/resolve-from-3.0.0.tgz", "integrity": "sha1-six699nWiBvItuZTM17rywoYh0g=", "dev": true}, "resolve-url": {"version": "0.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/resolve-url/-/resolve-url-0.2.1.tgz", "integrity": "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=", "dev": true}, "ret": {"version": "0.1.15", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ret/-/ret-0.1.15.tgz", "integrity": "sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=", "dev": true}, "rimraf": {"version": "2.7.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/rimraf/-/rimraf-2.7.1.tgz", "integrity": "sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=", "dev": true, "requires": {"glob": "^7.1.3"}}, "ripemd160": {"version": "2.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ripemd160/-/ripemd160-2.0.2.tgz", "integrity": "sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=", "dev": true, "requires": {"hash-base": "^3.0.0", "inherits": "^2.0.1"}}, "run-queue": {"version": "1.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/run-queue/-/run-queue-1.0.3.tgz", "integrity": "sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=", "dev": true, "requires": {"aproba": "^1.1.1"}}, "sa-sdk-javascript": {"version": "1.22.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/sa-sdk-javascript/-/sa-sdk-javascript-1.22.2.tgz", "integrity": "sha1-jdbgzFEJHjpm2ObticojUrwDRt8="}, "sa-sdk-miniprogram": {"version": "1.17.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/sa-sdk-miniprogram/-/sa-sdk-miniprogram-1.17.7.tgz", "integrity": "sha1-1rQhxK1hBfq5SkaCrsIN2UetZ4o="}, "safe-buffer": {"version": "5.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "dev": true}, "safe-regex": {"version": "1.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/safe-regex/-/safe-regex-1.1.0.tgz", "integrity": "sha1-QKNmnzsHfR6UPURinhV91IAjvy4=", "dev": true, "requires": {"ret": "~0.1.10"}}, "safer-buffer": {"version": "2.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "dev": true}, "schema-utils": {"version": "2.7.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/schema-utils/-/schema-utils-2.7.1.tgz", "integrity": "sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=", "dev": true, "requires": {"@types/json-schema": "^7.0.5", "ajv": "^6.12.4", "ajv-keywords": "^3.5.2"}}, "semver": {"version": "6.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/-/semver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "dev": true}, "serialize-javascript": {"version": "4.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/serialize-javascript/-/serialize-javascript-4.0.0.tgz", "integrity": "sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao=", "dev": true, "requires": {"randombytes": "^2.1.0"}}, "set-blocking": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/set-blocking/-/set-blocking-2.0.0.tgz", "integrity": "sha1-BF+XgtARrppoA93TgrJDkrPYkPc=", "dev": true}, "set-value": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/set-value/-/set-value-2.0.1.tgz", "integrity": "sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=", "dev": true, "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "setimmediate": {"version": "1.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/setimmediate/-/setimmediate-1.0.5.tgz", "integrity": "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=", "dev": true}, "sha.js": {"version": "2.4.11", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/sha.js/-/sha.js-2.4.11.tgz", "integrity": "sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=", "dev": true, "requires": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "shebang-command": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/shebang-command/-/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "dev": true, "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/shebang-regex/-/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=", "dev": true}, "snapdragon": {"version": "0.8.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/snapdragon/-/snapdragon-0.8.2.tgz", "integrity": "sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=", "dev": true, "requires": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "ms": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "snapdragon-node": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/snapdragon-node/-/snapdragon-node-2.1.1.tgz", "integrity": "sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=", "dev": true, "requires": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "snapdragon-util": {"version": "3.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/snapdragon-util/-/snapdragon-util-3.0.1.tgz", "integrity": "sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=", "dev": true, "requires": {"kind-of": "^3.2.0"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "source-list-map": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-list-map/-/source-list-map-2.0.1.tgz", "integrity": "sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=", "dev": true}, "source-map": {"version": "0.5.7", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true}, "source-map-resolve": {"version": "0.5.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map-resolve/-/source-map-resolve-0.5.3.tgz", "integrity": "sha1-GQhmvs51U+H48mei7oLGBrVQmho=", "dev": true, "requires": {"atob": "^2.1.2", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "source-map-support": {"version": "0.5.19", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map-support/-/source-map-support-0.5.19.tgz", "integrity": "sha1-qYti+G3K9PZzmWSMCFKRq56P7WE=", "dev": true, "requires": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "source-map-url": {"version": "0.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map-url/-/source-map-url-0.4.1.tgz", "integrity": "sha1-CvZmBadFpaL5HPG7+KevvCg97FY=", "dev": true}, "split-string": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/split-string/-/split-string-3.1.0.tgz", "integrity": "sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=", "dev": true, "requires": {"extend-shallow": "^3.0.0"}}, "ssri": {"version": "6.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/ssri/-/ssri-6.0.2.tgz", "integrity": "sha1-FXk5E08gRk5zAd26PpD/qPdyisU=", "dev": true, "requires": {"figgy-pudding": "^3.5.1"}}, "static-extend": {"version": "0.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/static-extend/-/static-extend-0.1.2.tgz", "integrity": "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=", "dev": true, "requires": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}}}, "stream-browserify": {"version": "2.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/stream-browserify/-/stream-browserify-2.0.2.tgz", "integrity": "sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs=", "dev": true, "requires": {"inherits": "~2.0.1", "readable-stream": "^2.0.2"}}, "stream-each": {"version": "1.2.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/stream-each/-/stream-each-1.2.3.tgz", "integrity": "sha1-6+J6DDibBPvMIzZClS4Qcxr6m64=", "dev": true, "requires": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}}, "stream-http": {"version": "2.8.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/stream-http/-/stream-http-2.8.3.tgz", "integrity": "sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw=", "dev": true, "requires": {"builtin-status-codes": "^3.0.0", "inherits": "^2.0.1", "readable-stream": "^2.3.6", "to-arraybuffer": "^1.0.0", "xtend": "^4.0.0"}}, "stream-shift": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/stream-shift/-/stream-shift-1.0.1.tgz", "integrity": "sha1-1wiCgVWasneEJCebCHfaPDktWj0=", "dev": true}, "string-width": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/string-width/-/string-width-3.1.0.tgz", "integrity": "sha1-InZ74htirxCBV0MG9prFG2IgOWE=", "dev": true, "requires": {"emoji-regex": "^7.0.1", "is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^5.1.0"}}, "string_decoder": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "requires": {"safe-buffer": "~5.1.0"}}, "strip-ansi": {"version": "5.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/strip-ansi/-/strip-ansi-5.2.0.tgz", "integrity": "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=", "dev": true, "requires": {"ansi-regex": "^4.1.0"}}, "supports-color": {"version": "5.5.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "dev": true, "requires": {"has-flag": "^3.0.0"}}, "tapable": {"version": "1.1.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tapable/-/tapable-1.1.3.tgz", "integrity": "sha1-ofzMBrWNth/XpF2i2kT186Pme6I=", "dev": true}, "terser": {"version": "4.8.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/terser/-/terser-4.8.0.tgz", "integrity": "sha1-YwVjQ9fHC7KfOvZlhlpG/gOg3xc=", "dev": true, "requires": {"commander": "^2.20.0", "source-map": "~0.6.1", "source-map-support": "~0.5.12"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "terser-webpack-plugin": {"version": "1.4.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/terser-webpack-plugin/-/terser-webpack-plugin-1.4.5.tgz", "integrity": "sha1-oheu+uozDnNP+sthIOwfoxLWBAs=", "dev": true, "requires": {"cacache": "^12.0.2", "find-cache-dir": "^2.1.0", "is-wsl": "^1.1.0", "schema-utils": "^1.0.0", "serialize-javascript": "^4.0.0", "source-map": "^0.6.1", "terser": "^4.1.2", "webpack-sources": "^1.4.0", "worker-farm": "^1.7.0"}, "dependencies": {"find-cache-dir": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/find-cache-dir/-/find-cache-dir-2.1.0.tgz", "integrity": "sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=", "dev": true, "requires": {"commondir": "^1.0.1", "make-dir": "^2.0.0", "pkg-dir": "^3.0.0"}}, "find-up": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/find-up/-/find-up-3.0.0.tgz", "integrity": "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=", "dev": true, "requires": {"locate-path": "^3.0.0"}}, "locate-path": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/locate-path/-/locate-path-3.0.0.tgz", "integrity": "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=", "dev": true, "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "make-dir": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/make-dir/-/make-dir-2.1.0.tgz", "integrity": "sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=", "dev": true, "requires": {"pify": "^4.0.1", "semver": "^5.6.0"}}, "p-locate": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-locate/-/p-locate-3.0.0.tgz", "integrity": "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=", "dev": true, "requires": {"p-limit": "^2.0.0"}}, "path-exists": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-exists/-/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "dev": true}, "pkg-dir": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/pkg-dir/-/pkg-dir-3.0.0.tgz", "integrity": "sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=", "dev": true, "requires": {"find-up": "^3.0.0"}}, "schema-utils": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/schema-utils/-/schema-utils-1.0.0.tgz", "integrity": "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=", "dev": true, "requires": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}}, "semver": {"version": "5.7.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/semver/-/semver-5.7.1.tgz", "integrity": "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=", "dev": true}, "source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "through2": {"version": "2.0.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/through2/-/through2-2.0.5.tgz", "integrity": "sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=", "dev": true, "requires": {"readable-stream": "~2.3.6", "xtend": "~4.0.1"}}, "timers-browserify": {"version": "2.0.12", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/timers-browserify/-/timers-browserify-2.0.12.tgz", "integrity": "sha1-RKRcEfv0B/NPl7zNFXfGUjYbAO4=", "dev": true, "requires": {"setimmediate": "^1.0.4"}}, "to-arraybuffer": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz", "integrity": "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=", "dev": true}, "to-fast-properties": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/to-fast-properties/-/to-fast-properties-2.0.0.tgz", "integrity": "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=", "dev": true}, "to-object-path": {"version": "0.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/to-object-path/-/to-object-path-0.3.0.tgz", "integrity": "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "to-regex": {"version": "3.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/to-regex/-/to-regex-3.0.2.tgz", "integrity": "sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=", "dev": true, "requires": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}}, "to-regex-range": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/to-regex-range/-/to-regex-range-2.1.1.tgz", "integrity": "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=", "dev": true, "requires": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}}, "tty-browserify": {"version": "0.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/tty-browserify/-/tty-browserify-0.0.0.tgz", "integrity": "sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=", "dev": true}, "typedarray": {"version": "0.0.6", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=", "dev": true}, "unicode-canonical-property-names-ecmascript": {"version": "1.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-1.0.4.tgz", "integrity": "sha1-JhmADEyCWADv3YNDr33Zkzy+KBg=", "dev": true}, "unicode-match-property-ecmascript": {"version": "1.0.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-1.0.4.tgz", "integrity": "sha1-jtKjJWmWG86SJ9Cc0/+7j+1fAgw=", "dev": true, "requires": {"unicode-canonical-property-names-ecmascript": "^1.0.4", "unicode-property-aliases-ecmascript": "^1.0.4"}}, "unicode-match-property-value-ecmascript": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-1.2.0.tgz", "integrity": "sha1-DZH2AO7rMJaqlisdb8iIduZOpTE=", "dev": true}, "unicode-property-aliases-ecmascript": {"version": "1.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-1.1.0.tgz", "integrity": "sha1-3Vepn2IHvt/0Yoq++5TFDblByPQ=", "dev": true}, "union-value": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/union-value/-/union-value-1.0.1.tgz", "integrity": "sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=", "dev": true, "requires": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^2.0.1"}}, "unique-filename": {"version": "1.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/unique-filename/-/unique-filename-1.1.1.tgz", "integrity": "sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=", "dev": true, "requires": {"unique-slug": "^2.0.0"}}, "unique-slug": {"version": "2.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/unique-slug/-/unique-slug-2.0.2.tgz", "integrity": "sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=", "dev": true, "requires": {"imurmurhash": "^0.1.4"}}, "unset-value": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/unset-value/-/unset-value-1.0.0.tgz", "integrity": "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=", "dev": true, "requires": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "dependencies": {"has-value": {"version": "0.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-value/-/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "dev": true, "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "dependencies": {"isobject": {"version": "2.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "dev": true, "requires": {"isarray": "1.0.0"}}}}, "has-values": {"version": "0.1.4", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/has-values/-/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E=", "dev": true}}}, "upath": {"version": "1.2.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/upath/-/upath-1.2.0.tgz", "integrity": "sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=", "dev": true, "optional": true}, "uri-js": {"version": "4.4.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=", "dev": true, "requires": {"punycode": "^2.1.0"}}, "urix": {"version": "0.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/urix/-/urix-0.1.0.tgz", "integrity": "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=", "dev": true}, "url": {"version": "0.11.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/url/-/url-0.11.0.tgz", "integrity": "sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=", "dev": true, "requires": {"punycode": "1.3.2", "querystring": "0.2.0"}, "dependencies": {"punycode": {"version": "1.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/punycode/-/punycode-1.3.2.tgz", "integrity": "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=", "dev": true}}}, "use": {"version": "3.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/use/-/use-3.1.1.tgz", "integrity": "sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=", "dev": true}, "util": {"version": "0.11.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/util/-/util-0.11.1.tgz", "integrity": "sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE=", "dev": true, "requires": {"inherits": "2.0.3"}, "dependencies": {"inherits": {"version": "2.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=", "dev": true}}}, "util-deprecate": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "dev": true}, "v8-compile-cache": {"version": "2.3.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/v8-compile-cache/-/v8-compile-cache-2.3.0.tgz", "integrity": "sha1-LeGWGMZtwkfc+2+ZM4A12CRaLO4=", "dev": true}, "vm-browserify": {"version": "1.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/vm-browserify/-/vm-browserify-1.1.2.tgz", "integrity": "sha1-eGQcSIuObKkadfUR56OzKobl3aA=", "dev": true}, "watchpack": {"version": "1.7.5", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/watchpack/-/watchpack-1.7.5.tgz", "integrity": "sha1-EmfmxV4Lm1vkTCAjrtVDeiwmxFM=", "dev": true, "requires": {"chokidar": "^3.4.1", "graceful-fs": "^4.1.2", "neo-async": "^2.5.0", "watchpack-chokidar2": "^2.0.1"}}, "watchpack-chokidar2": {"version": "2.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/watchpack-chokidar2/-/watchpack-chokidar2-2.0.1.tgz", "integrity": "sha1-OFAAcu5uzmbzdpk2lQ6hdxvhyVc=", "dev": true, "optional": true, "requires": {"chokidar": "^2.1.8"}, "dependencies": {"anymatch": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/anymatch/-/anymatch-2.0.0.tgz", "integrity": "sha1-vLJLTzeTTZqnrBe0ra+J58du8us=", "dev": true, "optional": true, "requires": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}, "dependencies": {"normalize-path": {"version": "2.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "dev": true, "optional": true, "requires": {"remove-trailing-separator": "^1.0.1"}}}}, "binary-extensions": {"version": "1.13.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/binary-extensions/-/binary-extensions-1.13.1.tgz", "integrity": "sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=", "dev": true, "optional": true}, "chokidar": {"version": "2.1.8", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/chokidar/-/chokidar-2.1.8.tgz", "integrity": "sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc=", "dev": true, "optional": true, "requires": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "fsevents": "^1.2.7", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1"}}, "fsevents": {"version": "1.2.13", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/fsevents/-/fsevents-1.2.13.tgz", "integrity": "sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=", "dev": true, "optional": true, "requires": {"bindings": "^1.5.0", "nan": "^2.12.1"}}, "glob-parent": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/glob-parent/-/glob-parent-3.1.0.tgz", "integrity": "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=", "dev": true, "optional": true, "requires": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}, "dependencies": {"is-glob": {"version": "3.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-glob/-/is-glob-3.1.0.tgz", "integrity": "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=", "dev": true, "optional": true, "requires": {"is-extglob": "^2.1.0"}}}}, "is-binary-path": {"version": "1.0.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/is-binary-path/-/is-binary-path-1.0.1.tgz", "integrity": "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=", "dev": true, "optional": true, "requires": {"binary-extensions": "^1.0.0"}}, "readdirp": {"version": "2.2.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/readdirp/-/readdirp-2.2.1.tgz", "integrity": "sha1-DodiKjMlqjPokihcr4tOhGUppSU=", "dev": true, "optional": true, "requires": {"graceful-fs": "^4.1.11", "micromatch": "^3.1.10", "readable-stream": "^2.0.2"}}}}, "webpack": {"version": "4.46.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/webpack/-/webpack-4.46.0.tgz", "integrity": "sha1-v5tEBOogoHNgXgoBHRiNd8tq1UI=", "dev": true, "requires": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-module-context": "1.9.0", "@webassemblyjs/wasm-edit": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0", "acorn": "^6.4.1", "ajv": "^6.10.2", "ajv-keywords": "^3.4.1", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^4.5.0", "eslint-scope": "^4.0.3", "json-parse-better-errors": "^1.0.2", "loader-runner": "^2.4.0", "loader-utils": "^1.2.3", "memory-fs": "^0.4.1", "micromatch": "^3.1.10", "mkdirp": "^0.5.3", "neo-async": "^2.6.1", "node-libs-browser": "^2.2.1", "schema-utils": "^1.0.0", "tapable": "^1.1.3", "terser-webpack-plugin": "^1.4.3", "watchpack": "^1.7.4", "webpack-sources": "^1.4.1"}, "dependencies": {"schema-utils": {"version": "1.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/schema-utils/-/schema-utils-1.0.0.tgz", "integrity": "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=", "dev": true, "requires": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}}}}, "webpack-cli": {"version": "3.3.12", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/webpack-cli/-/webpack-cli-3.3.12.tgz", "integrity": "sha1-lOmtoIFFPNCqYJyZ5QABL9OtLUo=", "dev": true, "requires": {"chalk": "^2.4.2", "cross-spawn": "^6.0.5", "enhanced-resolve": "^4.1.1", "findup-sync": "^3.0.0", "global-modules": "^2.0.0", "import-local": "^2.0.0", "interpret": "^1.4.0", "loader-utils": "^1.4.0", "supports-color": "^6.1.0", "v8-compile-cache": "^2.1.1", "yargs": "^13.3.2"}, "dependencies": {"supports-color": {"version": "6.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/supports-color/-/supports-color-6.1.0.tgz", "integrity": "sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "webpack-sources": {"version": "1.4.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/webpack-sources/-/webpack-sources-1.4.3.tgz", "integrity": "sha1-7t2OwLko+/HL/plOItLYkPMwqTM=", "dev": true, "requires": {"source-list-map": "^2.0.0", "source-map": "~0.6.1"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "which": {"version": "1.3.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/which/-/which-1.3.1.tgz", "integrity": "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=", "dev": true, "requires": {"isexe": "^2.0.0"}}, "which-module": {"version": "2.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/which-module/-/which-module-2.0.0.tgz", "integrity": "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=", "dev": true}, "worker-farm": {"version": "1.7.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/worker-farm/-/worker-farm-1.7.0.tgz", "integrity": "sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag=", "dev": true, "requires": {"errno": "~0.1.7"}}, "wrap-ansi": {"version": "5.1.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/wrap-ansi/-/wrap-ansi-5.1.0.tgz", "integrity": "sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=", "dev": true, "requires": {"ansi-styles": "^3.2.0", "string-width": "^3.0.0", "strip-ansi": "^5.0.0"}}, "wrappy": {"version": "1.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "dev": true}, "xtend": {"version": "4.0.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/xtend/-/xtend-4.0.2.tgz", "integrity": "sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=", "dev": true}, "y18n": {"version": "4.0.3", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/y18n/-/y18n-4.0.3.tgz", "integrity": "sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8=", "dev": true}, "yallist": {"version": "3.1.1", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/yallist/-/yallist-3.1.1.tgz", "integrity": "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=", "dev": true}, "yargs": {"version": "13.3.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/yargs/-/yargs-13.3.2.tgz", "integrity": "sha1-rX/+/sGqWVZayRX4Lcyzipwxot0=", "dev": true, "requires": {"cliui": "^5.0.0", "find-up": "^3.0.0", "get-caller-file": "^2.0.1", "require-directory": "^2.1.1", "require-main-filename": "^2.0.0", "set-blocking": "^2.0.0", "string-width": "^3.0.0", "which-module": "^2.0.0", "y18n": "^4.0.0", "yargs-parser": "^13.1.2"}, "dependencies": {"find-up": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/find-up/-/find-up-3.0.0.tgz", "integrity": "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=", "dev": true, "requires": {"locate-path": "^3.0.0"}}, "locate-path": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/locate-path/-/locate-path-3.0.0.tgz", "integrity": "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=", "dev": true, "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "p-locate": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/p-locate/-/p-locate-3.0.0.tgz", "integrity": "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=", "dev": true, "requires": {"p-limit": "^2.0.0"}}, "path-exists": {"version": "3.0.0", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/path-exists/-/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "dev": true}}}, "yargs-parser": {"version": "13.1.2", "resolved": "http://artifactory.sf-express.com:80/artifactory/api/npm/npm/yargs-parser/-/yargs-parser-13.1.2.tgz", "integrity": "sha1-Ew8JcC667vJlDVTObj5XBvek+zg=", "dev": true, "requires": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}}}}
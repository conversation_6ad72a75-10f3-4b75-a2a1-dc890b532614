{"name": "@ky/sensors", "version": "1.1.6", "description": "快运前端神策工具类", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "webpack --config webpack.config.js"}, "files": ["web-sensors.js", "web-sensors.min.js", "wx-sensors.js", "wx-sensors.min.js"], "keywords": [], "author": "", "license": "ISC", "dependencies": {"@babel/runtime-corejs3": "^7.14.6", "core-js": "^3.14.0", "sa-sdk-javascript": "1.22.2", "sa-sdk-miniprogram": "^1.17.7"}, "devDependencies": {"@babel/core": "^7.14.6", "@babel/plugin-transform-runtime": "^7.14.5", "@babel/preset-env": "^7.14.5", "babel-loader": "^8.2.2", "terser-webpack-plugin": "^1.3.0", "webpack": "^4.34.0", "webpack-cli": "^3.3.4"}}
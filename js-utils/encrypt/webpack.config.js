const path = require('path');
const webpack = require('webpack');
const ParallelUglifyPlugin = require('webpack-parallel-uglify-plugin');

const {
  version = '0.0.1',
  name = 'LargeEncrypt',
  license = 'MIT',
  repository = '',
  author = 'Jieyu - 01417462',
} = {};

const banner = `
  ${name} v${version}
  ${repository.url}
  Copyright (c) ${author.replace(/ *\<[^)]*\> */g, ' ')}
  This source code is licensed under the ${license} license found in the
  LICENSE file in the root directory of this source tree.
`;

module.exports = {
  mode: 'production',
  entry: './src/index.js',
  output: {
    filename: `${name}.js`,
    path: path.resolve(__dirname, 'build'),
    libraryExport: 'default',
    library: name,
    libraryTarget: 'umd',
  },
  module: {
    rules: [
      {
        test: /\.m?js$/,
        exclude: /(node_modules|bower_components)/,
        use: {
          loader: 'babel-loader',
        },
      },
    ],
  },
  plugins: [
    // new PrettierPlugin(),
    new webpack.DefinePlugin({
      'process.env.DEPLOY_ENV': JSON.stringify(process.env.DEPLOY_ENV),
    }),
    new webpack.BannerPlugin(banner),
    new ParallelUglifyPlugin({
      cacheDir: '.cache/',
      uglifyJS: {
        output: {
          comments: false,
        },
        compress: {},
        warnings: false,
      },
    }),
  ],
};

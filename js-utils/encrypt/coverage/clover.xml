<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1570938126042" clover="3.2.0">
  <project timestamp="1570938126042" name="All files">
    <metrics statements="4" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="1" elements="6" coveredelements="4" complexity="0" loc="4" ncloc="4" packages="2" files="2" classes="2"/>
    <package name="scripts">
      <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="testMock.js" path="/home/<USER>/work/js-library-boilerplate-basic/js-library-boilerplate-basic/scripts/testMock.js">
        <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src">
      <metrics statements="3" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="1"/>
      <file name="index.js" path="/home/<USER>/work/js-library-boilerplate-basic/js-library-boilerplate-basic/src/index.js">
        <metrics statements="3" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="1"/>
        <line num="5" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>

new KYFeedbackPC({
  appCode: 'EOS-FMS-OMS',
  userId: sessionStorage.userid, // 员工工号
  onOpen: () => {},
  onSuccess: () => {},
  onClose: () => {},
});
// var a = window.Feedback({
//   userId: '', // 员工工号
//   urlMatches: /\S/,
//   showHeader: true, // 是否显示导航头
//   onOpen: () => {
//     console.log('open');
//     // 无导航头可以调用hybrid方法设置头部标题或其他逻辑
//   },
//   onSuccess: () => {
//     console.log('success');
//     // 无导航头可以调用hybrid方法设置头部标题或其他逻辑
//   },
//   onClose: () => {
//     console.log('close');
//     // 无导航头可以调用hybrid方法设置头部标题或其他逻辑
//   },
//   showIcon: true, // 是否显示悬浮Icon,不显示可以自行调用实例方法的open方法显示反馈组件
//   gapWidth: 0, // 反馈图标距离左右边距
//   gapTop: 0, // 反馈图标上边距
//   gapBottom: 0, // 反馈图标下边距
// });

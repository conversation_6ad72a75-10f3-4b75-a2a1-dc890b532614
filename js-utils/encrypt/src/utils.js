export function loadScript(src) {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = src;
    script.onerror = () => {
      reject();
    };
    script.onload = () => {
      resolve(true);
    };
    const s = document.getElementsByTagName('script')[0];
    try {
      s.parentNode.insertBefore(script, s);
    } catch (error) {
      reject();
    }
  });
}

/**
 * 字节转十六进制
 * @param {*} hex
 */
export function hexToBytes(hex) {
  const bytes = [];
  for (let c = 0; c < hex.length; c += 2) {
    bytes.push(parseInt(hex.substr(c, 2), 16));
  }
  return bytes;
}

export function base64Encode(buffer) {
  let binary = '';
  const bytes = new Uint8Array(buffer);
  const len = bytes.byteLength;

  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }

  return window.btoa(binary);
}
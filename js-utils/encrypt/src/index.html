<!DOCTYPE html>
<html lang="en" style="padding:0;margin:0;">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no,minimal-ui" />
  <meta name="format-detection" content="telephone=no, email=no" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-touch-fullscreen" content="yes" />
  <meta name="x5-fullscreen" content="true" />
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
  <meta http-equiv="Pragma" content="no-cache" />
  <meta http-equiv="Expires" content="0" />
  <title>快运反馈插件-PC</title>
  <base target="_parent" />
</head>

<body class="">
  <nav id="sidebar" class="behavior_1">
    <div class="wrap">
      <div class="profile">
        <a href="/">
          <img src="/static/upload/201605/logo.jpg" alt="李成银的随笔" />
        </a>
        <span>李成银的随笔</span>
      </div>
      <ul class="buttons">
        <li>
          <a href="/" title="主页">
            <i class="iconfont icon-home"></i> <span>主页</span></a>
        </li>
        <li>
          <a href="/archives" title="归档">
            <i class="iconfont icon-archive"></i> <span>归档</span></a>
        </li>
        <li>
          <a href="/links" title="链接">
            <i class="iconfont icon-link"></i> <span>链接</span></a>
        </li>
        <li>
          <a href="/tags" title="标签">
            <i class="iconfont icon-tags"></i> <span>标签</span></a>
        </li>
        <li>
          <a href="/about" title="关于">
            <i class="iconfont icon-user"></i> <span>关于</span></a>
        </li>
      </ul>
      <ul class="buttons">
        <li>
          <a class="inline" rel="nofollow" target="_blank" href="https://github.com/welefen">
            <i class="iconfont icon-github-v" title="GitHub"></i></a>
          <a class="inline" rel="nofollow" target="_blank" href="https://twitter.com/welefen">
            <i class="iconfont icon-twitter-v" title="Twitter"></i></a>
          <a class="inline" href="/rss.html">
            <i class="iconfont icon-rss-v" title="RSS"></i></a>
          <a class="inline" href="/search.html">
            <i class="iconfont icon-search" title="Search"></i></a>
        </li>
      </ul>
    </div>
  </nav>
  <div id="header">
    <div class="btn-bar"><i></i></div>
    <h1><a href="/">李成银的随笔</a></h1>
    <a class="me" href="/about/"><img src="/static/upload/201605/logo.jpg" alt="李成银的随笔" /></a>
  </div>
  <div id="sidebar-mask" style="display: none;"></div>
  <div id="main">
    <div id="page-post">
      <article class="post detail">
        <div class="meta">
          <div class="date">01月03, 2020</div>
          <div class="comment"><a href="#comments">0 comments</a></div>
        </div>
        <h1 class="title">解决 html2canvas 处理 svg 时样式丢失的问题</h1>
        <div class="entry-content">
          <div class="toc">
            <ul>
              <li><a href="#toc-198">html2canvas 实现机制</a></li>
              <li>
                <a href="#toc-c6c">修复 svg 的样式问题</a>
                <ul>
                  <li><a href="#toc-12e">样式直接定义在节点上</a></li>
                  <li>
                    <a href="#toc-cab">动态将外层定义的样式重新赋值到节点上</a>
                  </li>
                  <li><a href="#toc-633">svg 中含有图片的处理</a></li>
                  <li><a href="#toc-112">svg 含有自定义字体的处理</a></li>
                  <li><a href="#toc-12f">svg 的其他处理方案</a></li>
                </ul>
              </li>
              <li><a href="#toc-5eb">性能问题</a></li>
              <li><a href="#toc-91f">部分事件的额外执行</a></li>
            </ul>
          </div>
          <p>
            在项目开发中，经常会遇到截图的需求（如：把结果生成图片用于分享，生成模块的封面图），此时用
            html2canvas 来完成这个操作是最适合不过了。
          </p>
          <!--more-->

          <p>
            html2canvas 是一款非常优秀的截图 JS
            工具，直接在浏览器端运行，不依赖服务端。当前最新版本是
            <code>1.0.0-rc.5</code>，使用起来很简单，<a href="http://welefen.com/lab/html2canvas/index.html">DEMO</a>：
          </p>
          <pre><code class="language-js firekylin-code"><ul><li><span class="line-num" data-line="1"></span>html2canvas(document.body).then(function(canvas) {</li><li><span class="line-num" data-line="2"></span>  document.body.appendChild(canvas);</li><li><span class="line-num" data-line="3"></span>  // 把 canvas 转成图片</li><li><span class="line-num" data-line="4"></span>  // const image = new Image();</li><li><span class="line-num" data-line="5"></span>  // image.src = canvas.toDataURL("image/png");</li><li><span class="line-num" data-line="6"></span>  // document.body.appendChild(image);</li><li><span class="line-num" data-line="7"></span>});</li></ul></code></pre>
          <p>
            html2canvas 会自动识别普通 html
            节点内容中的样式，不管样式是在外层用 link/style
            定义的，还是直接写在元素上的。但如果截图的内容中含有
            svg，且样式是在外层定义的话，会发现样式丢失，<a href="http://welefen.com/lab/html2canvas/svg.html">DEMO</a>：
          </p>
          <p>
            原始内容给 svg text
            节点设置了字体大小和颜色，截图中这二个样式都丢失了。
          </p>
          <p>
            为了弄清楚 html2canvas 在处理 svg 时样式丢失的问题，先需要弄清楚
            html2canvas 的处理机制。
          </p>
          <h2>
            <a id="toc-198" class="anchor" href="#toc-198"></a>html2canvas
            实现机制
          </h2>
          <p>
            html2canvas
            的实现机制是通过遍历分析指定节点以及子节点的样式，然后通过 canvas
            API 在 canvas 中绘制出来，具体如下：
          </p>
          <ul>
            <li>
              为了避免对当前页面的影响，将当前页面的所有节点拷贝到一个 iframe
              中
              <ul>
                <li>从 <code>ownerDocument</code> 开始递归，拷贝节点</li>
                <li>如果节点是 script，则忽略</li>
                <li>如果节点命中了 <code>ignoreElements</code> 则忽略</li>
                <li>记录指定节点对应的拷贝节点</li>
                <li>
                  动态创建 <code>iframe</code>，将拷贝的节点写入到
                  <code>iframe</code> 中
                </li>
                <li>调用 <code>onclone</code> 方法</li>
              </ul>
            </li>
            <li>
              通过内部的
              <code>parseTree</code> 方法遍历解析拷贝节点及子节点，生成对应的
              container，如：<code>ImageElementContainer</code>，<code>SVGElementContainer</code>，<code>CanvasElementContainer</code>
              等
            </li>
            <li>
              最后通过 <code>CanvasRenderer</code> 或
              <code>ForeignObjectRenderer</code> 类来渲染这些节点
            </li>
          </ul>
          <p>
            从上面的流程中可以知道，<code>svg</code> 节点是通过
            <code>SVGElementContainer</code> 来处理的。通过代码发现
            <code>svg</code> 的处理非常简单粗暴：
          </p>
          <pre><code class="language-js firekylin-code"><ul><li><span class="line-num" data-line="1"></span>export class SVGElementContainer extends ElementContainer {</li><li><span class="line-num" data-line="2"></span>    svg: string;</li><li><span class="line-num" data-line="3"></span>    intrinsicWidth: number;</li><li><span class="line-num" data-line="4"></span>    intrinsicHeight: number;</li><li><span class="line-num" data-line="5"></span>    constructor(img: SVGSVGElement) {</li><li><span class="line-num" data-line="6"></span>        super(img);</li><li><span class="line-num" data-line="7"></span>        const s = new XMLSerializer();</li><li><span class="line-num" data-line="8"></span>        this.svg = `data:image/svg+xml,${encodeURIComponent(s.serializeToString(img))}`;</li><li><span class="line-num" data-line="9"></span>        this.intrinsicWidth = img.width.baseVal.value;</li><li><span class="line-num" data-line="10"></span>        this.intrinsicHeight = img.height.baseVal.value;</li><li><span class="line-num" data-line="11"></span>        CacheStorage.getInstance().addImage(this.svg);</li><li><span class="line-num" data-line="12"></span>    }</li><li><span class="line-num" data-line="13"></span>}</li></ul></code></pre>
          <p>
            也就是说，对于 <code>svg</code>，是直接将 svg
            序列化，然后转成图片来处理的。这样一来，定义在外部的样式序列化时无法携带，自然也就丢失了。
          </p>
          <h2>
            <a id="toc-c6c" class="anchor" href="#toc-c6c"></a>修复 svg
            的样式问题
          </h2>
          <p>
            知道了
            <code>svg</code>
            是序列化后转成图片来处理的，只要保证在序列化时携带上样式，生成的图片后就没有问题了。
          </p>
          <h3>
            <a id="toc-12e" class="anchor" href="#toc-12e"></a>样式直接定义在节点上
          </h3>
          <p>
            最简单的办法就是，将原本定义在外层的样式写在 svg 节点上，<a href="http://welefen.com/lab/html2canvas/svg-fix1.html">DEMO</a>：
          </p>
          <pre><code class="language-html firekylin-code"><ul><li><span class="line-num" data-line="1"></span>&lt;svg width="200" height="100"&gt;</li><li><span class="line-num" data-line="2"></span>   &lt;text x="10" y="30" style="font-size: 24px;fill: red"&gt;svg text 内容&lt;/text&gt;</li><li><span class="line-num" data-line="3"></span>&lt;/svg&gt;</li></ul></code></pre>
          <p>
            这样处理虽然能解决问题，但比较麻烦，尤其是 svg
            是放在数据库/文件等地方存储时。
          </p>
          <h3>
            <a id="toc-cab" class="anchor" href="#toc-cab"></a>动态将外层定义的样式重新赋值到节点上
          </h3>
          <p>
            除了手工把样式写在 svg
            节点上外，也可以在生成截图前解析节点的样式，然后把样式重新设置到节点上，这样
            svg 序列化时携带这些信息，生成的图片就会有这些样式了。
          </p>
          <p>
            前面流程中提到，页面所有节点拷贝完成后会调用
            <code>onclone</code> 方法，那么就可以在这个方法里动态设置，<a href="http://welefen.com/lab/html2canvas/svg-fix2.html">DEMO</a>：
          </p>
          <pre><code class="language-js firekylin-code"><ul><li><span class="line-num" data-line="1"></span>html2canvas(container, {</li><li><span class="line-num" data-line="2"></span>    onclone(html) {</li><li><span class="line-num" data-line="3"></span>      const textNodes = $(html).find('svg.svg text');</li><li><span class="line-num" data-line="4"></span>      // 根据需要修改要重新赋值的属性</li><li><span class="line-num" data-line="5"></span>      const styles = ['font-size', 'fill']; </li><li><span class="line-num" data-line="6"></span>      textNodes.each(function() {</li><li><span class="line-num" data-line="7"></span>        const textStyle = window.getComputedStyle(this);</li><li><span class="line-num" data-line="8"></span>        styles.forEach(item =&gt; {</li><li><span class="line-num" data-line="9"></span>          const value = textStyle.getPropertyValue(item);</li><li><span class="line-num" data-line="10"></span>          $(this).css(item, value);</li><li><span class="line-num" data-line="11"></span>        })</li><li><span class="line-num" data-line="12"></span>      })</li><li><span class="line-num" data-line="13"></span>    }</li><li><span class="line-num" data-line="14"></span>})</li></ul></code></pre>
          <h3>
            <a id="toc-633" class="anchor" href="#toc-633"></a>svg
            中含有图片的处理
          </h3>
          <p>
            如果 svg
            中含有图片且图片地址是远程的，直接序列化后截图中并不能正常显示图片。借助
            <code>onclone</code> 可以返回 <code>promise</code>，把图片转成
            <code>base64</code> 后回写到原图片中，<a href="http://welefen.com/lab/html2canvas/svg-fix3.html">DEMO</a>：
          </p>
          <pre><code class="language-js firekylin-code"><ul><li><span class="line-num" data-line="1"></span>onclone(html) {</li><li><span class="line-num" data-line="2"></span>  const imageNodes = $(html).find('svg.svg image');</li><li><span class="line-num" data-line="3"></span>  const promises = [];</li><li><span class="line-num" data-line="4"></span>  imageNodes.each(function() {</li><li><span class="line-num" data-line="5"></span>    const element = $(this);</li><li><span class="line-num" data-line="6"></span>    const href = element.attr('href');</li><li><span class="line-num" data-line="7"></span>    if(href.startsWith('base64')) return;</li><li><span class="line-num" data-line="8"></span>    const promise = new Promise((resolve, reject) =&gt; {</li><li><span class="line-num" data-line="9"></span>      const img = new Image();</li><li><span class="line-num" data-line="10"></span>      img.crossOrigin = 'anonymous';</li><li><span class="line-num" data-line="11"></span>      img.onload = function() {</li><li><span class="line-num" data-line="12"></span>        const width = parseFloat(element.css('width'));</li><li><span class="line-num" data-line="13"></span>        const height = parseFloat(element.css('height'));</li><li><span class="line-num" data-line="14"></span>        const canvas = document.createElement('canvas');</li><li><span class="line-num" data-line="15"></span>        canvas.width = width;</li><li><span class="line-num" data-line="16"></span>        canvas.height = height;</li><li><span class="line-num" data-line="17"></span>        const ctx = canvas.getContext("2d");</li><li><span class="line-num" data-line="18"></span>        ctx.drawImage(img, 0, 0, width, height);</li><li><span class="line-num" data-line="19"></span>        const base64 = canvas.toDataURL("image/jpeg");</li><li><span class="line-num" data-line="20"></span>        element.attr('href', base64);</li><li><span class="line-num" data-line="21"></span>        resolve();</li><li><span class="line-num" data-line="22"></span>      }</li><li><span class="line-num" data-line="23"></span>      img.onerror = reject;</li><li><span class="line-num" data-line="24"></span>      img.src = href;</li><li><span class="line-num" data-line="25"></span>    });</li><li><span class="line-num" data-line="26"></span>    promises.push(promise);</li><li><span class="line-num" data-line="27"></span>  })</li><li><span class="line-num" data-line="28"></span>  return Promise.all(promises)</li><li><span class="line-num" data-line="29"></span>}</li></ul></code></pre>
          <h3>
            <a id="toc-112" class="anchor" href="#toc-112"></a>svg
            含有自定义字体的处理
          </h3>
          <p>
            有时候页面中通过 <code>@font-face</code> 引用了自定义字体，如果
            svg 中用到了这个字体，那么必须要把字体以内联的方式放置到 svg
            中，才能让生成的图片中字体一致，<a href="http://welefen.com/lab/html2canvas/svg-font.html">DEMO</a>：
          </p>
          <pre><code class="language-js firekylin-code"><ul><li><span class="line-num" data-line="1"></span>onclone(html) {</li><li><span class="line-num" data-line="2"></span>  const promises = [];</li><li><span class="line-num" data-line="3"></span>  const svg = $(html).find('svg.svg');</li><li><span class="line-num" data-line="4"></span>  svg.find('text').each(function() {</li><li><span class="line-num" data-line="5"></span>    const family = window.getComputedStyle(this).getPropertyValue('font-family');</li><li><span class="line-num" data-line="6"></span>    if(family === 'Merriweather') {</li><li><span class="line-num" data-line="7"></span>      $(this).css('font-family', family)</li><li><span class="line-num" data-line="8"></span>      const promise = fetch(`https://lib.baomitu.com/fonts/merriweather/merriweather-regular.woff2`, {</li><li><span class="line-num" data-line="9"></span>      }).then(res =&gt; res.blob()).then(data =&gt; {</li><li><span class="line-num" data-line="10"></span>        return new Promise((resolve, reject) =&gt; {</li><li><span class="line-num" data-line="11"></span>          const fr = new FileReader();</li><li><span class="line-num" data-line="12"></span>          fr.onload = function(e) {</li><li><span class="line-num" data-line="13"></span>            resolve(e.target.result)</li><li><span class="line-num" data-line="14"></span>          }</li><li><span class="line-num" data-line="15"></span>          fr.readAsDataURL(data)</li><li><span class="line-num" data-line="16"></span>        })</li><li><span class="line-num" data-line="17"></span>      }).then(data =&gt; {</li><li><span class="line-num" data-line="18"></span>        const node = `&lt;defs&gt;</li><li><span class="line-num" data-line="19"></span>        &lt;style&gt;</li><li><span class="line-num" data-line="20"></span>          @font-face {</li><li><span class="line-num" data-line="21"></span>            font-family: 'Merriweather';</li><li><span class="line-num" data-line="22"></span>            font-style: normal;</li><li><span class="line-num" data-line="23"></span>            font-weight: regular;</li><li><span class="line-num" data-line="24"></span>            src: local('Merriweather'), local('Merriweather-Normal'), url('${data}') format('woff2');</li><li><span class="line-num" data-line="25"></span>          }</li><li><span class="line-num" data-line="26"></span>        &lt;/style&gt;</li><li><span class="line-num" data-line="27"></span>        &lt;/defs&gt;`;</li><li><span class="line-num" data-line="28"></span>        $(node).insertBefore(svg.children(":first"));</li><li><span class="line-num" data-line="29"></span>      })</li><li><span class="line-num" data-line="30"></span>      promises.push(promise);</li><li><span class="line-num" data-line="31"></span>    }</li><li><span class="line-num" data-line="32"></span>  })</li><li><span class="line-num" data-line="33"></span>  return Promise.all(promises).then(() =&gt; {</li><li><span class="line-num" data-line="34"></span>    console.log(svg[0])</li><li><span class="line-num" data-line="35"></span>  });</li><li><span class="line-num" data-line="36"></span>}</li></ul></code></pre>
          <p>
            通过获取元素的
            <code>font-family</code>，然后动态加载对应的字体，转成
            <code>base64</code>，生成 <code>defs</code> 标签插入到 svg 中。
          </p>
          <h3>
            <a id="toc-12f" class="anchor" href="#toc-12f"></a>svg
            的其他处理方案
          </h3>
          <p>
            除了 html2canvas 里把 svg
            转成图片的方案，也可以使用其他的方案，如：<code>canvg</code>
            是一款把 svg 转成 canvas 并支持事件、动效等功能的 JS
            库。这样就可以利用 <code>canvg</code> 库，把 svg 转成 canvas
            后，替换原有的 svg 节点。
          </p>
          <p>
            不过利用 <code>canvg</code> 也需要自己处理 svg
            里使用的外部样式，同时引入 <code>canvg</code> 也需要加载一个额外的
            JS 文件，是否需要可以根据项目评估。
          </p>
          <h2><a id="toc-5eb" class="anchor" href="#toc-5eb"></a>性能问题</h2>
          <p>
            有时候会发现使用
            <code>html2canvas</code>
            截图时比较慢，原因在于处理时会把当前页面拷贝一份到 iframe
            中操作。如果页面很复杂，节点数量很多，拷贝节点所花费的时间就会比较长。
          </p>
          <p>
            可以通过
            <code>ignoreElements</code>
            方法来处理这个问题，在拷贝的时候把不需要的节点忽略掉，提升拷贝的速度，<a
              href="http://welefen.com/lab/html2canvas/svg-performace.html">DEMO</a>。
          </p>
          <pre><code class="language-js firekylin-code"><ul><li><span class="line-num" data-line="1"></span>ignoreElements: el =&gt; {</li><li><span class="line-num" data-line="2"></span>  const tagName = el.tagName.toLowerCase();</li><li><span class="line-num" data-line="3"></span>  const list = ['head', 'body', 'style', 'title', 'meta']</li><li><span class="line-num" data-line="4"></span>  if(list.includes(tagName)) return false;</li><li><span class="line-num" data-line="5"></span>  // id="extra" 下所有节点忽略</li><li><span class="line-num" data-line="6"></span>  if(el.id === 'extra') return true;</li><li><span class="line-num" data-line="7"></span>  return false;</li><li><span class="line-num" data-line="8"></span>}</li></ul></code></pre>
          <p>
            除了
            <code>ignoreElements</code>
            方法中自己指定忽略的元素外，也可以在元素上添加
            <code>data-html2canvas-ignore</code> 属性忽略。
          </p>
          <pre><code class="language-html firekylin-code"><ul><li><span class="line-num" data-line="1"></span>&lt;div data-html2canvas-ignore class="test"&gt;</li><li><span class="line-num" data-line="2"></span> 忽略的节点内容</li><li><span class="line-num" data-line="3"></span>&lt;/div&gt;</li></ul></code></pre>
          <h2>
            <a id="toc-91f" class="anchor" href="#toc-91f"></a>部分事件的额外执行
          </h2>
          <p>
            截图时，由于是把当前页面拷贝到 iframe 中操作的，虽然会自动忽略
            script
            等节点，但如果元素上有属性事件时，默认还是会拷贝过去。这样会导致事件的多次执行，<a href="http://welefen.com/lab/html2canvas/svg-img1.html">DEMO</a>：
          </p>
          <pre><code class="language-html firekylin-code"><ul><li><span class="line-num" data-line="1"></span>&lt;img src="https://p0.ssl.qhimg.com/t01d22afab8ee7b5a06.jpg" onload="xxx('图片加载完成')" width="100"&gt;</li></ul></code></pre>
          <p>
            如果方法
            <code>xxx</code>
            是系统内置的函数，则会再执行一次。如果是外部的函数，则会报错
            <code>xxx</code> 不存在。
          </p>
          <p>
            处理办法是在
            <code>ignoreElements</code>
            中判断，然后移除这个属性，不过这个是操作原本的元素，不是拷贝后的元素，这个影响是否可以接受需要根据项目评估，<a
              href="http://welefen.com/lab/html2canvas/svg-img.html">DEMO</a>。
          </p>
          <pre><code class="language-js firekylin-code"><ul><li><span class="line-num" data-line="1"></span>ignoreElements: function(el) {</li><li><span class="line-num" data-line="2"></span>  if(el.tagName === 'IMG') {</li><li><span class="line-num" data-line="3"></span>    el.removeAttribute('onload')</li><li><span class="line-num" data-line="4"></span>  }</li><li><span class="line-num" data-line="5"></span>}</li></ul></code></pre>
        </div>
        <p>
          本文链接：<a
            href="http://www.welefen.com/post/html2canvas-with-svg.html">http://www.welefen.com/post/html2canvas-with-svg.html</a>
        </p>
        <p>-- <abbr title="End of File">EOF</abbr> --</p>
        <div class="post-info">
          <p>
            作者
            <a href="/author/welefen" data-user="welefen">
              <code class="notebook">welefen</code></a>
            发表于 <i>2020-01-03 10:50:39</i> ，添加在分类
            <a href="/cate/" data-cate="前端开发">
              <code class="notebook">前端开发</code></a>
            下 ，最后修改于 <i>2020-04-24 13:16:26</i>
          </p>
        </div>
      </article>
      <nav class="pagination">
        <a href="/post/buy-insurance-in-hongkong.html" class="prev">« 香港买保险及游记</a>
        <a href="/post/how-to-optimize-firekylin-theme.html" class="next">如何优化 Firekylin 博客主题 »</a>
      </nav>
      <div id="comments" data-type="custom" data-thread-key="d41d8cd98f00b204e9800998ecf8427e"
        data-url="http://www.welefen.com/post/html2canvas-with-svg.html">
        <h1 class="title">Comments</h1>
        <!---->
      </div>
    </div>
    <footer id="footer" class="inner">
      © 2020&nbsp;-&nbsp; 李成银的随笔 &nbsp;-&nbsp;<a target="_blank" rel="nofollow" class="external beian"
        href="http://beian.miit.gov.cn">备案号：皖ICP备16024379号-2</a>
      <br />
      Powered by&nbsp;<a target="_blank" href="https://thinkjs.org">ThinkJS</a>&nbsp;&amp;&nbsp;<a target="_blank"
        rel="nofollow" class="external" href="https://firekylin.org">FireKylin 1.3.1</a>
    </footer>
  </div>
</body>

</html>
import { hexToBytes, loadScript, base64Encode } from './utils';

class LargeEncrypt {
  constructor(options) {
    const defaultOptions = {
      privateKey: '',
      publicKey: '',
    };
    const mergeOptions = {
      ...defaultOptions,
      ...options,
    };
    for (const option in mergeOptions) {
      if (Object.prototype.hasOwnProperty.call(mergeOptions, option)) {
        this[option] = mergeOptions[option];
      }
    }
    this.initPromise = this.init();
  }

  getPublicKey() {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.onreadystatechange = function onreadystatechange() {
        if (xhr.readyState === 4) {
          const res = JSON.parse(xhr.response);
          if (res.success) {
            resolve(res.obj);
          } else {
            reject(res.errorMessage);
          }
        }
      };
      xhr.open('GET', `/eosFmsSfpsServices/userFeedback/getPublicKey`, true);
      xhr.setRequestHeader('content-type', 'application/json');
      xhr.send();
    });
  }

  async encrypt(input) {
    if (!input) {
      return { success: false, message: 'input 字符串不能为空' };
    }
    await this.initPromise;
    if (
      !('JSEncrypt' in window) ||
      !('encryptLong' in window.JSEncrypt.prototype)
    ) {
      return { success: false, message: '加密失败' };
    }
    const crypt = new window.JSEncrypt();
    if (!this.publicKey) {
      this.publicKey = await this.getPublicKey();
    }
    crypt.setPublicKey(this.publicKey);
    return base64Encode(crypt.encryptLong(encodeURIComponent(input)));
  }

  decrypt(input) {
    if (!this.privateKey) {
      console.error('缺少密钥');
      return false;
    }
  }

  async init() {
    await this.loadDependences();
    window.JSEncrypt.prototype.encryptLong = function(string) {
      const k = this.getKey();

      try {
        const lt = '';
        let ct = '';
        // RSA每次加密117bytes，需要辅助方法判断字符串截取位置
        // 1.获取字符串截取点
        const bytes = [];
        bytes.push(0);
        let byteNo = 0;
        let len;
        let c;
        len = string.length;
        let temp = 0;

        for (var i = 0; i < len; i++) {
          c = string.charCodeAt(i);

          if (c >= 0x010000 && c <= 0x10ffff) {
            byteNo += 4;
          } else if (c >= 0x000800 && c <= 0x00ffff) {
            byteNo += 3;
          } else if (c >= 0x000080 && c <= 0x0007ff) {
            byteNo += 2;
          } else {
            byteNo += 1;
          }

          if (byteNo % 117 >= 114 || byteNo % 117 == 0) {
            if (byteNo - temp >= 114) {
              bytes.push(i);
              temp = byteNo;
            }
          }
        } // 2.截取字符串并分段加密

        if (bytes.length > 1) {
          for (var i = 0; i < bytes.length - 1; i++) {
            var str;

            if (i == 0) {
              str = string.substring(0, bytes[i + 1] + 1);
            } else {
              str = string.substring(bytes[i] + 1, bytes[i + 1] + 1);
            }
            const t1 = k.encrypt(str);
            ct += t1;
          }

          if (bytes[bytes.length - 1] != string.length - 1) {
            const lastStr = string.substring(bytes[bytes.length - 1] + 1);
            ct += k.encrypt(lastStr);
          }

          return hexToBytes(ct);
        }

        const t = k.encrypt(string);
        const y = hexToBytes(t);
        return y;
      } catch (ex) {
        return false;
      }
    };
  }

  loadDependences() {
    const dependences = [
      'https://freight.sf-express.com/assets/feedback/jsencrypt.min.js',
    ];
    return loadScript(dependences[0]);
  }
}

export default LargeEncrypt;

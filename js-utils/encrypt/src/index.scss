.ky-feedback--icon {
  position: fixed;
  z-index: 9998;
  bottom: 193px;
  right: 15px;
  background: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 50%;
  width: 45px;
  height: 45px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 7px 2px rgba(79, 79, 79, 0.06);

  img {
    width: 14px;
  }
  p {
    margin: 3px 0 0;
    padding: 0;
    font-family: PingFangSC, sans-serif;
    font-size: 10px;
    color: #666;
    text-align: center;
  }
}

.ky-feedback-pc {
  &-icon {
    // display: flex;
    // flex-direction: column;
    // align-items: center;
    cursor: pointer;
    user-select: none;
    position: fixed;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    width: 60px;
    height: 60px;
    right: 20px;
    text-align: center;
    z-index: 9998;
    bottom: 20px;
    background: rgba(0, 0, 0, 0.49);
    border-radius: 4px;
    &--logo {
      display: inline-block;
      background: url(https://freight.sf-express.com/assets/feedback/imgs/pc-icon.png)
        no-repeat;
      background-position: center center;
      background-size: contain;
      vertical-align: middle;
      width: 24px;
      height: 25px;
    }
    p {
      margin: 5px 0 0;
      padding: 0;
      font-family: PingFangSC-Regular, sans-serif;
      font-size: 12px;
      color: #ffffff;
    }
    &--tip {
      position: absolute;
      left: 0;
      bottom: 0;
      margin-left: -360px;
      width: 340px;
      height: 60px;
      padding: 12px 6px 12px 16px;
      box-sizing: border-box;
      text-align: center;
      font-family: PingFangSC-Regular, sans-serif;
      font-size: 14px;
      color: #666666;
      background: #ffffff;
      box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
      border-radius: 4px;
      line-height: 18px;
      z-index: 11;
    }
    &--arrow {
      content: '';
      display: block;
      position: absolute;
      top: 50%;
      right: 0;
      margin-right: 74px;
      margin-left: -360px;
      width: 12px;
      height: 12px;
      transform: translateY(-50%) rotate(-45deg);
      z-index: 10;
      background: #ffffff;
      box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
    }
  }
}

html,
body {
  padding: 0;
  margin: 0;
  height: 100%;
}
.ky-feedback-loading {
  position: fixed;
  z-index: 99991;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
}
.ky-feedback-mask {
  position: relative;
  z-index: 1;
  width: 100vw;
  height: 100vh;
  min-height: 658px;
  overflow: auto;
  background: rgba(0, 0, 0, 0.5);
}
.ky-feedback-loading svg {
  width: 32px;
  height: 32px;
  position: absolute;
  left: 50%;
  top: 50%;
  z-index: 2;
}

.ky-feedback-crop svg {
  width: 20px;
  height: 20px;
  //   position: absolute;
  //   left: 50%;
  //   top: 50%;
  z-index: 2;
  margin-right: 5px;
  //   transform: translate(-50%, -50%);
  //   margin-top: -16px;
  //   margin-left: -16px;
  path {
    color: #dc1e32;
  }
}
.ky-feedback-crop-loading {
  position: relative;
  z-index: 999;
  width: 100%;
  height: 100%;
  display: flex;
  color: #fff;
  align-items: center;
  justify-content: center;
}
.ky-feedback-loading svg path {
  fill: #dc1e32;
}
.ky-feedback-preview {
  position: fixed;
  top: 50%;
  left: 50%;
  max-height: 90%;
  max-width: 90%;
  transform: translate(-50%, -50%);
  z-index: 2001;
  background: #f2f2f2;
  display: flex;
  flex-direction: column;
}
.ky-feedback-preview-content {
  flex: 1;
  //   position: relative;
  overflow: scroll;
  display: flex;
  align-items: baseline;
  justify-content: center;
}

.ky-feedback-preview-close {
  //   display: flex;
  position: absolute;
  align-items: center;
  background: url(https://freight.sf-express.com/assets/feedback/imgs/big-close.png)
    no-repeat;
  background-size: contain;
  background-position: center center;
  height: 34.6px;
  width: 34.6px;
  right: 0;
  top: 0;
  z-index: 99;
  margin-top: -17.3px;
  margin-right: -17.3px;
}
.ky-feedback-preview-nav-item {
  width: 45px;
  height: 45px;
  border-radius: 4px;
  border: 1px solid #f2f2f2;
  background-size: 100%;
  margin-right: 8.5px;
}
.ky-feedback-preview-nav-item.active {
  border-color: red;
}
.ky-feedback-h5-title {
  position: relative;
  padding-left: 11px;
  font-family: PingFangSC-Medium, sans-serif;
  font-size: 20px;
  color: #333333;
  letter-spacing: 0;
  text-align: left;
  line-height: 0.9;
  &::before {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    width: 4px;
    height: 20px;
    // transform: scaleX(-1);
    background-image: linear-gradient(180deg, #ff5656 0%, #dc1e32 100%);
  }
}
.ky-feedback-header {
  position: relative;
  height: 44px;
  background: #ffffff;
  font-family: PingFangSC-Medium, sans-serif;
  font-size: 18px;
  color: #303233;
  text-align: center;
  line-height: 44px;
}
.ky-feedback-arrow {
  position: absolute;
  left: 16px;
  top: 50%;
  width: 10.5px;
  height: 19px;
  margin-top: -9.5px;
  background: url('data:image/png;base64,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')
    no-repeat;
  background-size: contain;
}
.ky-feedback-content {
  flex: 1;
  background: rgb(242, 242, 242);
  padding: 10px;
  overflow: auto;
}
.ky-feedback-main-wrap {
  background: #ffffff;
  border-radius: 4px;
  padding: 17px 15px;
}
.ky-feedack-title {
  margin: 0;
  font-family: PingFangSC-Medium, sans-serif;
  font-size: 17px;
  color: #333;
  letter-spacing: 0;
}
.ky-feedack-checkbox-group {
  margin: 16.5px 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ky-feedack-checkbox {
  width: 103px;
  height: 32px;
  position: relative;
  border-radius: 4px;
}
.ky-feedack-checkbox input[type='radio'] {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
  width: 100%;
  height: 100%;
  opacity: 0;
  margin: 0;
}
.ky-feedack-checkbox label {
  display: block;
  box-sizing: border-box;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  background: #ffffff;
  font-family: PingFangSC-Regular, sans-serif;
  font-size: 15px;
  line-height: 32px;
  color: #333333;
  letter-spacing: 0;
  text-align: center;
  border: 1px solid #cdcdcd;
  border-radius: 4px;
}
.ky-feedack-checkbox input[type='radio']:checked + label {
  background-image: linear-gradient(90deg, #dc1e32 3%, #ff2b52 100%);
  color: #fff;
  border-color: transparent;
}
.ky-feedback-type-tips {
  font-family: PingFangSC-Regular, sans-serif;
  font-size: 13px;
  line-height: 18px;
  color: #666666;
  letter-spacing: 0;
}
.ky-feedback-type-tips.border {
  border-bottom: 1px solid #c4c4c4;
  padding-bottom: 13px;
}
.ky-feedback-detail-wrap {
  padding-top: 16.5px;
}
.ky-feedback-text-wrap {
  margin-top: 15px;
  background: #f9f9f9;
  border-radius: 4px;
  // height: 170px;
  padding: 14px 15px 14px;
  box-sizing: border-box;
}
.ky-feedback-textarea-wrap {
  position: relative;
}
.ky-feedback-textarea-wrap textarea {
  border: 0;
  outline: none;
  background: transparent;
  width: 100%;
  font-family: PingFang-SC-Medium, sans-serif;
  font-size: 14px;
  color: #666666;
  padding: 0;
}
.ky-feedback-textarea-wrap--count {
  color: #dc1e32;
  background: #f9f9f9;
  position: absolute;
  font-size: 12px;
  bottom: 5px;
  right: 10px;
}

.pic-close {
  width: 15px;
  height: 15px;
  background: url('data:image/png;base64,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')
    no-repeat;
  background-size: contain;
}
.ky-feedback-checkbox-item {
  //   padding-left:20px;
  display: flex;
  align-items: center;
  //   justify-content: flex-end;
  margin: 20px 0 12px;
}
.ky-feedback--checkbox {
  position: relative;
  width: 15px;
  height: 15px;
  margin-right: 6px;
}
.ky-feedback--checkbox input[type='checkbox'] {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 3;
  opacity: 0;
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
}

.ky-feedback-success {
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 9999;
  height: 358px;
  width: 640px;
  transform: translate(-50%, -50%);
  background: #ffffff;
  box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  text-align: center;
}
.ky-feedback-h5-success-header {
  height: 46px;
  position: relative;
  > div.ky-feedback-h5-close {
    background-image: url(https://freight.sf-express.com/assets/feedback/imgs/remove.png);
    width: 21px;
    height: 21px;
    margin: 0;
    padding: 0;
    right: 16px;
    top: 10px;
    cursor: pointer;
  }
}
.ky-feedback-success .ky-feedback-h5-content img {
  width: 72px;
  height: 72px;
  margin-top: 28px;
  margin-bottom: 36px;
}
.ky-feedback-success h1,
.ky-feedback-success p {
  margin: 0;
  padding: 0;
}
.ky-feedback-success h1 {
  font-family: PingFangSC-Semibold, sans-serif;
  font-size: 24px;
  color: #333333;
  text-align: center;
  margin-bottom: 29px;
}
.ky-feedback-success p {
  font-family: PingFangSC-Regular, sans-serif;
  font-size: 16px;
  color: #666666;
  text-align: center;
}

.contact-way {
  margin-top: 30px;
}
.contact-way input {
  background: #f9f9f9;
  border-radius: 4px;
  border: 0;
  outline: none;
  font-family: PingFang-SC-Medium, sans-serif;
  font-size: 14px;
  color: #999999;
  padding: 0 15px;
  height: 40px;
  width: 100%;
  box-sizing: border-box;
  margin: 16px 0 28px;
}

.ky-feedback-toast {
  border-radius: 4px;
  padding: 12px 16px;
  color: #fff;
  font-size: 14px;
  z-index: 99999;
  text-align: center;
  font-family: PingFang-SC-Medium, sans-serif;
  background: rgba(0, 0, 0, 0.75);
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.ky-feedback-h5 {
  // opacity: 0;
  padding: 20px 30px 26px;
  position: fixed;
  // top: 50%;
  // left: 50%;
  top: 0;
  bottom: 0;
  overflow-y: scroll;
  overflow-x: hidden;
  height: auto;
  margin-left: calc((100% - 800px) / 2);
  margin-top: 30px;
  margin-bottom: 30px;
  // transform: translate(-50%, -50%);
  z-index: 99999;
  width: 800px;
  // height: 660px;
  box-sizing: border-box;
  background: #ffffff;
  box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}
.ky-feedback-h5-close {
  width: 22px;
  height: 22px;
  margin-top: -10px;
  margin-right: -15px;
  position: absolute;
  right: 0;
  top: 0;
  background: url(https://freight.sf-express.com/assets/feedback/imgs/m_close.png)
    no-repeat;
  background-size: contain;
  background-position: center center;
  cursor: pointer;
}
.ky-feedback-h5-content {
  padding: 20px 30px 0 24px;
}
.ky-feedback-h5-sub-title {
  padding-left: 10px;
  font-family: PingFangSC-Medium, sans-serif;
  font-size: 16px;
  color: #333333;
  letter-spacing: 0;
}
.ky-feedback-h5-sub-title::before {
  content: '*';
  display: inline-block;
  font-family: PingFangSC-Regular, sans-serif;
  font-size: 16px;
  color: #dc1e32;
  letter-spacing: 0;
  margin-left: -12px;
  vertical-align: middle;
  padding-right: 4px;
}
.ky-feedback-checkbox-group {
  /* margin: 16.5px 0 20px; */
  display: flex;
  height: 16px;
  justify-content: space-between;
  align-items: center;
}

.ky-feedback-checkbox {
  width: 77px;
  height: 16px;
  display: inline-block;
  margin-right: 38px;
  position: relative;
  border-radius: 4px;
}
.ky-feedback-checkbox input[type='radio'] {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
  width: 100%;
  height: 100%;
  opacity: 0;
  margin: 0;
}

.ky-feedback--checkbox
  input[type='checkbox']:checked
  + .ky-feedback--checkbox__bg {
  background-image: url(https://freight.sf-express.com/assets/feedback/imgs/pc-ckb-1.png);
}

.ky-feedback-checkbox-item label {
  font-family: PingFangSC;
  font-size: 14px;
  color: #666666;
  letter-spacing: 0;
}

.ky-feedback-checkbox label {
  display: block;
  box-sizing: border-box;
  position: absolute;
  left: 21px;
  top: 0;
  z-index: 1;
  width: 100%;
  /* height: 100%; */
  background: #ffffff;
  font-family: PingFangSC-Regular, sans-serif;
  font-size: 14px;
  line-height: 1.1;
  color: #333333;
  letter-spacing: 0;
}
.ky-feedback-checkbox input[type='radio']:checked + label {
  font-size: 14px;
  color: #dc1e32;
}
.ky-feedback-checkbox input[type='radio'] + label + .bg {
  position: absolute;
  pointer-events: none;
  left: 0;
  top: 0;
  z-index: 3;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAHqADAAQAAAABAAAAHgAAAADKQTcFAAACuklEQVRIDcWXO4gaURSGz4xEIvGBTYSBrcRCiYVsZUKwETtlIUVIaZkigWVBIaRMAgrLwia9ZUgR2FiqBCxMla0MWog2C74a8YEEwZmcf5hZxkTNJszMHpjnvff/7mvu/UegG4SiKGKhUHgoCMIR3x9yEUk7ULqPg9MuOe0in89/43sZCftC2JdYKpXujkajlyx0wqL3kdfv95PH4yG3260WXSwWNJ/PaTKZqM+cd8x5TwOBwHk2m/2pvtxy2gkuFotPWOCMjwMWoWg0SqFQiLxe7xYZotlsRp1Oh5rNJnFliStwxcdxLpf7vK3AH2AGCdytbzjzK5/PR4lEgsLh8LayO9+1222q1+s0nU6R5x13/2uuhGIssAEGlFv6ka9Pg8EgZTIZcjqdxvw3vl+tVlQul6nb7aL1n7jlz4xwh1HJ5XK95efnsViM0uk0ORwbycasf71H2UgkQsvlkgaDwYNGo3GnVqt91QteK2tj+h4tBdSsgN5wOMTke5xKpX5Uq9U2tEWcMHu5e88wpuheswOa0AYDLOirYHwy/PIAE+l/x3RfZaEJbTDAUsH8IPKgn+CT+dfZuw/2exq0wQALTAdPqEec6UU8HidJwoJkXazXa+r1evd4otXQ2iOgsDhYHToDTJGbfYhlcNeKZGZlwAALTEwuCWuvXaGxJBWsL/h2wDWWCraDt8HgMVbQ4j62NrsCLB7jgQrGfmpXaKw+PqdLbOLYT60OMMASRfE7PqcLALGJWx06Q5blL6LmkcZwDlYHGNzDYzDR1TK3+hR2Bc7BqoA2GGCBicmFxfucH65gV+AczA5oQhsMsKCvguEG+eUxPBLsitkBTWiDoTvPawcCZ5BMJp1wCrArcA5mRKVSoVarBSmYvg+65q2ZvQ0wasODb7+91bsBV9sNvRF+K78wxgpw95v+0/YLHjGk76VCaeEAAAAASUVORK5CYII=')
    no-repeat;
  background-position: center;
  background-size: contain;
  width: 16px;
  height: 16px;
}
.ky-feedback-checkbox input[type='radio']:checked + label + .bg {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAHqADAAQAAAABAAAAHgAAAADKQTcFAAAC2klEQVRIDcWXz2sTQRTH35uEaqhVEGswENNfSg/eNh4URdGToiBUKF78D1REMaIeIrTFSkVUUPDowR9gQVC8CYLgqf0HtLHWQkvssQpWkh3fm80k2d3ZdLNJ6hyS3Tdvvp/5+eYtQogi83nx7dnbg7IMZ8jdQoCUlDLFTRFxSQIs0eMsxuDNwPnTnzGft7muUSGN4DLfd3Rz2f51EdG+IiXsDPas1SDCTynFvZjY8rD/+8c/tRr3UyC4kMmO0KjuA8i0u0nYN1yk2bg8uDAzbWohvEaC4VwmOy6l/To6lFVlmjUcLekboMvA0ELGekGtRr0davH91eDC7DmaAdoOTnGNuNC3f4zM7YYyabSi7VDptzpiZ015ejtXEMVZveYK7Oze1S+trWmYDuNiTPTs5d2uppqPTOeh3DGZdlgAgoMDn9Mw/Q3rI7ZthcSRA0Z3FROYWRiwDsmS/GT0imBkaOr5E+gaHoLihRvw+/0HnwrG8bCohEFfZRSDhm7aNwwYj0PywTjEdiV9UszkNbZ8NREM9VBuLm0bVq6PQXm5aFKzBG1rFexNtWFtRujV27A6/c4owUyhbxmjBxm7TxyD2I7tQdXQLJSFmOmKXF71npFTkHw8CamXT43wKFDFoNBJRwn5LvUV3hS9d24BCgFde/p98MhQItFUL4vKJe4D86YoXroJslRSdfXwVqAsxky6Aq0pmvTAANJ98jgkH02o48GN/n6dB7m2BnxkuKjd22AjKSfvD+JUqADihWudSFBqrAKIypEoXdFipn+OPhyF9LSzT2QosRSTReZ2Z68B2JP83KjokQNtuJVmp7cqLHJDP2buNn0tMlwkEoHBoapvfKhdiwrMPhudCFQDiMoMUEwYO9oOI2nr7IPlqiPml/+W7HEWyNkghav2jVyN1J1h+kbMBl02PKHXYF4PTswoO8rxZ4m2r/fv+Ioct61fU2871xp7K/V7Jz7a/gFtt2bAw/iGQgAAAABJRU5ErkJggg==');
}
.ky-feedback-h5-suggest-wrap {
  display: flex;
  align-items: center;
  margin: 16px 0 23px;
}
.ky-feedback-h5-suggest-tips {
  font-family: PingFangSC-Regular, sans-serif;
  display: flex;

  align-items: center;
  font-size: 14px;
  color: #333333;
  letter-spacing: 0.3px;
}
.hidden {
  display: none !important;
}
.icon-suggest {
  width: 24px;
  height: 21px;
  margin-right: 13px;
  display: inline-block;
  background: url(https://freight.sf-express.com/assets/feedback/imgs/sug1.png)
    no-repeat;
  background-position: center;
  background-size: contain;
}
.icon-pop {
  width: 23px;
  height: 21px;
  margin-right: 13px;
  display: inline-block;
  background: url(https://freight.sf-express.com/assets/feedback/imgs/sug2.png)
    no-repeat;
  background-position: center;
  background-size: contain;
}
.icon-jubao {
  width: 40px;
  height: 14px;
  margin-right: 13px;
  display: inline-block;
  background: url(https://freight.sf-express.com/assets/feedback/imgs/sug3.png)
    no-repeat;
  background-position: center;
  background-size: contain;
}

.ky-feedback-pic-uploader-wrap {
  display: flex;
  flex-wrap: wrap;
}
.ky-feedback-pic-uploader--button {
  position: relative;
  width: 72px;
  height: 72px;
  margin-right: 19px;
  background: url('https://freight.sf-express.com/publicResource/feedback/imgs/upload-pic.png')
    no-repeat;
  background-size: contain;
}
.ky-feedback-pic-uploader--button input[type='file'] {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  z-index: 2;
}
.ky-feedback-pic-uploader--preview {
  display: flex;
}

.ky-feedback-pic-uploader--preview-item {
  position: relative;
  width: 72px;
  height: 72px;
  margin-right: 19px;
  border: 1px solid #c0c0c0;
}
.ky-feedback-pic-uploader--preview-item div.img {
  width: 72px;
  height: 72px;
  background-size: cover;
  background-repeat: no-repeat;
}
.pic-close-wrap {
  position: absolute;
  right: 0;
  top: 0;
  margin-top: -12.5px;
  margin-right: -16.5px;
  width: 25px;
  height: 25px;
  padding: 5px 0;
}

.ky-feedback--checkbox__bg {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 2;
  width: 100%;
  background: url(https://freight.sf-express.com/assets/feedback/imgs/pc-ckb-0.png)
    no-repeat;
  background-size: contain;
  height: 100%;
}

.ky-feedback-crop {
  cursor: pointer;
  /* opacity: 0.68;
    background: #000000; */
  display: flex;
  background-repeat: no-repeat;
  background-position: 0 0;
  background-size: cover;
  align-items: center;
  justify-content: center;
  position: relative;

  height: 92px;
}
.ky-feedback-crop::after {
  content: '';
  display: block;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.58);
  position: absolute;
  border-radius: 4px;
  left: 0;
  top: 0;
}
.ky-feedback-crop-tips {
  display: flex;
  font-family: PingFangSC-Regular, sans-serif;
  font-size: 18px;
  color: #ffffff;
  letter-spacing: 0;
  text-align: right;
  z-index: 999;
}
.icon-crop {
  margin-right: 14px;
  width: 28px;
  height: 28px;
  background: url(https://freight.sf-express.com/assets/feedback/imgs/crop.png)
    no-repeat;
  background-position: center;
  background-size: contain;
}
.ky-feedback-button {
  display: block;
  margin: 0 auto;
  padding: 0;
  border: 0;
  outline: none;
  background-color: #dc1e32;
  border-radius: 4px;
  width: 100px;
  height: 36px;
  margin-top: 40px;
  font-family: PingFangSC-Medium, sans-serif;
  font-size: 14px;
  color: #ffffff;
  text-align: center;
  cursor: pointer;
}
.ky-feedback-h5-mask {
  position: fixed;
  background: rgba(0, 0, 0, 0.68);
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
}
.ky-feedback-h5-main-wrap {
  position: relative;
  z-index: 2;
}
.ky-feedback-pic-operator {
  // background: #fff;
  // background: rgba(0, 0, 0, 0.68);
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: scroll;
  // overflow-x: hidden;
  height: 90%;
  // left: 50%;
  // margin-top: 30px;
  // margin-bottom: 30px;
  // margin-left: calc((100% - 1024px) / 2);
  // top: 50%;
  width: 90%;
  margin: auto;
  // height: 600px;
  // margin-top: -300px;
  // margin-left: -512px;
  transform: scale(0);
  // overflow: visible;
  z-index: 2001;
}

.canvas-wrap {
  width: 100%;
  max-height: 90%;
  margin: auto;
  // background: #fff;
  // width: 1024px;
  // max-height: 600px;
  overflow: auto;
  canvas {
    width: 1024px;
    height: 600px;
  }
  z-index: 2000;
}
.ky-feedback-pic-operator--operation {
  display: flex;
  align-items: center;
  justify-content: space-around;
  position: absolute;
  // bottom: 0;
  right: 0;
  margin-top: 18px;
  // margin-bottom: -58px;
  width: 200px;
  height: 40px;
  background: #ffffff;
  border-radius: 4px;
  z-index: 2001;
}
.ky-feedback-pic-operator--operation > div.active::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  transform: translate(-5%, -5%) scale(1.2);
  border: 1px dotted #e10;
}
.icon-rect {
  position: relative;
  width: 20px;
  height: 20px;
  background: url(https://freight.sf-express.com/assets/feedback/imgs/rect.png)
    no-repeat;
  background-position: center center;
  background-size: contain;
  cursor: pointer;
}
.ky-feedback-pic-operator--operation > div:hover {
  transform: scale(1.1);
}

.icon-rect:hover::before {
  content: '框选区域';
  display: block;
  text-align: center;
  padding: 5px 5px;
  width: 80px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.68);
  position: absolute;
  top: -45px;
  left: 50%;
  transform: translate(-50%, 0);
  font-size: 14px;
  color: #fff;
}

.icon-text {
  position: relative;
  width: 15px;
  height: 18px;
  background: url(https://freight.sf-express.com/assets/feedback/imgs/text.png)
    no-repeat;
  background-position: center center;
  background-size: contain;
  cursor: pointer;
}

.icon-text:hover::before {
  content: '标记文本';
  display: block;
  text-align: center;
  padding: 5px 5px;
  width: 80px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.68);
  position: absolute;
  top: -45px;
  left: 50%;
  transform: translate(-50%, 0);
  font-size: 14px;
  color: #fff;
}

.icon-rotate {
  position: relative;
  width: 21px;
  height: 19px;
  background: url(https://freight.sf-express.com/assets/feedback/imgs/rotate.png)
    no-repeat;
  background-position: center center;
  background-size: contain;
  cursor: pointer;
}
.icon-rotate:hover::before {
  content: '旋转';
  display: block;
  text-align: center;
  padding: 5px 5px;
  width: 40px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.68);
  position: absolute;
  top: -45px;
  left: 50%;
  transform: translate(-50%, 0);
  font-size: 14px;
  color: #fff;
}

.icon-undo {
  position: relative;
  width: 20px;
  height: 17px;
  background: url(https://freight.sf-express.com/assets/feedback/imgs/undo.png)
    no-repeat;
  background-position: center center;
  background-size: contain;
  cursor: pointer;
}

.icon-undo:hover::before {
  content: '撤销';
  display: block;
  text-align: center;
  padding: 5px 5px;
  width: 80px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.68);
  position: absolute;
  top: -45px;
  left: 50%;
  transform: translate(-50%, 0);
  font-size: 14px;
  color: #fff;
}

.icon-redo {
  position: relative;
  width: 20px;
  height: 17px;
  background: url(https://freight.sf-express.com/assets/feedback/imgs/redo.png)
    no-repeat;
  background-position: center center;
  background-size: contain;
  cursor: pointer;
}

.icon-redo:hover::before {
  content: '取消撤销';
  display: block;
  text-align: center;
  padding: 5px 5px;
  width: 80px;
  border-radius: 4px;
  background: rgba(70, 49, 49, 0.68);
  position: absolute;
  top: -45px;
  left: 50%;
  transform: translate(-50%, 0);
  font-size: 14px;
  color: #fff;
}

.icon-remove {
  position: relative;
  width: 22px;
  height: 22px;
  background: url(https://freight.sf-express.com/assets/feedback/imgs/remove.png)
    no-repeat;
  background-position: center center;
  background-size: contain;
  cursor: pointer;
}
.icon-remove:hover::before {
  content: '取消';
  display: block;
  text-align: center;
  padding: 5px 5px;
  width: 40px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.68);
  position: absolute;
  top: -45px;
  left: 50%;
  transform: translate(-50%, 0);
  font-size: 14px;
  color: #fff;
}

.icon-confirm {
  position: relative;
  width: 24px;
  height: 18px;
  background: url(https://freight.sf-express.com/assets/feedback/imgs/confirm.png)
    no-repeat;
  background-position: center center;
  background-size: contain;
  cursor: pointer;
}
.icon-confirm:hover::before {
  content: '确定';
  display: block;
  text-align: center;
  padding: 5px 5px;
  width: 40px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.68);
  position: absolute;
  top: -45px;
  left: 50%;
  transform: translate(-50%, 0);
  font-size: 14px;
  color: #fff;
}

@keyframes scale-enter {
  from {
    transform: scale3d(0, 0, 0);
  }

  to {
    transform: scale3d(1, 1, 1);
  }
}

.scale-enter {
  animation: scale-enter both cubic-bezier(0.4, 0, 0, 1.5);
}

@keyframes scale-leave {
  from {
    transform: scale3d(1, 1, 1);
  }

  to {
    transform: scale3d(0, 0, 0);
  }
}

.scale-leave {
  animation: scale-leave both;
}

.ky-feedback-input-field {
  display: flex;
  align-items: center;
  margin-top: 25px;
  label {
    font-family: PingFangSC-Regular, sans-serif;
    font-size: 16px;
    color: #333333;
    letter-spacing: 0;
    text-align: right;
    margin-right: 18px;
  }
  input {
    outline: none;
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    width: 300px;
    height: 34px;
    font-family: PingFangSC-Regular, sans-serif;
    font-size: 14px;
    color: #666666;
    letter-spacing: 0.3px;
    padding: 0 16px;
    &::placeholder {
      color: #999999;
    }
  }
}

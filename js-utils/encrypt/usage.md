# 快运 PC 反馈插件

## UMD 用法

```html
<script src="https://freight.sf-express.com/cdn/kyutils/feedback-pc/0.1.0/LargeEntry.min.js"></script>
```

## npm 安装

```javascript
import LargeEncrypt from '@ky/large-encrypt';
cont data = {
  userCode: '',
  userName: '',
}
const ciphertext = await new LargeEncrypt().encrypt(JSON.stringify(data));
```

## 配置项 Options

| 属性      | 类型     | 默认值 | 必要 | 说明                         |
| --------- | -------- | ------ | ---- | ---------------------------- |
| publichKey| String   | ‘’     | 否   | 公钥 |
| privateKey| String   | ‘’     | 否   | 私钥 |


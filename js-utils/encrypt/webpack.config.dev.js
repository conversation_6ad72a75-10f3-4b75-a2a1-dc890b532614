const path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const ParallelUglifyPlugin = require('webpack-parallel-uglify-plugin');

module.exports = {
  mode: 'development',
  entry: './src/dev.js',
  output: {
    filename: 'large-encrypt.js',
    path: path.resolve(__dirname, 'build'),
    libraryExport: 'default',
    library: 'LargeEncrypt',
    libraryTarget: 'umd',
  },
  devServer: {
    contentBase: path.join(__dirname, 'build'),
    port: 9000,
    hot: true,
    proxy: {
      '/': {
        target: 'https://freight.sf-express.com',
        changeOrigin: true,
        secure: false,
      },
    },
  },
  module: {
    rules: [
      {
        test: /\.m?js$/,
        exclude: /(node_modules|bower_components)/,
        use: {
          loader: 'babel-loader',
        },
      },
    ],
  },
  plugins: [
    new webpack.DefinePlugin({
      'process.env.DEPLOY_ENV': JSON.stringify(process.env.DEPLOY_ENV),
    }),
    new ParallelUglifyPlugin({
      cacheDir: '.cache/',
      uglifyJS: {
        output: {
          comments: false,
        },
        compress: {},
        warnings: false,
      },
    }),
    new HtmlWebpackPlugin({
      template: './src/index.html',
      filename: 'index.html',
    }),
  ],
};

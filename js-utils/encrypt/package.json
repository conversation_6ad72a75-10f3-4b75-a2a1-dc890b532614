{"name": "@ky/large-encrypt", "version": "0.1.0", "description": "大件加解密组件", "scripts": {"build": "webpack", "dev": "webpack-dev-server --config webpack.config.dev.js", "test": "jest", "trypublish": "npm publish || true"}, "publishConfig": {"registry": "https://artifactory.sf-express.com/artifactory/api/npm/npm-sf-local/"}, "main": "build/LargeEncrypt.js", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "keywords": [], "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.5", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/polyfill": "^7.4.4", "@babel/preset-env": "^7.4.5", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-preset-minify": "^0.5.0", "html-webpack-plugin": "^4.3.0", "jest": "^24.8.0", "prettier": "^1.18.2", "prettier-webpack-plugin": "^1.2.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.4", "webpack-parallel-uglify-plugin": "^1.1.2"}, "dependencies": {"cross-env": "^6.0.3", "webpack-dev-server": "^3.11.0"}, "directories": {"test": "tests"}, "gitHead": "78366e16f33b9b71e27a360a7f909ca82426e254"}
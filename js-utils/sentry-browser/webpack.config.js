const path = require('path');
const webpack = require('webpack');
const PrettierPlugin = require('prettier-webpack-plugin');
const ParallelUglifyPlugin = require('webpack-parallel-uglify-plugin');

// const {
//   version = '1.0.1',
//   name = 'casLogin',
//   license = 'MIT',
//   repository = '',
//   author = 'Focus',
// } = {};

// const banner = `
//   ${name} v${version}
//   ${repository.url}
//   Copyright (c) ${author.replace(/ *\<[^)]*\> */g, ' ')}
//   This source code is licensed under the ${license} license found in the
//   LICENSE file in the root directory of this source tree.
// `;

module.exports = {
  mode: 'production',
  entry: './src/index.js',
  output: {
    filename: 'index.js',
    path: path.resolve(__dirname, 'build'),
    library: 'CasLogin',
    libraryTarget: 'umd',
    libraryExport: 'default',
  },
  module: {
    rules: [
      {
        test: /\.m?js$/,
        exclude: /(node_modules|bower_components)/,
        use: ['babel-loader'],
      },
      {
        test: /\.css$/i,
        use: ['style-loader', 'css-loader'],
      },
      {
        test: /\.(png|jpe?g|gif|svg|eot|ttf|woff|woff2)$/,
        use: ['url-loader'],
      },
    ],
  },
  plugins: [
    // new PrettierPlugin(),
    new webpack.DefinePlugin({
      'process.env.DEPLOY_ENV': JSON.stringify(process.env.DEPLOY_ENV),
    }),
    // new webpack.BannerPlugin(banner),
    new ParallelUglifyPlugin({
      cacheDir: '.cache/',
      uglifyJS: {
        output: {
          comments: false,
        },
        compress: {},
        warnings: false,
      },
    }),
  ],
};

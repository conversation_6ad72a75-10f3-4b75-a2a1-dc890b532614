import * as Sentry from '@sentry/browser';
const excludeError = [
  {
    value: 'promise rejection captured with keys: errorFields', // 表单校验错误，50k
  },
  {
    value: 'ResizeObserver loop limit exceeded', // 浏览器提示，不影响使用 1000k
  },
  // {
  //   value: 'ResizeObserver loop completed with undelivered notifications', //
  // },
  {
    value: 'promise rejection captured with value: cancel', // 请求被取消 100k
  },
  {
    value: 'timeout of ', // 请求超时 100k
  },
];
const errorList = {};
const throttleUpload = error => {
  const now = new Date().getTime();
  try {
    const last = errorList[error.exception.values[0].value];
    // console.log(now - last, 'now - last');
    if (last) {
      if (now - last < 3000) {
        return null;
      }
    }
    errorList[error.exception.values[0].value] = now;
    return error;
  } catch (err) {
    errorList[error.exception.values[0].value] = now;
    return error;
  }
};

// const _init = sentry.init;
const _init = function(options) {
  const newOptions = Object.assign(
    {
      maxBreadcrumbs: 50,
      beforeSend: error => {
        if (
          excludeError.find(
            item => error.exception.values[0].value.indexOf(item.value) !== -1,
          )
        ) {
          return null;
        }
        return throttleUpload(error);
      },
    },
    options,
  );
  // console.log(newOptions, 'newOptions');
  this.init(newOptions);
};

export default {
  ...Sentry,
  _init,
};

// export default function SentryInit(options) {
//   const newOptions = Object.assign(
//     {
//       maxBreadcrumbs: 50,
//       beforeSend: error => {
//         if (
//           excludeError.find(
//             item => error.exception.values[0].value.indexOf(item.value) !== -1,
//           )
//         ) {
//           // console.log(error.exception.values[0].value, 'preventError');
//           return null;
//         }

//         return throttleUpload(error);
//       },
//     },
//     options,
//   );
//   // console.log(newOptions, 'newOptions');
//   Sentry.init(newOptions);
// }

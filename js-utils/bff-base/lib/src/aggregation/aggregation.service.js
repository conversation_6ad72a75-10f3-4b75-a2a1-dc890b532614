"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AggregationService = void 0;
const common_1 = require("@nestjs/common");
const rxjs_1 = require("rxjs");
const httpClient_service_1 = require("../bff-http/httpClient.service");
const base_service_1 = require("../base/base.service");
var METHOD_ENUM;
(function (METHOD_ENUM) {
    METHOD_ENUM["POST"] = "post";
    METHOD_ENUM["GET"] = "get";
})(METHOD_ENUM || (METHOD_ENUM = {}));
let AggregationService = class AggregationService {
    constructor(httpClientService, baseService) {
        this.httpClientService = httpClientService;
        this.baseService = baseService;
        this.config = {};
        this.config = this.baseService.getConfig();
    }
    handle(request) {
        const clients = [];
        const params = request.body;
        let clientItem;
        params.map((item) => {
            const url = this.config.route.transmit_dest_host + item.url;
            const config = { headers: Object.assign({}, request.headers) };
            if (item.method === METHOD_ENUM.POST) {
                clientItem = this.httpClientService.postClient(url, item.param, config);
            }
            else if (item.method === METHOD_ENUM.GET) {
                clientItem = this.httpClientService.getClient(url, config);
            }
            clients.push(clientItem);
        });
        return (0, rxjs_1.forkJoin)(clients);
    }
};
AggregationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [httpClient_service_1.HttpClientService,
        base_service_1.BaseService])
], AggregationService);
exports.AggregationService = AggregationService;

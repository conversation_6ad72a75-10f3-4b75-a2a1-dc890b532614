import { Observable } from 'rxjs';
import { Request } from 'express';
import { HttpClientService } from '../bff-http/httpClient.service';
import { BaseService } from '../base/base.service';
export declare class AggregationService {
    private readonly httpClientService;
    private readonly baseService;
    private config;
    constructor(httpClientService: HttpClientService, baseService: BaseService);
    handle(request: Request): Observable<any>;
}
//# sourceMappingURL=aggregation.service.d.ts.map
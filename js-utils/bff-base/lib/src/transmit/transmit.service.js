"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransmitService = void 0;
const common_1 = require("@nestjs/common");
const operators_1 = require("rxjs/operators");
const bff_http_1 = require("../bff-http");
const base_1 = require("../base");
let TransmitService = class TransmitService {
    constructor(httpClientService, baseService) {
        this.httpClientService = httpClientService;
        this.baseService = baseService;
        this.config = {};
        this.config = this.baseService.getConfig();
    }
    transmitData(request) {
        switch (request.method) {
            case 'POST':
                return this.postHandler(request);
            case 'GET':
                return this.getHandler(request);
        }
        return this.getHandler(request);
    }
    postHandler(request) {
        const data = request.body;
        const headers = request.headers;
        const config = {
            headers
        };
        const url = this.config.route.transmit_dest_host +
            request.originalUrl.replace(`/${this.config.route.global_prefix}/transmit`, '');
        return this.httpClientService.postClient(url, data, config).pipe((0, operators_1.map)(res => {
            if (res.status && res.status !== 200) {
                throw new common_1.HttpException(res.data, res.status);
            }
            else {
                return res;
            }
        }));
    }
    getHandler(request) {
        const headers = request.headers;
        const config = {
            headers,
        };
        const url = this.config.route.transmit_dest_host +
            request.originalUrl.replace(`/${this.config.route.global_prefix}/transmit`, '');
        return this.httpClientService.getClient(url, config).pipe((0, operators_1.map)(res => {
            if (res.status && res.status !== 200) {
                throw new common_1.HttpException(res.data, res.status);
            }
            else {
                return res;
            }
        }));
    }
};
TransmitService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [bff_http_1.HttpClientService,
        base_1.BaseService])
], TransmitService);
exports.TransmitService = TransmitService;

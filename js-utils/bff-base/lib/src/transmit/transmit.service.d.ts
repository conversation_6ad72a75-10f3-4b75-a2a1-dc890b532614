import { Observable } from 'rxjs';
import { Request } from 'express';
import { AxiosResponse } from 'axios';
import { HttpClientService } from '../bff-http';
import { BaseService } from '../base';
export declare class TransmitService {
    private readonly httpClientService;
    private readonly baseService;
    private config;
    constructor(httpClientService: HttpClientService, baseService: BaseService);
    transmitData(request: Request): Observable<AxiosResponse<any>>;
    private postHandler;
    private getHandler;
}
//# sourceMappingURL=transmit.service.d.ts.map
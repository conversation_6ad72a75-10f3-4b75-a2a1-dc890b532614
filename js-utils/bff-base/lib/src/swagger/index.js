"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupSwagger = void 0;
const swagger_1 = require("@nestjs/swagger");
// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
const setupSwagger = (app, swaggerOptions) => {
    const options = new swagger_1.DocumentBuilder()
        .setTitle(swaggerOptions.title)
        .setDescription(swaggerOptions.description)
        .setVersion(swaggerOptions.version)
        .addBearerAuth()
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, options);
    swagger_1.SwaggerModule.setup(swaggerOptions.root, app, document);
};
exports.setupSwagger = setupSwagger;

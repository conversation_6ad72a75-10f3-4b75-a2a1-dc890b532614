import { ExecutionContext, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
import { LoggerSharedService } from './logger.shared.service';
export declare class LoggingInterceptor implements NestInterceptor {
    private readonly logger;
    constructor(logger: LoggerSharedService);
    intercept(context: ExecutionContext, call$: Observable<any>): Observable<any>;
}
//# sourceMappingURL=logging.interceptor.d.ts.map
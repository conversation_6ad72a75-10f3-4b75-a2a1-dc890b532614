"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggerConsoleSharedService = void 0;
const log_levels_const_1 = require("./log-levels.const");
class LoggerConsoleSharedService {
    constructor(configService) {
        this.configService = configService;
        this.startSeparator = '\n';
        this.endSeparator = '\n\n----------------------------';
        this.loggingLevel = this.configService.logging.console.levels;
        this.logError = this.loggingLevel.includes(log_levels_const_1.LogLevels.error);
        this.logWarn = this.loggingLevel.includes(log_levels_const_1.LogLevels.warning);
        this.logInfo = this.loggingLevel.includes(log_levels_const_1.LogLevels.info);
        this.logDebug = this.loggingLevel.includes(log_levels_const_1.LogLevels.debug);
        this.logTrace = this.loggingLevel.includes(log_levels_const_1.LogLevels.trace);
    }
    log(msg, ...logObjects) {
        return __awaiter(this, void 0, void 0, function* () {
            if (this.logInfo)
                console.log(this.startSeparator, msg, ...logObjects, this.endSeparator);
        });
    }
    info(msg, ...logObjects) {
        return __awaiter(this, void 0, void 0, function* () {
            if (this.logInfo) {
                console.info(this.startSeparator, msg, ...logObjects, this.endSeparator);
            }
        });
    }
    error(msg, ...logObjects) {
        return __awaiter(this, void 0, void 0, function* () {
            if (this.logError) {
                console.error(this.startSeparator, msg, ...logObjects, this.endSeparator);
                console.trace();
            }
        });
    }
    warn(msg, ...logObjects) {
        return __awaiter(this, void 0, void 0, function* () {
            if (this.logWarn)
                console.warn(this.startSeparator, msg, ...logObjects, this.endSeparator);
        });
    }
    debug(msg, ...logObjects) {
        return __awaiter(this, void 0, void 0, function* () {
            if (this.logDebug) {
                console.debug(this.startSeparator, msg, ...logObjects, this.endSeparator);
            }
        });
    }
    trace(msg, ...logObjects) {
        return __awaiter(this, void 0, void 0, function* () {
            if (this.logTrace)
                console.log(this.startSeparator, msg, ...logObjects, this.endSeparator);
        });
    }
}
exports.LoggerConsoleSharedService = LoggerConsoleSharedService;

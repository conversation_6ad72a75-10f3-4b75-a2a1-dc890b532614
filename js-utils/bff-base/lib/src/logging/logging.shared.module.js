"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggingSharedModule = exports.getLogger = void 0;
const common_1 = require("@nestjs/common");
const logger_console_shared_service_1 = require("./logger-console.shared.service");
const logger_winston_shared_service_1 = require("./logger-winston.shared.service");
const logger_shared_service_1 = require("./logger.shared.service");
const base_service_1 = require("../base/base.service");
const getLogger = (baseService) => {
    return baseService.getConfig().app.env === 'dev'
        ? new logger_console_shared_service_1.LoggerConsoleSharedService(baseService.getConfig())
        : new logger_winston_shared_service_1.LoggerWinstonSharedService(baseService.getConfig());
};
exports.getLogger = getLogger;
const LoggerSharedServiceProvider = {
    provide: logger_shared_service_1.LoggerSharedService,
    useFactory: exports.getLogger,
    inject: [base_service_1.BaseService],
};
let LoggingSharedModule = class LoggingSharedModule {
};
LoggingSharedModule = __decorate([
    (0, common_1.Module)({
        providers: [LoggerSharedServiceProvider],
        exports: [LoggerSharedServiceProvider],
    })
], LoggingSharedModule);
exports.LoggingSharedModule = LoggingSharedModule;

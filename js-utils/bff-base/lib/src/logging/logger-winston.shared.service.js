"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var LoggerWinstonSharedService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggerWinstonSharedService = void 0;
const common_1 = require("@nestjs/common");
const FileSystem = require("fs");
const winston = require("winston");
let LoggerWinstonSharedService = LoggerWinstonSharedService_1 = class LoggerWinstonSharedService {
    constructor(configService) {
        this.configService = configService;
        this.transports = [];
        this.appName = this.configService.app.name || 'BFF';
        const logLevel = this.configService.logging.winston.level || 'info';
        const logTransport = this.configService.logging.winston.transports;
        for (const key in logTransport) {
            if (logTransport.hasOwnProperty(key)) {
                if (key === LoggerWinstonSharedService_1.TRANSPORT_KEY_CONSOLE) {
                    this.configureConsoleTransport(logTransport[key], logLevel);
                }
                else if (key === LoggerWinstonSharedService_1.TRANSPORT_KEY_FILE) {
                    this.configureFileTransport(logTransport[key]);
                }
            }
        }
        this.logger = winston.createLogger({
            transports: this.transports,
        });
    }
    log(msg, ...logObjects) {
        this.info(msg, ...logObjects);
    }
    info(msg, ...logObjects) {
        this.logger.info(msg, ...logObjects);
    }
    error(msg, ...logObjects) {
        this.logger.error(msg, ...logObjects);
    }
    warn(msg, ...logObjects) {
        this.logger.warn(msg, ...logObjects);
    }
    debug(msg, ...logObjects) {
        this.logger.debug(msg, ...logObjects);
    }
    trace(msg, ...logObjects) {
        this.logger.verbose(msg, ...logObjects);
    }
    /**
     * Configuring console transport
     */
    configureConsoleTransport(transport, logLevel) {
        const options = Object.assign({
            level: logLevel,
        }, transport);
        this.transports.push(new winston.transports.Console(options));
    }
    /**
     * Configuring file transport
     */
    configureFileTransport(transport) {
        const targetFileList = this.configService.logging.winston.target;
        const targetDir = this.configService.logging.logDir || 'logs';
        // Creating log directory if it does not exist
        if (!FileSystem.existsSync(targetDir)) {
            FileSystem.mkdirSync(targetDir);
        }
        for (const key in targetFileList) {
            if (targetFileList.hasOwnProperty(key)) {
                const options = Object.assign({
                    name: key,
                    level: key,
                    filename: `${targetDir}/${this.appName + '-' + targetFileList[key]}`,
                }, transport);
                this.transports.push(new winston.transports.File(options));
            }
        }
    }
};
LoggerWinstonSharedService.TRANSPORT_KEY_CONSOLE = 'console';
LoggerWinstonSharedService.TRANSPORT_KEY_FILE = 'file';
LoggerWinstonSharedService = LoggerWinstonSharedService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [Object])
], LoggerWinstonSharedService);
exports.LoggerWinstonSharedService = LoggerWinstonSharedService;

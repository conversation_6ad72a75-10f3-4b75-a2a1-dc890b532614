export declare abstract class LoggerSharedService {
    abstract log(msg: string, ...logObjects: any[]): void;
    abstract info(msg: string, ...logObjects: any[]): void;
    abstract error(msg: string, ...logObjects: any[]): void;
    abstract warn(msg: string, ...logObjects: any[]): void;
    abstract debug(msg: string, ...logObjects: any[]): void;
    abstract trace(msg: string, ...logObjects: any[]): void;
}
//# sourceMappingURL=logger.shared.service.d.ts.map
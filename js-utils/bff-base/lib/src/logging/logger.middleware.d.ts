import { NestMiddleware } from '@nestjs/common';
import { LoggerSharedService } from './logger.shared.service';
import { NextFunction, Request, Response } from 'express';
export declare class LoggerMiddleware implements NestMiddleware {
    private readonly logger;
    constructor(logger: LoggerSharedService);
    use(req: Request, res: Response, next: NextFunction): void;
}
//# sourceMappingURL=logger.middleware.d.ts.map
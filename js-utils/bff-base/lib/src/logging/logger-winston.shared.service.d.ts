import { LoggerSharedService } from './logger.shared.service';
export declare class LoggerWinstonSharedService implements LoggerSharedService {
    private readonly configService;
    static loggerInstance: LoggerSharedService;
    private static readonly TRANSPORT_KEY_CONSOLE;
    private static readonly TRANSPORT_KEY_FILE;
    private readonly appName;
    private logger;
    private transports;
    constructor(configService: any);
    log(msg: string, ...logObjects: any[]): void;
    info(msg: string, ...logObjects: any[]): void;
    error(msg: string, ...logObjects: any[]): void;
    warn(msg: string, ...logObjects: any[]): void;
    debug(msg: string, ...logObjects: any[]): void;
    trace(msg: string, ...logObjects: any[]): void;
    /**
     * Configuring console transport
     */
    private configureConsoleTransport;
    /**
     * Configuring file transport
     */
    private configureFileTransport;
}
//# sourceMappingURL=logger-winston.shared.service.d.ts.map
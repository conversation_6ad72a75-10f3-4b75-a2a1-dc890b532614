import { LoggerSharedService } from './logger.shared.service';
export declare class LoggerConsoleSharedService implements LoggerSharedService {
    private readonly configService;
    constructor(configService: any);
    private startSeparator;
    private endSeparator;
    private loggingLevel;
    private logError;
    private logWarn;
    private logInfo;
    private logDebug;
    private logTrace;
    log(msg: string, ...logObjects: any[]): Promise<void>;
    info(msg: string, ...logObjects: any[]): Promise<void>;
    error(msg: string, ...logObjects: any[]): Promise<void>;
    warn(msg: string, ...logObjects: any[]): Promise<void>;
    debug(msg: string, ...logObjects: any[]): Promise<void>;
    trace(msg: string, ...logObjects: any[]): Promise<void>;
}
//# sourceMappingURL=logger-console.shared.service.d.ts.map
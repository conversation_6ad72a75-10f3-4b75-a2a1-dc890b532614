"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var BaseModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseModule = void 0;
const common_1 = require("@nestjs/common");
const base_controller_1 = require("./base.controller");
const base_service_1 = require("./base.service");
const transmit_1 = require("../transmit");
const aggregation_1 = require("../aggregation");
const bff_http_1 = require("../bff-http");
const logging_1 = require("../logging");
let BaseModule = BaseModule_1 = class BaseModule {
    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
    static forRoot(options) {
        return {
            module: BaseModule_1,
            imports: [
                bff_http_1.HttpClientModule,
                transmit_1.TransmitModule,
                aggregation_1.AggregationModule,
                logging_1.LoggingSharedModule,
            ],
            providers: [
                {
                    provide: 'CONFIG',
                    useValue: options.config
                },
                base_service_1.BaseService,
            ],
            controllers: [base_controller_1.BaseController],
            exports: [
                base_service_1.BaseService,
                bff_http_1.HttpClientModule,
                transmit_1.TransmitModule,
                aggregation_1.AggregationModule,
                logging_1.LoggingSharedModule,
            ],
        };
    }
    configure(consumer) {
        consumer.apply(logging_1.LoggerMiddleware).forRoutes('*');
    }
};
BaseModule = BaseModule_1 = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({})
], BaseModule);
exports.BaseModule = BaseModule;

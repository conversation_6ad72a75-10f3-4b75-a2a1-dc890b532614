import { HttpService } from '@nestjs/axios';
import { Observable } from 'rxjs';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
export declare class HttpClientService {
    private readonly httpService;
    constructor(httpService: HttpService);
    postClient(url: string, data: any, config: AxiosRequestConfig): Observable<AxiosResponse<any>>;
    getClient(url: string, config: AxiosRequestConfig): Observable<AxiosResponse<any>>;
}
//# sourceMappingURL=httpClient.service.d.ts.map
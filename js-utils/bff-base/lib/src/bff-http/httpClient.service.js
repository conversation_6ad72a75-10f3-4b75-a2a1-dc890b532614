"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpClientService = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("@nestjs/axios");
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
let HttpClientService = class HttpClientService {
    constructor(httpService) {
        this.httpService = httpService;
    }
    postClient(url, data, config) {
        var _a, _b;
        (_a = config.headers) === null || _a === void 0 ? true : delete _a['content-length'];
        (_b = config.headers) === null || _b === void 0 ? true : delete _b.host;
        return this.httpService.post(url, data, config).pipe((0, operators_1.map)((res) => res.data), (0, rxjs_1.catchError)((e) => {
            const errInfo = { status: e.response.status, data: e.response.data };
            return (0, rxjs_1.of)(errInfo);
        }));
    }
    getClient(url, config) {
        var _a, _b;
        (_a = config.headers) === null || _a === void 0 ? true : delete _a['content-length'];
        (_b = config.headers) === null || _b === void 0 ? true : delete _b.host;
        return this.httpService.get(url, config).pipe((0, operators_1.map)((res) => res.data), (0, rxjs_1.catchError)((e) => {
            const errInfo = { status: e.response.status, data: e.response.data };
            return (0, rxjs_1.of)(JSON.parse(errInfo));
        }));
    }
};
HttpClientService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [axios_1.HttpService])
], HttpClientService);
exports.HttpClientService = HttpClientService;

{"name": "@ky/bff-base", "version": "0.1.6", "description": "A base package module for ky-bff", "author": "ky", "license": "MIT", "main": "./lib/index.js", "directories": {"src": "src"}, "files": ["lib"], "types": "./lib", "publishConfig": {"access": "public"}, "dependencies": {"@nestjs/axios": "0.0.3", "@nestjs/common": "^8.0.6", "@nestjs/config": "^1.0.1", "@nestjs/core": "^8.0.6", "@nestjs/passport": "^8.0.1", "@nestjs/platform-express": "^8.0.6", "@nestjs/swagger": "^5.0.9", "class-transformer": "^0.4.0", "class-validator": "^0.13.1", "cookie-parser": "^1.4.6", "dotenv": "^10.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.3.0", "swagger-ui-express": "^4.1.6", "typescript": "^4.4.2", "winston": "^3.3.3"}}
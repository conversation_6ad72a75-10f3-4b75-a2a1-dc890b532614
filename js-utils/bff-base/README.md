<!--
 * @Descripttion:
 * @version:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-01 17:30:54
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-06-09 18:13:40
-->

# 快运神策 Web JS 工具类

基于神策 Web JS SDK 的二次封装

### 目录结构

```bash
├── /dist/             # 项目输出目录
├── /lib/              # 项目源码目录
│ ├── webSensors.js    # web端Sensors类
│ ├── rule.json        # 所有属性的定义文件
├── web-sensors.js     # web文件（未压缩）
├── web-sensors.min.js # web文件（压缩过）
├── wx-sensors.js      # 小程序文件（未压缩）
├── wx-sensors.min.js  # 小程序文件（压缩过）
├── package.json       # 项目依赖
├── webpack.config.js  # 配置文件
```

### 打包项目

1. 执行打包命令 `npm run build`，会在 dist 文件夹生产 Web 端`web-sensors.js` 与 `web-sensors.min.js`
2. 在入口文件 index.js 根据环境变量 `process.env.NODE_ENV` 导出不同文件产物
3. ky-lerna 使用：http://confluence.sf-express.com/pages/viewpage.action?pageId=122041525

## Web 端

### 用法

```javascript
// 项目常量配置
const SENSORS_CONFIG = {
  SERVER_URL: 'xxxxx', // 上报路径
  SYSTEM_CODE: 'xxxx',
  PLATFORM_TYPE: 'xx',
  PLATFORM_NAME: 'xx'
}

// 引用 @ky/sensors
import sensors from '@ky/sensors/web-sensors'

// 初始化 (不传的话，默认为上报灰度地址，且不打印日志，根据项目需求进行覆盖)
sensors.init({
  // 上报路径
  server_url: SENSORS_CONFIG.SERVER_URL,
  // 单页面配置，默认开启，若页面中有锚点设计，需要将该配置删除，否则触发锚点会多触发 **$pageview** 事件
  is_track_single_page: false,
  use_client_time: true,
  send_type: 'beacon',
  // 是否打印日志
  show_log: false, //process.env.NODE_ENV === 'development'
  heatmap: {
    // 是否开启点击图，default 表示开启，自动采集 **$WebClick** 事件，可以设置 'not_collect' 表示关闭。
    clickmap: 'not_collect',
    // 是否开启触达注意力图，not_collect 表示关闭，不会自动采集 **$WebStay** 事件，可以设置 'default' 表示开启。
    scroll_notice_map: 'not_collect'
  }
})

// 注册公共属性
sensors.registerPage({
  platform_type: SENSORS_CONFIG.PLATFORM_TYPE,
  platform_name: SENSORS_CONFIG.PLATFORM_NAME,
  system_code: SENSORS_CONFIG.SYSTEM_CODE,
})

// 登录
sensors.login('xxxx')

// 设置用户属性
sensors.setProfile({...})

// 元素点击
sensors.webClick({ $element_content: 'xxx'})

// 页面浏览
sensors.pageview({ $title: 'xxx' })

// 页面停留
sensors.pageStay({ $title: 'xxx' })

// 行为追踪事件
sensors.track(eventName, {...})

// 如果以上方法，无法满足需求，通过getSensorInstance可获取神策实际的实例
sensors.getSensorInstance()

// 挂载
// // Vue
Vue.prototype.$sensors = sensors
// // React其他
window.ky_sensors = sensors
```

### 文档

- 神策 Web 技术文档: https://manual.sensorsdata.cn/sa/latest/tech_sdk_client_web-7548149.html

- 神策 Web 预置事件和预置属性: https://manual.sensorsdata.cn/sa/latest/tech_sdk_js_preset_properties-27722212.html

- 神策埋点采集方案 Web 端: http://confluence.sf-express.com/pages/viewpage.action?pageId=162588786

/*
 * @Author: dong
 * @LastEditors: dong
 */
import image from '@rollup/plugin-image';
// import scss from 'rollup-plugin-scss';
import postcss from 'rollup-plugin-postcss';
import { babel } from '@rollup/plugin-babel';
export default {
  input: 'src/authLogin.js',
  output: {
    file: 'build/index.js',
    format: 'cjs',
  },
  plugins: [
    image(),
    postcss({
      modules: true,
      use: ['sass'],
    }),
    babel({
      exclude: 'node_modules/**',
      // babelHelpers: 'runtime',
      // exclude: 'node_modules/**',
      // plugins: ['@babel/plugin-transform-runtime', '@babel/preset-typeScript'],
    }),
  ],
};

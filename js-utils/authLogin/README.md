<!--
 * @Author: Jie<PERSON>w
 * @Date: 2021-07-07 10:40:05
 * @LastEditors: dong
 * @LastEditTime: 2022-02-17 15:40:47
 * @Description:
-->

- 安装依赖

```
 npm install @ky/authlogin -S
```

- 初始化

```
const loginIns = new AuthLogin({
    <!-- 控制是否显示短信验证码（只控制显示，不控制ui逻辑比如倒计时等） -->
    showPhoneCode: () => {
      this.setState({
        codeVisible: true,
      });
    },
    appKey: 'FOP-WEB-ZD-DUAL-AUTH',
    appSecret: 'SF@2Q1w4E3U#',
    <!-- 为了保证提示样式跟项目统一，需要注入项目内的message函数，这里建议用modal.info -->
    message: str => {
      Modal.info({
        title: '提示',
        content: (
          <div>
            <p>{str}</p>
          </div>
        ),
      });
    },
  });
```

- 之前的图形验证码需要改为短信验证码

```
<!-- 仅供参考，以项目为准 -->
 {codeVisible ? (
                <Row>
                  <Col span={18}>
                    <FormItem
                      label="短信验证码"
                      name="verifyCode"
                      rules={[{ required: true, message: '请输入验证码！' }]}
                      labelCol={{ md: 8, sm: 24, xs: 24 }}
                      wrapperCol={{ md: 14, sm: 24, xs: 24 }}
                    >
                      <Input
                        style={{ display: 'flex' }}
                        size="large"
                        autoComplete="off"
                        placeholder="验证码"
                      />
                    </FormItem>
                  </Col>
                  <Col span={6}>
                    <Button
                      disabled={!!count}
                      type="link"
                      onClick={() => this.onGetCaptcha()}
                    >
                      {count ? `${count} 秒` : '获取验证码'}
                    </Button>
                  </Col>
                </Row>
              ) : null}

<!-- 调用authlogin的发送短信 -->
    await loginIns.sendVerifyCode(username); //发送成功后自行控制倒计时逻辑
```

- 登录、退出

```
    loginIns.login({
        empPhone: username,
        empPassword: password,
        mobileCode: verifyCode, //非必须，根据页面显示来判断
      });

      loginIns.logout()
```

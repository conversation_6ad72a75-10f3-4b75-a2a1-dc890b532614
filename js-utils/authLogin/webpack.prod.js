/*
 * @Author: dong
 * @LastEditors: dong
 */
const path = require('path');
const webpack = require('webpack');
const { merge } = require('webpack-merge');

const base = require('./webpack.base.js');
// const PrettierPlugin = require('prettier-webpack-plugin');
// const ParallelUglifyPlugin = require('webpack-parallel-uglify-plugin');

module.exports = merge(base, {
  mode: 'none',
  entry: './src/authLogin.js',
  output: {
    filename: 'index.js',
    path: path.resolve(__dirname, 'build'),
    library: {
      type: 'umd',
      name: 'AuthLogin',
    },
    // libraryTarget: 'umd',
  },

  plugins: [
    // new PrettierPlugin(),
    // new webpack.BannerPlugin(banner),
  ],
});

/*
 * @Author: your name
 * @Date: 2021-12-23 11:29:08
 * @LastEditTime: 2022-04-08 14:45:08
 * @LastEditors: dong
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /fop-web-eca-kyutils/js-utils/authLogin/webpack.dev.js
 */
// const path = require('path');
const path = require('path');

const webpack = require('webpack');
const { merge } = require('webpack-merge');

const base = require('./webpack.base.js');
const src = path.resolve(__dirname, './src');
const dist = path.resolve(__dirname, './dist');
module.exports = merge(base, {
  mode: 'development',
  entry: `${src}/index.js`,
  output: {
    path: dist,
    filename: 'js/[name].bundle.js',
    publicPath: '/',
    assetModuleFilename: 'static/[hash][ext][query]',
    environment: {
      arrowFunction: true,
      bigIntLiteral: false,
      const: true,
      destructuring: true,
      dynamicImport: false,
      forOf: true,
    },
  },
  devtool: 'source-map',
  devServer: {
    compress: true,
    historyApiFallback: true,
    // open: true,
    port: 3001,
    // host: '0.0.0.0',
    proxy: {
      '/eosFmsOpssAuth': {
        target: 'http://fop.sit.sf-express.com/',
        changeOrigin: true,
        secure: false,
      },
      '/apis-auth': {
        target: 'http://fop.sit.sf-express.com/',
        changeOrigin: true,
        secure: false,
      },
    },
  },
  plugins: [new webpack.HotModuleReplacementPlugin()],
});

/*
 * @Author: dong
 * @LastEditors: dong
 */
const path = require('path');

const webpack = require('webpack');
// const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');

const babelLoader = {
  loader: 'babel-loader',
  options: {
    presets: ['@babel/preset-env', '@babel/preset-react'],
    plugins: [
      '@babel/plugin-proposal-class-properties',
      '@babel/plugin-syntax-dynamic-import',
      '@babel/plugin-transform-runtime',
    ],
  },
};
const src = path.resolve(__dirname, './src');
const dist = path.resolve(__dirname, './dist');

module.exports = {
  resolve: {
    extensions: ['.mjs', '.js', '.jsx', '.ts', '.tsx', '.json'],
  },
  experiments: {
    // topLevelAwait: true,
    // outputModule: true,
  },
  module: {
    rules: [
      // JavaScript, React
      {
        test: /\.jsx?$/i,
        exclude: /node_modules/,
        use: babelLoader,
      },
      // CSS, SASS
      {
        test: /\.(c|sa|sc)ss$/i,
        use: [
          'style-loader',
          {
            loader: 'css-loader',
            options: {
              importLoaders: 1,
              modules: {
                // getLocalIdent: (context, localIdentName, localName) =>
                //   `auth-login-verify-${localName}`,
                getLocalIdent: (context, localIdentName, localName) =>
                  localName,
              },
            },
          },
          {
            loader: 'postcss-loader',
          },
          'sass-loader',
        ],
      },

      // static files
      {
        test: /\.(jpe?g|png|gif|svg|eot|ttf|woff2|otf?)$/i,
        type: 'asset',
      },
    ],
  },
  plugins: [
    // new CopyWebpackPlugin({
    //   patterns: [
    //     {
    //       from: `${src}/static`,
    //       to: `${dist}/static`,
    //     },
    //   ],
    // }),
    new webpack.ProgressPlugin(),

    new HtmlWebpackPlugin({
      template: `${src}/index.html`,
      filename: 'index.html',
    }),
  ],
};

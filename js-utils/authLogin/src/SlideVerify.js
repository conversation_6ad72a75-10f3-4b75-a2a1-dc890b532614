// // import React from 'react';
import verifyClose from './images/verify-close.png';
import verifyArr from './images/verify-arr.png';
import verifyArrFFF from './images/verify-arr-fff.png';
import style from './auth.scss';

const $ = function(str) {
  if (str.match(/^#/)) {
    return document.getElementById(str.replace(/#/, ''));
  }
  const target = document.getElementsByClassName(str.replace(/\./, ''));
  return target.length ? target[0] : null;
};

const verify = data => {
  const { backgroundImage, sliderImage } = data;
  return `<img alt="" src="${verifyClose}" class="${style['close-verify-box']}"/>
  <p class="${style['verify-title']}">安全验证</p>
  <div class="${style['verify-img']}">
    <img class="${style['bg-img']}"  id="verify-img-bg-img" src="${backgroundImage}" alt=""/>
    <img  class="${style['slider-img']}"  id="verify-img-slider-img" src="${sliderImage}" alt=""/>
  </div>

  <div class="${style['slider-bar']}">
    <div class="${style.slider}">
      <img src="${verifyArr}"  class="${style['slider-img']}" style="display: block" alt=""/>
      <img src="${verifyArrFFF}" alt=""/>
    </div>
    <div class="${style['slider-text']}">向右滑动滑块填充拼图</div>
    <div class="${style['bg-color']}"/>
  </div>
 
  </div>
  <p class="${style['error-text']}" id="verify-img-error-text"></p>
  <div id="slide-loading-container" class="slide-loading-container">
      <div class="bounce-view">
      <div class="bounce bounce1"></div>
      <div class="bounce bounce2"></div>
      <div class="bounce bounce3"></div>
    </div>
  `;
};

export default class SlideVerify {
  constructor() {
    // 生成拖动验证码
    let div;
    let mask;
    if (document.querySelector('#auth-dual-verify-code')) {
      div = document.querySelector('#auth-dual-verify-code');
      mask = document.querySelector('#auth-dual-mask');
    } else {
      div = document.createElement('DIV');
      div.id = 'auth-dual-verify-code';
      div.className = style['auth-dual-verify-code'];
      div.style = 'display:none';
      mask = document.createElement('DIV');
      mask.id = 'auth-dual-mask';
      mask.className = style['auth-dual-mask'];
      mask.style = 'display:none';
    }

    div.innerHTML = verify({
      backgroundImage: '',
      sliderImage: '',
    });
    document.body.appendChild(div);
    document.body.appendChild(mask);
    this.verifyDiv = div;

    this.isMouseDown = false;
    this.originX = 0;
    this.moveX = 0;
    this.isPhoneShow = false;
  }

  updateVerifyImg = data =>
    new Promise((res, rej) => {
      this.resetVerify();
      const { backgroundImage, sliderImage, graphCodeId } = data;
      this.graphCodeId = graphCodeId;
      this.show();
      $('#verify-img-bg-img').setAttribute('src', backgroundImage);
      $('#verify-img-slider-img').setAttribute('src', sliderImage);

      // this.verifyDiv.innerHTML = verify({
      //   backgroundImage,
      //   sliderImage,
      // });

      // $('#auth-dual-mask').style.display = 'block';
      // $(`#auth-dual-verify-code`).style.display = 'block';
      this.bindVerifyevent(res, rej);
      // const h = `${document.documentElement.style.height}px`;
      // document.body.style.cssText = `height: ${h};overflowY: hidden;`;
      // document.documentElement.style.cssText = `height: ${h};overflowY: hidden;`;
    });
  // debugger;

  bindVerifyevent = (resolve, reject) => {
    // 验证码拖拽
    $(`.${style['slider-img']}`).addEventListener(
      'click',
      this.handleDragStart,
      false,
    );

    this.handleDragEndWithRes = e => {
      return this.handleDragEnd(e, resolve);
    };
    $(`.${style['slider-img']}`).addEventListener(
      'mousedown',
      this.handleDragStart,
      false,
    );
    $(`.${style['slider-img']}`).addEventListener(
      'mousemove',
      this.handleDragMove,
      false,
    );
    $(`.${style['slider-img']}`).addEventListener(
      'mouseup',
      this.handleDragEndWithRes,
      false,
    );
    $(`.${style['slider-img']}`).addEventListener(
      'touchstart',
      this.handleDragStart,
    );
    $(`.${style['slider-img']}`).addEventListener(
      'touchmove',
      this.handleDragMove,
    );
    $(`.${style['slider-img']}`).addEventListener(
      'touchend',
      this.handleDragEndWithRes,
    );
    $(`.${style.slider}`).addEventListener(
      'mousedown',
      this.handleDragStart,
      false,
    );
    $(`.${style.slider}`).addEventListener(
      'mousemove',
      this.handleDragMove,
      false,
    );
    $(`.${style.slider}`).addEventListener(
      'mouseup',
      this.handleDragEndWithRes,
      false,
    );

    document.documentElement.addEventListener(
      'mousemove',
      this.handleDragMove,
      false,
    );
    document.documentElement.addEventListener(
      'mouseup',
      this.handleDragEndWithRes,
      false,
    );
    $(`.${style.slider}`).addEventListener(
      'touchstart',
      this.handleDragStart,
      false,
    );
    $(`.${style.slider}`).addEventListener(
      'touchmove',
      this.handleDragMove,
      false,
    );
    $(`.${style.slider}`).addEventListener(
      'touchend',
      this.handleDragEndWithRes,
      false,
    );
    document.documentElement.addEventListener(
      'touchmove',
      this.handleDragMove,
      false,
    );
    document.documentElement.addEventListener(
      'touchend',
      this.handleDragEndWithRes,
      false,
    );

    $(`.${style['close-verify-box']}`).addEventListener(
      'click',
      () => this.hide(reject),
      false,
    );
  };

  resetVerify = () => {
    // $('#auth-dual-mask').style.display = 'none';
    // $(`#auth-dual-verify-code`).style.display = 'none';
    // this.hide();
    $(`.${style['bg-img']}`).setAttribute('src', '');
    $(`.${style['slider-img']}`).setAttribute('src', '');
    $(`.${style['slider-img']}`).style.left = 0;
    $(`.${style.slider}`).style.cssText = `left: 1px; background: #fff; `;
    $(`.${style['slider-text']}`).style.display = 'block';
    $(`.${style['bg-color']}`).style.width = 0;
    document.querySelectorAll(`.${style.slider} img`)[1].style.display = 'none';
    document.querySelectorAll(`.${style.slider} img`)[0].style.display =
      'block';
    // $(`.${style.slider} img`).style.display='block';
    // .eq(0)
    // .show()
    // .siblings('img')
    // .hide();
    // document.body.style.cssText = `height: auto;overflowY: auto;`;
    $(`.${style['slider-img']}`).removeEventListener(
      'mousemove',
      this.handleDragStart,
    );
    $(`.${style['slider-img']}`).removeEventListener(
      'mousemove',
      this.handleDragMove,
    );
    $(`.${style['slider-img']}`).removeEventListener(
      'mouseup',
      this.handleDragEndWithRes,
    );
    $(`.${style['slider-img']}`).removeEventListener(
      'touchstart',
      this.handleDragStart,
    );
    $(`.${style['slider-img']}`).removeEventListener(
      'touchmove',
      this.handleDragMove,
    );
    $(`.${style['slider-img']}`).removeEventListener(
      'touchend',
      this.handleDragEndWithRes,
    );
    $(`.${style.slider}`).removeEventListener(
      'mousedown',
      this.handleDragStart,
    );
    $(`.${style.slider}`).removeEventListener('mousemove', this.handleDragMove);
    $(`.${style.slider}`).removeEventListener(
      'mouseup',
      this.handleDragEndWithRes,
    );
    document.documentElement.removeEventListener(
      'mousemove',
      this.handleDragMove,
    );
    document.documentElement.removeEventListener(
      'mouseup',
      this.handleDragEndWithRes,
    );
    $(`.${style.slider}`).removeEventListener(
      'touchstart',
      this.handleDragStart,
    );
    $(`.${style.slider}`).removeEventListener('touchmove', this.handleDragMove);
    $(`.${style.slider}`).removeEventListener(
      'touchend',
      this.handleDragEndWithRes,
    );
    document.documentElement.removeEventListener(
      'touchmove',
      this.handleDragMove,
    );
    document.documentElement.removeEventListener(
      'touchend',
      this.handleDragEndWithRes,
    );
  };

  handleDragStart = e => {
    e.preventDefault();
    e.stopPropagation();
    this.isMouseDown = true;
    this.originX = e.clientX || e.touches[0].clientX;
    this.closeError();
  };

  handleDragMove = e => {
    if (!this.isMouseDown) return false;
    e.preventDefault();
    e.stopPropagation();
    $(`.${style['slider-text']}`).style.display = 'none';
    $(`.${style.slider}`).style.background = '#2A6CFE';
    document.querySelectorAll(`.${style.slider} img`)[1].style.display =
      'block';
    document.querySelectorAll(`.${style.slider} img`)[0].style.display = 'none';
    // $(`.${style.slider} img`).style.display='block';
    // .eq(1)
    // .show()
    // .siblings('img')
    // .hide();
    const eventX = e.clientX || e.touches[0].clientX;
    this.moveX = eventX - this.originX;
    if (this.moveX <= 0 || this.moveX + 67 > 300) return false;
    $(`.${style['slider-img']}`).style.left = `${this.moveX}px`;
    $(`.${style.slider}`).style.left = `${this.moveX}px`;
    $(`.${style['bg-color']}`).style.width = `${this.moveX}px`;
  };

  handleDragEnd = (e, resolve) => {
    if (!this.isMouseDown) return false;
    e.preventDefault();
    e.stopPropagation();
    this.isMouseDown = false;
    const eventEndX = e.clientX || e.changedTouches[0].clientX;
    if (eventEndX === this.originX) return false;
    // this.resetVerify();
    resolve({
      width: window.parseInt(eventEndX - this.originX), // 后端只接收整形int
      graphCodeId: this.graphCodeId,
    });
    return false;
  };

  show = () => {
    $('#auth-dual-mask').style.display = 'block';
    $(`#auth-dual-verify-code`).style.display = 'block';
  };

  hide = reject => {
    this.resetVerify();
    this.closeError();
    $('#auth-dual-mask').style.display = 'none';
    $(`#auth-dual-verify-code`).style.display = 'none';
    this.hideLoading();
    if (reject) reject('主动关闭');
  };

  showLoading = () => {
    $(`#slide-loading-container`).style.display = 'block';
  };

  hideLoading = () => {
    $(`#slide-loading-container`).style.display = 'none';
  };

  showError = message => {
    $(`#verify-img-error-text`).style.display = 'block';
    $(`#verify-img-error-text`).innerHTML = message;
  };

  closeError = () => {
    $(`#verify-img-error-text`).style.display = 'none';
    $(`#verify-img-error-text`).innerHTML = '';
  };
}

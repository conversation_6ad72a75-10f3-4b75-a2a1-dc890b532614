import '@babel/polyfill';

import axios from 'axios';
import FingerprintJS from '@fingerprintjs/fingerprintjs';

import SlideVerify from './SlideVerify';

const fpPromise = FingerprintJS.load();

// axios.interceptors.headers.common['hg-app-version'] = 'V2.0.0';
export default class Auth {
  constructor(options = {}) {
    const {
      url = '',
      appKey = '',
      appSecret = '',
      showPhoneCode,
      ENV = 'SIT',
      loginTimeout = null,
      message,
    } = options;
    Object.assign(this, {
      url,
      appKey,
      appSecret,
      ENV,
      showPhoneCode,
      loginTimeout,
      message,
    });

    this.slideVerify = new SlideVerify();
    const init = async deviceId => {
      if (!deviceId) {
        showPhoneCode();
        const ck = await this.getCk();
        this.deviceId = ck;
        this.axios = axios.create({
          baseURL: '',
          timeout: 10000,
          headers: { appKey, deviceId: ck, 'hg-app-version': 'V2.0.0' },
        });
      } else {
        this.deviceId = deviceId;
        this.axios = axios.create({
          baseURL: '',
          timeout: 10000,
          headers: { appKey, deviceId, 'hg-app-version': 'V2.0.0' },
        });
      }
      this.axios.defaults.headers.post['Content-Type'] = 'application/json';

      this.axios.defaults.withCredentials = true;

      this.axios.interceptors.response.use(
        res => {
          if (res) {
            if (!res.data.success && res.data.errorCode) {
              return Promise.reject(res.data);
            }
            return res.data.obj;
          }
        },
        error => {
          if (error.status !== 200) {
            if (!error.response || !error.response.errorMessage) {
              this.message(`请求出错！${error.status}`);
            }
          }
          return Promise.reject(error);
        },
      );
    };
    const deviceId = document.cookie
      .split(';')
      .find(item => item.trim().split('=')[0] === 'deviceId');
    this.initPromise = init(deviceId ? deviceId.split('=')[1] : null);
    if (!deviceId) {
      this.nodeviceId = true;
    }
  }

  // 滑动验证码验证
  verifyImg = async () => {
    let getRes;
    try {
      // 获取滑动验证码
      getRes = await this.axios.get(
        `/eosFmsOpssAuth/app/slideCaptcha?time=${new Date().getTime()}`,
      );
    } catch (error) {
      if (error.errorCode) {
        this.message(
          `获取滑动验证码验证失败，请重试！（错误代码：${error.errorCode}）`,
        );
      }
      throw error;
    }

    const { backgroundImage, sliderImage, graphCodeId } = getRes;
    this.graphCodeId = graphCodeId;
    // 拉起滑动验证码界面
    const verifyRes = await this.slideVerify.updateVerifyImg({
      backgroundImage,
      sliderImage,
      graphCodeId,
    });

    const { width } = verifyRes;
    this.slideVerify.showLoading();
    try {
      const { tempTicket } = await this.axios.post(
        '/eosFmsOpssAuth/app/slideCaptcha/verify',
        {
          graphCodeId,
          graphCode: width,
        },
      );
      this.slideVerify.hide();
      return {
        tempTicket,
        graphCodeId,
      };
    } catch (error) {
      this.slideVerify.hideLoading();

      if (error.errorCode) {
        const { errorMessage } = error;
        this.slideVerify.showError(`${errorMessage}，请重试！`);
      }

      const retryResult = await this.verifyImg();
      return retryResult;

      // throw error;
    }
  };

  checkToken = () =>
    new Promise((resolve, reject) => {
      const checkTokenUrl = `${this.url}/apis-auth/login/check_token`;
      return axios({
        url: checkTokenUrl,
        method: 'GET',
      })
        .then(res => {
          resolve(res);
        })
        .catch(err => {
          reject(err);
        });
    });

  login = async data => {
    await this.initPromise;
    const { empPhone, empPassword, mobileCode, type, ...rest } = data;
    const loginData = {
      empPhone,
      empPassword,
      appKey: this.appKey,
      appSecret: this.appSecret,
      deviceId: this.deviceId,
      type,
      ...rest,
    };
    // 微信登陆不需要验证码
    if (type !== 'wechat') {
      // 短信验证码登录
      if (mobileCode) {
        Object.assign(loginData, {
          verificationCode: mobileCode,
          passwordType: 6,
        });
      } else {
        const { tempTicket, graphCodeId } = await this.verifyImg();
        Object.assign(loginData, {
          graphCodeId,
          tempTicket,
          passwordType: 0,
        });
      }
    }

    try {
      const userData = await this.axios.post(
        '/apis-auth/login/auth',
        loginData,
      );
      document.cookie = `deviceId=${this.deviceId}`;
      return userData;
    } catch (error) {
      if (error.errorCode) {
        if (error.errorCode === '10025') {
          this.message(
            '为保证账号安全，本次需要短信验证码登录，请点击页面上的发送验证码获取',
          );
          this.showPhoneCode();
        } else {
          this.message(error.errorMessage);
        }
      }
      throw error;
    }
  };

  sendVerifyCode = async phoneNumber => {
    const { tempTicket, graphCodeId } = await this.verifyImg();

    try {
      await this.axios.post('/eosFmsOpssAuth/app/sms/send', {
        smsCodeType: 0,
        empPhone: phoneNumber,
        graphCodeId,
        tempTicket,
      });
    } catch (error) {
      if (error.errorCode) {
        this.message(error.errorMessage);
      }
      throw error;
    }
  };

  logout = () => {
    document.cookie = 'lastOpTime=;path=/;'; // 退出清空lastOpTime cookie
    const logoutUrl = `${
      this.url
    }/apis-auth/login/logout?time=${new Date().getTime()}`;
    this.axios.get(logoutUrl).catch(error => {
      if (error.errorMessage) {
        this.message(error.errorMessage);
      }
      throw error;
    });
  };

  getCk = () =>
    fpPromise
      .then(fp => fp.get())
      .then(result => {
        // This is the visitor identifier:
        const { visitorId } = result;
        return visitorId;
      });
}

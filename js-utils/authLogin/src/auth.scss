.auth-dual-verify-code {
  position: fixed;
  padding: 35px;
  top: 50%;
  left: 50%;
  margin-left: -160px;
  z-index: 990;
  background: #fff;
  display: none;
  margin-top: -150px;
}
.close-verify-box {
  position: absolute;
  width: 16px;
  height: 16px;
  right: 12px;
  top: 12px;
  cursor: pointer;
}
.verify-img {
  position: relative;
  width: 300px;
  height: 160px;
  img {
    position: absolute;
    left: 0;
    top: 0;
  }
  .bg-img {
    width: 100%;
    height: 100%;
  }
  .slider-img {
    width: 67px;
    height: 100%;
    &:hover {
      cursor: move;
    }
  }
}
.slider-bar {
  position: relative;
  margin-top: 15px;
  width: 300px;
  height: 40px;
  background: #f2f2f2;
  .slider {
    position: absolute;
    width: 38px;
    height: 38px;
    background: #fff;
    left: 1px;
    top: 1px;
    cursor: pointer;
    img {
      display: none;
      width: 18px;
      height: 11px;
      padding: 13px 9px;
      box-sizing: content-box;
    }
  }
  .bg-color {
    position: absolute;
    background: #dfe7ff;
    width: 0;
    height: 100%;
    left: 0;
  }
  .slider-text {
    width: 100%;
    height: 100%;
    line-height: 40px;
    text-align: center;
    color: #333;
    font-size: 14px;
  }
  
}
.verify-title {
  font-size: 14px;
  line-height: 14px;
  color: #b8b8b8;
  padding-bottom: 16px;
  text-align: center;
  margin: 0;
}
.auth-dual-mask {
  width: 100%;
  height: 100%;
  background-color: #000;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0.3;
  z-index: 989;
  display: none;
}


.bounce-view {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.bounce {
  width: 10px;
  height: 10px;
  background-color: #fff;
  border-radius: 50%;
  /* 设置小球动画 */
  animation: loading 1.2s both infinite;
}

@keyframes loading {
  0% {
      transform: scale(0);
  }

  40% {
      transform: scale(1);
  }

  80% {
      transform: scale(0);
  }

  100% {
      transform: scale(0);
  }
}
.bounce {
  /* 设置小球形状的样式几乎和type=1一样，重复利用了 */
  display: inline-block;
}

/* 设置延迟 */
.bounce1 {
  animation-delay: -0.32s;
}

.bounce2 {
  animation-delay: -0.16s;
}

.bounce3 {
  animation-delay: 0s;
}


.slide-loading-container{
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0,0,0,0.5);
  display: none;
}

.error-text{
  color: #ec1313;
  padding-top: 15px;
  margin: 0;
  font-size: 14px;
}

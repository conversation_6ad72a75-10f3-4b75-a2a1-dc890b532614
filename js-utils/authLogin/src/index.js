/*
 * @Author: your name
 * @Date: 2021-12-22 19:08:03
 * @LastEditTime: 2022-04-26 15:47:30
 * @LastEditors: dong
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /fop-web-eca-kyutils/js-utils/authLogin/src/index.js
 */

import React, { useCallback, useMemo, useState } from 'react';
import ReactDom from 'react-dom';
import { Modal } from 'antd';
import Auth from './authLogin.js';
// auth.updateVerifyImg();

const Home = () => {
  const [visible, setVisible] = useState(false);
  const [code, setCode] = useState(null);
  const [phone, setPhone] = useState(null);
  const [password, setPassword] = useState(null);
  const auth = useMemo(() => {
    return new Auth({
      showPhoneCode: () => setVisible(true),
      appKey: 'FOP-WEB-ZD-AUTH',
      appSecret: 'SF@2Q1w4E3U#',
      message: str => {
        Modal.info({
          title: '身份验证',
          content: (
            <div>
              <p>{str}</p>
            </div>
          ),
        });
      },
    });
  }, []);
  const click = useCallback(() => {
    auth.login({
      empPhone: phone,
      empPassword: password,
      mobileCode: code,
    });
  }, [phone, password, code]);
  const clickSend = useCallback(() => {
    auth.sendVerifyCode(phone);
  }, [phone]);
  return (
    <div>
      <input value={phone} onChange={e => setPhone(e.target.value)} />
      <input value={password} onChange={e => setPassword(e.target.value)} />
      {visible ? (
        <div>
          <h1 onClick={clickSend}>点击发送验证码</h1>
          <input value={code} onChange={e => setCode(e.target.value)} />
        </div>
      ) : null}

      <h1 onClick={click}>点击登录</h1>
    </div>
  );
};
ReactDom.render(
  <Home />,

  document.getElementById('app'),
);

{"name": "@ky/authlogin", "version": "1.3.5", "description": "快运auth Login", "type": "module", "scripts": {"build": "rollup -c rollup.config.js", "dev": "webpack serve -c ./webpack.dev.js"}, "publishConfig": {"registry": "http://artifactory.sf-express.com/artifactory/api/npm/npm-sf-local/"}, "main": "build/index.js", "author": "cheng zhong dong", "license": "MIT", "keywords": [], "devDependencies": {"@babel/core": "^7.13.10", "@babel/plugin-proposal-class-properties": "^7.16.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.16.5", "@babel/preset-react": "^7.16.5", "@babel/preset-typeScript": "npm:@babel/preset-typescript@^7.16.7", "@babel/runtime": "^7.17.2", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-image": "^2.1.1", "antd": "^4.18.7", "assert": "^2.0.0", "babel-loader": "^8.2.2", "babel-preset-minify": "^0.5.0", "copy-webpack-plugin": "^7.0.0", "core-js": "^3.21.0", "cross-env": "^7.0.3", "css-loader": "^6.5.1", "expose-loader": "^3.1.0", "html-webpack-plugin": "^5.5.0", "jest": "^24.8.0", "node-sass": "^7.0.1", "postcss-loader": "^6.2.1", "prettier": "^1.18.2", "prettier-webpack-plugin": "^1.2.0", "react": "^17.0.2", "react-dom": "^17.0.2", "rollup": "^2.67.3", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-scss": "^3.0.0", "sass": "^1.45.1", "sass-loader": "^12.4.0", "style-loader": "^3.3.1", "ts-loader": "^9.2.6", "url-loader": "^2.0.0", "util": "^0.12.4", "webpack": "^5.65.0", "webpack-cli": "^4.9.1", "webpack-dev-server": "^4.7.1", "webpack-merge": "^5.8.0", "webpack-parallel-uglify-plugin": "^1.1.2"}, "dependencies": {"@babel/polyfill": "^7.12.1", "@fingerprintjs/fingerprintjs": "^3.3.1", "axios": "^0.21.1"}, "gitHead": "fa945b359212561c41d13f278fbe58cd3769c70e"}